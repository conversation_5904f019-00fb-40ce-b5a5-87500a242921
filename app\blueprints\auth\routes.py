# -*- coding: utf-8 -*-
"""
مسارات المصادقة
"""

from flask import render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from app import db
from app.models.user import User
from app.blueprints.auth import bp
from app.forms.auth import LoginForm, RegistrationForm

@bp.route('/login', methods=['GET', 'POST'])
def login():
    """تسجيل الدخول"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()

        if user is None or not user.check_password(form.password.data):
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
            return redirect(url_for('auth.login'))

        if not user.is_active:
            flash('حسابك غير مفعل. يرجى الاتصال بالمدير', 'error')
            return redirect(url_for('auth.login'))

        login_user(user, remember=form.remember_me.data)
        user.update_last_login()

        next_page = request.args.get('next')
        if not next_page or url_parse(next_page).netloc != '':
            next_page = url_for('dashboard.index')

        flash(f'مرحباً {user.nom_complet}', 'success')
        return redirect(next_page)

    return render_template('auth/login.html', title='تسجيل الدخول', form=form)

@bp.route('/logout')
@login_required
def logout():
    """تسجيل الخروج"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('auth.login'))

@bp.route('/csrf-token')
@login_required
def csrf_token():
    """الحصول على رمز CSRF جديد"""
    from flask import jsonify
    from flask_wtf.csrf import generate_csrf

    return jsonify({
        'csrf_token': generate_csrf()
    })

@bp.route('/register', methods=['GET', 'POST'])
@login_required
def register():
    """تسجيل مستخدم جديد (للمديرين فقط)"""
    if not current_user.has_permission('manage_users'):
        flash('ليس لديك صلاحية لإنشاء مستخدمين جدد', 'error')
        return redirect(url_for('dashboard.index'))

    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            nom_complet=form.nom_complet.data,
            role=form.role.data,
            telephone=form.telephone.data,
            is_active=True
        )
        user.set_password(form.password.data)

        db.session.add(user)
        db.session.commit()

        flash(f'تم إنشاء المستخدم {user.nom_complet} بنجاح', 'success')
        return redirect(url_for('auth.users'))

    return render_template('auth/register.html', title='إنشاء مستخدم جديد', form=form)

@bp.route('/users')
@login_required
def users():
    """قائمة المستخدمين"""
    if not current_user.has_permission('manage_users'):
        flash('ليس لديك صلاحية لعرض المستخدمين', 'error')
        return redirect(url_for('dashboard.index'))

    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('auth/users.html', title='المستخدمين', users=users)

@bp.route('/profile')
@login_required
def profile():
    """الملف الشخصي"""
    return render_template('auth/profile.html', title='الملف الشخصي')

@bp.route('/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """تغيير كلمة المرور"""
    from app.forms.auth import ChangePasswordForm

    form = ChangePasswordForm()
    if form.validate_on_submit():
        if not current_user.check_password(form.old_password.data):
            flash('كلمة المرور الحالية غير صحيحة', 'error')
            return redirect(url_for('auth.change_password'))

        current_user.set_password(form.new_password.data)
        db.session.commit()

        flash('تم تغيير كلمة المرور بنجاح', 'success')
        return redirect(url_for('auth.profile'))

    return render_template('auth/change_password.html', title='تغيير كلمة المرور', form=form)
