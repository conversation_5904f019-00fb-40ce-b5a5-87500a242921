{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bell me-2"></i>
        التنبيهات
        {% if unread_count > 0 %}
        <span class="badge bg-danger ms-2">{{ unread_count }}</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if unread_count > 0 %}
            <form action="{{ url_for('notifications.mark_all_read') }}" method="POST" style="display: inline;">
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-check-double me-1"></i>
                    تحديد الكل كمقروء
                </button>
            </form>
            {% endif %}
            <a href="{{ url_for('notifications.settings') }}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-1"></i>
                الإعدادات
            </a>
        </div>
        <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوحة التحكم
        </a>
    </div>
</div>

<!-- إحصائيات التنبيهات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-primary">
            <div class="stats-number">{{ total_notifications }}</div>
            <div class="stats-label">إجمالي التنبيهات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-warning">
            <div class="stats-number">{{ unread_count }}</div>
            <div class="stats-label">غير مقروءة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-danger">
            <div class="stats-number">{{ urgent_count }}</div>
            <div class="stats-label">عاجلة</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ overdue_count }}</div>
            <div class="stats-label">متأخرة</div>
        </div>
    </div>
</div>

<!-- فلاتر التنبيهات -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="type" class="form-label">نوع التنبيه</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    <option value="audience" {% if filter_type == 'audience' %}selected{% endif %}>جلسات</option>
                    <option value="deadline" {% if filter_type == 'deadline' %}selected{% endif %}>مواعيد نهائية</option>
                    <option value="response" {% if filter_type == 'response' %}selected{% endif %}>ردود مطلوبة</option>
                    <option value="general" {% if filter_type == 'general' %}selected{% endif %}>عامة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="priority" class="form-label">الأولوية</label>
                <select class="form-select" id="priority" name="priority">
                    <option value="">جميع الأولويات</option>
                    <option value="urgent" {% if filter_priority == 'urgent' %}selected{% endif %}>عاجلة</option>
                    <option value="high" {% if filter_priority == 'high' %}selected{% endif %}>عالية</option>
                    <option value="medium" {% if filter_priority == 'medium' %}selected{% endif %}>متوسطة</option>
                    <option value="low" {% if filter_priority == 'low' %}selected{% endif %}>منخفضة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    <option value="unread" {% if filter_status == 'unread' %}selected{% endif %}>غير مقروءة</option>
                    <option value="read" {% if filter_status == 'read' %}selected{% endif %}>مقروءة</option>
                    <option value="overdue" {% if filter_status == 'overdue' %}selected{% endif %}>متأخرة</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        تصفية
                    </button>
                    <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- قائمة التنبيهات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة التنبيهات
        </h5>
    </div>
    <div class="card-body">
        {% if notifications %}
            <div class="notifications-list">
                {% for notification in notifications %}
                <div class="notification-item {{ 'unread' if not notification.is_read else '' }} {{ 'overdue' if notification.is_overdue() else '' }}" 
                     data-notification-id="{{ notification.id }}">
                    <div class="notification-content">
                        <div class="notification-header">
                            <div class="notification-meta">
                                <span class="notification-type">
                                    <i class="fas fa-{{ notification.get_type_icon() }} me-1"></i>
                                    {{ notification.get_type_text() }}
                                </span>
                                <span class="notification-priority badge bg-{{ notification.get_priority_color() }}">
                                    {{ notification.get_priority_text() }}
                                </span>
                                {% if notification.is_overdue() %}
                                <span class="badge bg-danger">متأخر</span>
                                {% endif %}
                                {% if not notification.is_read %}
                                <span class="badge bg-primary">جديد</span>
                                {% endif %}
                            </div>
                            <div class="notification-time">
                                <small class="text-muted">
                                    {{ notification.scheduled_time.strftime('%d/%m/%Y %H:%M') }}
                                </small>
                            </div>
                        </div>
                        
                        <div class="notification-body">
                            <h6 class="notification-title">
                                <a href="{{ url_for('notifications.view', id=notification.id) }}" 
                                   class="text-decoration-none">
                                    {{ notification.title }}
                                </a>
                            </h6>
                            <p class="notification-message">
                                {{ notification.message[:150] }}
                                {% if notification.message|length > 150 %}...{% endif %}
                            </p>
                        </div>
                        
                        <div class="notification-actions">
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('notifications.view', id=notification.id) }}" 
                                   class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if not notification.is_read %}
                                <button type="button" class="btn btn-outline-success mark-read-btn" 
                                        data-notification-id="{{ notification.id }}" title="تحديد كمقروء">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                <form action="{{ url_for('notifications.delete', id=notification.id) }}" 
                                      method="POST" style="display: inline;" 
                                      onsubmit="return confirm('هل أنت متأكد من حذف هذا التنبيه؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية للتنبيهات المرتبطة -->
                    {% if notification.audience %}
                    <div class="notification-related">
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            مرتبط بجلسة: 
                            <a href="{{ url_for('audiences.view', id=notification.audience.id) }}">
                                {{ notification.audience.numero_dossier }}
                            </a>
                        </small>
                    </div>
                    {% elif notification.dossier %}
                    <div class="notification-related">
                        <small class="text-muted">
                            <i class="fas fa-folder me-1"></i>
                            مرتبط بملف: 
                            <a href="{{ url_for('dossiers.view', id=notification.dossier.id) }}">
                                {{ notification.dossier.numero_dossier }}
                            </a>
                        </small>
                    </div>
                    {% elif notification.client %}
                    <div class="notification-related">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>
                            مرتبط بموكل: 
                            <a href="{{ url_for('clients.view', id=notification.client.id) }}">
                                {{ notification.client.nom_complet }}
                            </a>
                        </small>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">لا توجد تنبيهات</h4>
                {% if filter_type or filter_priority or filter_status %}
                <p class="text-muted">لم يتم العثور على تنبيهات تطابق معايير البحث</p>
                <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-primary">
                    عرض جميع التنبيهات
                </a>
                {% else %}
                <p class="text-muted">ستظهر التنبيهات هنا عند إنشائها</p>
                <a href="{{ url_for('notifications.settings') }}" class="btn btn-primary">
                    <i class="fas fa-cog me-1"></i>
                    إعداد التنبيهات
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.notifications-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-item {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    background-color: #fff;
    transition: all 0.3s ease;
}

.notification-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.notification-item.unread {
    border-left: 4px solid #007bff;
    background-color: #f8f9ff;
}

.notification-item.overdue {
    border-left: 4px solid #dc3545;
    background-color: #fff5f5;
}

.notification-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.notification-header {
    display: flex;
    justify-content: between;
    align-items: center;
}

.notification-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.notification-type {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.notification-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.notification-title a {
    color: inherit;
}

.notification-title a:hover {
    color: #007bff;
}

.notification-message {
    margin: 0;
    color: #6c757d;
    line-height: 1.5;
}

.notification-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
}

.notification-related {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
}

.notification-related a {
    color: #007bff;
    text-decoration: none;
}

.notification-related a:hover {
    text-decoration: underline;
}

@media (max-width: 768px) {
    .notification-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .notification-meta {
        flex-wrap: wrap;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تحديد التنبيه كمقروء
document.addEventListener('DOMContentLoaded', function() {
    const markReadButtons = document.querySelectorAll('.mark-read-btn');
    
    markReadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const notificationId = this.dataset.notificationId;
            
            fetch(`/notifications/${notificationId}/mark_read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // إزالة شارة "جديد" وتحديث المظهر
                    const notificationItem = document.querySelector(`[data-notification-id="${notificationId}"]`);
                    notificationItem.classList.remove('unread');
                    
                    // إخفاء زر "تحديد كمقروء"
                    this.style.display = 'none';
                    
                    // تحديث العداد
                    updateNotificationCount();
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('حدث خطأ في تحديث التنبيه');
            });
        });
    });
});

// تحديث عداد التنبيهات
function updateNotificationCount() {
    fetch('/notifications/api/count')
    .then(response => response.json())
    .then(data => {
        // تحديث العداد في الشريط العلوي
        const countBadge = document.querySelector('.navbar .badge');
        if (countBadge) {
            if (data.unread_count > 0) {
                countBadge.textContent = data.unread_count;
                countBadge.style.display = 'inline';
            } else {
                countBadge.style.display = 'none';
            }
        }
    });
}

// تحديث تلقائي كل 30 ثانية
setInterval(updateNotificationCount, 30000);
</script>
{% endblock %}
