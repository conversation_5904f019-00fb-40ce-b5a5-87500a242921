# -*- coding: utf-8 -*-
"""
خدمة استخراج البيانات من موقع المحاكم
"""

import time
import json
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait, Select
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from flask import current_app

class MahakimScraper:
    """خدمة استخراج البيانات من موقع المحاكم"""
    
    def __init__(self):
        self.driver = None
        self.wait = None
        self.base_url = "https://www.mahakim.ma"
        
    def setup_driver(self, headless=True):
        """إعداد متصفح Chrome"""
        try:
            chrome_options = Options()
            
            if headless:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            
            # تعطيل الصور لتسريع التحميل
            prefs = {"profile.managed_default_content_settings.images": 2}
            chrome_options.add_experimental_option("prefs", prefs)
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 30)
            
            current_app.logger.info("تم إعداد متصفح Chrome بنجاح")
            return True
            
        except Exception as e:
            current_app.logger.error(f"خطأ في إعداد المتصفح: {str(e)}")
            return False
    
    def close_driver(self):
        """إغلاق المتصفح"""
        if self.driver:
            try:
                self.driver.quit()
                current_app.logger.info("تم إغلاق المتصفح")
            except Exception as e:
                current_app.logger.error(f"خطأ في إغلاق المتصفح: {str(e)}")
    
    def extract_case_info(self, numero_dossier, tribunal, type_juridiction="ابتدائية"):
        """
        استخراج معلومات القضية من موقع المحاكم
        
        Args:
            numero_dossier (str): رقم الملف
            tribunal (str): اسم المحكمة
            type_juridiction (str): نوع المحكمة
            
        Returns:
            dict: معلومات القضية المستخرجة
        """
        
        if not self.setup_driver():
            return {"success": False, "error": "فشل في إعداد المتصفح"}
        
        try:
            current_app.logger.info(f"بدء استخراج معلومات الملف: {numero_dossier}")
            
            # الذهاب لموقع المحاكم
            self.driver.get(self.base_url)
            time.sleep(3)
            
            # البحث عن نموذج البحث
            search_result = self._fill_search_form(numero_dossier, tribunal, type_juridiction)
            if not search_result["success"]:
                return search_result
            
            # استخراج النتائج
            extraction_result = self._extract_results()
            
            return extraction_result
            
        except Exception as e:
            current_app.logger.error(f"خطأ في استخراج البيانات: {str(e)}")
            return {
                "success": False,
                "error": f"خطأ في استخراج البيانات: {str(e)}"
            }
        
        finally:
            self.close_driver()
    
    def _fill_search_form(self, numero_dossier, tribunal, type_juridiction):
        """ملء نموذج البحث"""
        try:
            # البحث عن حقل رقم الملف
            numero_field_selectors = [
                "input[name='numero']",
                "input[id='numero']",
                "input[name='numero_dossier']",
                "input[id='numero_dossier']",
                "input[placeholder*='رقم']",
                "input[type='text']"
            ]
            
            numero_field = None
            for selector in numero_field_selectors:
                try:
                    numero_field = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not numero_field:
                return {"success": False, "error": "لم يتم العثور على حقل رقم الملف"}
            
            # إدخال رقم الملف
            numero_field.clear()
            numero_field.send_keys(numero_dossier)
            current_app.logger.info(f"تم إدخال رقم الملف: {numero_dossier}")
            
            # البحث عن قائمة المحاكم
            tribunal_selectors = [
                "select[name='tribunal']",
                "select[id='tribunal']",
                "select[name='juridiction']",
                "select[id='juridiction']"
            ]
            
            tribunal_select = None
            for selector in tribunal_selectors:
                try:
                    tribunal_select = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if tribunal_select:
                select = Select(tribunal_select)
                # البحث عن المحكمة في القائمة
                for option in select.options:
                    if tribunal.lower() in option.text.lower():
                        select.select_by_visible_text(option.text)
                        current_app.logger.info(f"تم اختيار المحكمة: {option.text}")
                        break
            
            # البحث عن زر البحث
            search_button_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "button[id*='search']",
                "button[id*='recherche']",
                "button[class*='btn']",
                ".btn-primary",
                ".btn-search"
            ]
            
            search_button = None
            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if not search_button:
                return {"success": False, "error": "لم يتم العثور على زر البحث"}
            
            # النقر على زر البحث
            search_button.click()
            current_app.logger.info("تم النقر على زر البحث")
            
            # انتظار تحميل النتائج
            time.sleep(5)
            
            return {"success": True}
            
        except Exception as e:
            current_app.logger.error(f"خطأ في ملء نموذج البحث: {str(e)}")
            return {"success": False, "error": f"خطأ في ملء نموذج البحث: {str(e)}"}
    
    def _extract_results(self):
        """استخراج النتائج من الصفحة"""
        try:
            # البحث عن جدول النتائج
            table_selectors = [
                "table",
                ".table",
                ".results-table",
                ".data-table",
                "#results",
                ".result-container"
            ]
            
            results_table = None
            for selector in table_selectors:
                try:
                    results_table = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    break
                except TimeoutException:
                    continue
            
            if not results_table:
                return {"success": False, "error": "لم يتم العثور على جدول النتائج"}
            
            # استخراج البيانات من الجدول
            extracted_data = {
                "success": True,
                "data": {
                    "procedures": [],
                    "next_audience": None,
                    "last_decision": None,
                    "case_status": None,
                    "extraction_date": datetime.now().isoformat()
                }
            }
            
            # استخراج الصفوف
            rows = results_table.find_elements(By.TAG_NAME, "tr")
            
            for row in rows[1:]:  # تجاهل رأس الجدول
                cells = row.find_elements(By.TAG_NAME, "td")
                if len(cells) >= 3:
                    procedure_data = {
                        "date": self._clean_text(cells[0].text),
                        "procedure": self._clean_text(cells[1].text),
                        "details": self._clean_text(cells[2].text) if len(cells) > 2 else "",
                        "status": self._clean_text(cells[3].text) if len(cells) > 3 else ""
                    }
                    
                    extracted_data["data"]["procedures"].append(procedure_data)
                    
                    # البحث عن الجلسة القادمة
                    if self._is_future_date(procedure_data["date"]):
                        if not extracted_data["data"]["next_audience"]:
                            extracted_data["data"]["next_audience"] = procedure_data
                    
                    # آخر قرار
                    if "قرار" in procedure_data["procedure"].lower() or "حكم" in procedure_data["procedure"].lower():
                        extracted_data["data"]["last_decision"] = procedure_data
            
            # تحديد حالة القضية
            if extracted_data["data"]["procedures"]:
                last_procedure = extracted_data["data"]["procedures"][-1]
                extracted_data["data"]["case_status"] = last_procedure["procedure"]
            
            current_app.logger.info(f"تم استخراج {len(extracted_data['data']['procedures'])} إجراء")
            
            return extracted_data
            
        except Exception as e:
            current_app.logger.error(f"خطأ في استخراج النتائج: {str(e)}")
            return {"success": False, "error": f"خطأ في استخراج النتائج: {str(e)}"}
    
    def _clean_text(self, text):
        """تنظيف النص المستخرج"""
        if not text:
            return ""
        return text.strip().replace('\n', ' ').replace('\t', ' ')
    
    def _is_future_date(self, date_str):
        """التحقق من أن التاريخ في المستقبل"""
        try:
            # محاولة تحليل التاريخ بصيغ مختلفة
            date_formats = [
                "%d/%m/%Y",
                "%Y-%m-%d",
                "%d-%m-%Y",
                "%d.%m.%Y"
            ]
            
            for fmt in date_formats:
                try:
                    date_obj = datetime.strptime(date_str, fmt)
                    return date_obj.date() > datetime.now().date()
                except ValueError:
                    continue
            
            return False
            
        except Exception:
            return False
    
    def simulate_extraction(self, numero_dossier, tribunal):
        """محاكاة استخراج البيانات للاختبار"""
        current_app.logger.info(f"محاكاة استخراج البيانات للملف: {numero_dossier}")
        
        # بيانات تجريبية
        sample_procedures = [
            {
                "date": "15/01/2025",
                "procedure": "إيداع المقال الافتتاحي",
                "details": "تم إيداع المقال الافتتاحي بالمحكمة",
                "status": "مكتمل"
            },
            {
                "date": "22/01/2025", 
                "procedure": "تبليغ المدعى عليه",
                "details": "تم تبليغ المدعى عليه بالدعوى",
                "status": "مكتمل"
            },
            {
                "date": "05/02/2025",
                "procedure": "جلسة المرافعات",
                "details": "جلسة للمرافعات والمناقشة",
                "status": "مجدولة"
            },
            {
                "date": "12/02/2025",
                "procedure": "النطق بالحكم",
                "details": "جلسة النطق بالحكم",
                "status": "مجدولة"
            }
        ]
        
        return {
            "success": True,
            "data": {
                "procedures": sample_procedures,
                "next_audience": {
                    "date": "05/02/2025",
                    "procedure": "جلسة المرافعات",
                    "details": "جلسة للمرافعات والمناقشة",
                    "status": "مجدولة"
                },
                "last_decision": None,
                "case_status": "في المرافعات",
                "extraction_date": datetime.now().isoformat(),
                "source": "محاكاة تجريبية"
            }
        }
