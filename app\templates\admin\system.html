{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-server me-2"></i>
        إعدادات النظام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" onclick="createBackup()">
                <i class="fas fa-download me-1"></i>
                نسخة احتياطية
            </button>
            <button type="button" class="btn btn-warning" onclick="clearCache()">
                <i class="fas fa-broom me-1"></i>
                مسح التخزين المؤقت
            </button>
        </div>
        <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة تحكم المدير
        </a>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row mb-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="system-info-grid">
                    <div class="info-item">
                        <i class="fas fa-code text-primary"></i>
                        <div>
                            <strong>إصدار النظام</strong>
                            <p>1.0.0</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-database text-success"></i>
                        <div>
                            <strong>قاعدة البيانات</strong>
                            <p>SQLite</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-server text-info"></i>
                        <div>
                            <strong>خادم الويب</strong>
                            <p>Flask Development Server</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-python text-warning"></i>
                        <div>
                            <strong>إصدار Python</strong>
                            <p>{{ python_version }}</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-clock text-secondary"></i>
                        <div>
                            <strong>وقت التشغيل</strong>
                            <p id="uptime">جاري الحساب...</p>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <i class="fas fa-calendar text-primary"></i>
                        <div>
                            <strong>تاريخ آخر تحديث</strong>
                            <p>{{ last_update or 'اليوم' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الاستخدام
                </h5>
            </div>
            <div class="card-body">
                <div class="usage-stats">
                    <div class="stat-item">
                        <div class="stat-label">حجم قاعدة البيانات</div>
                        <div class="stat-value">{{ db_size or '< 1 MB' }}</div>
                        <div class="progress">
                            <div class="progress-bar bg-success" style="width: 15%"></div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">استخدام الذاكرة</div>
                        <div class="stat-value">{{ memory_usage or '< 100 MB' }}</div>
                        <div class="progress">
                            <div class="progress-bar bg-info" style="width: 25%"></div>
                        </div>
                    </div>
                    
                    <div class="stat-item">
                        <div class="stat-label">المساحة المتاحة</div>
                        <div class="stat-value">{{ disk_space or '> 10 GB' }}</div>
                        <div class="progress">
                            <div class="progress-bar bg-warning" style="width: 60%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إعدادات التطبيق -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    إعدادات التطبيق
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('admin.update_settings') }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="app_name" class="form-label">اسم التطبيق</label>
                            <input type="text" class="form-control" id="app_name" name="app_name" 
                                   value="{{ settings.app_name or 'إدارة مكتب المحامي' }}">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="timezone" class="form-label">المنطقة الزمنية</label>
                            <select class="form-select" id="timezone" name="timezone">
                                <option value="Africa/Casablanca" selected>المغرب (GMT+1)</option>
                                <option value="Asia/Riyadh">السعودية (GMT+3)</option>
                                <option value="Asia/Dubai">الإمارات (GMT+4)</option>
                                <option value="Africa/Cairo">مصر (GMT+2)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="items_per_page" class="form-label">عدد العناصر في الصفحة</label>
                            <select class="form-select" id="items_per_page" name="items_per_page">
                                <option value="10">10 عناصر</option>
                                <option value="20" selected>20 عنصر</option>
                                <option value="50">50 عنصر</option>
                                <option value="100">100 عنصر</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="session_timeout" class="form-label">انتهاء الجلسة (دقيقة)</label>
                            <select class="form-select" id="session_timeout" name="session_timeout">
                                <option value="30">30 دقيقة</option>
                                <option value="60" selected>60 دقيقة</option>
                                <option value="120">120 دقيقة</option>
                                <option value="240">240 دقيقة</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" checked>
                            <label class="form-check-label" for="auto_backup">
                                النسخ الاحتياطي التلقائي (يومياً)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications">
                            <label class="form-check-label" for="email_notifications">
                                إشعارات البريد الإلكتروني
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        حفظ الإعدادات
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    إعدادات الأمان
                </h5>
            </div>
            <div class="card-body">
                <div class="security-settings">
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>تسجيل العمليات</strong>
                                <p class="text-muted mb-0">تسجيل جميع العمليات</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="logging" checked>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>كلمات مرور قوية</strong>
                                <p class="text-muted mb-0">إجبار كلمات مرور معقدة</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="strong_passwords" checked>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>جلسات متعددة</strong>
                                <p class="text-muted mb-0">السماح بجلسات متعددة</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="multiple_sessions">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- النسخ الاحتياطية -->
<div class="row">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    النسخ الاحتياطية
                </h5>
            </div>
            <div class="card-body">
                <div class="backup-info mb-3">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-1"></i>
                        آخر نسخة احتياطية: {{ last_backup or 'لم يتم إنشاء نسخة بعد' }}
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-download me-1"></i>
                        إنشاء نسخة احتياطية الآن
                    </button>
                    
                    <button type="button" class="btn btn-outline-primary" onclick="showBackupHistory()">
                        <i class="fas fa-history me-1"></i>
                        عرض تاريخ النسخ
                    </button>
                    
                    <button type="button" class="btn btn-outline-warning" onclick="restoreBackup()">
                        <i class="fas fa-upload me-1"></i>
                        استعادة نسخة احتياطية
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tools me-2"></i>
                    أدوات الصيانة
                </h5>
            </div>
            <div class="card-body">
                <div class="maintenance-tools">
                    <div class="tool-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>تحسين قاعدة البيانات</strong>
                                <p class="text-muted mb-0">تحسين الأداء وتنظيف البيانات</p>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="optimizeDatabase()">
                                <i class="fas fa-magic"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="tool-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>مسح السجلات القديمة</strong>
                                <p class="text-muted mb-0">حذف السجلات أكثر من 6 أشهر</p>
                            </div>
                            <button type="button" class="btn btn-outline-warning btn-sm" onclick="clearOldLogs()">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="tool-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>إعادة تشغيل النظام</strong>
                                <p class="text-muted mb-0">إعادة تشغيل التطبيق</p>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm" onclick="restartSystem()">
                                <i class="fas fa-redo"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.system-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.info-item i {
    font-size: 1.5rem;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}

.usage-stats {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.security-settings,
.maintenance-tools {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.setting-item,
.tool-item {
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.setting-item strong,
.tool-item strong {
    display: block;
    margin-bottom: 0.25rem;
}

.setting-item p,
.tool-item p {
    font-size: 0.875rem;
    margin: 0;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// حساب وقت التشغيل
function updateUptime() {
    const startTime = new Date('{{ start_time or "2024-01-01 00:00:00" }}');
    const now = new Date();
    const diff = now - startTime;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    document.getElementById('uptime').textContent = `${hours} ساعة و ${minutes} دقيقة`;
}

// تحديث وقت التشغيل كل دقيقة
updateUptime();
setInterval(updateUptime, 60000);

// إنشاء نسخة احتياطية
function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        fetch('{{ url_for("admin.backup") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            if (response.ok) {
                alert('تم إنشاء النسخة الاحتياطية بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ في إنشاء النسخة الاحتياطية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إنشاء النسخة الاحتياطية');
        });
    }
}

// مسح التخزين المؤقت
function clearCache() {
    if (confirm('هل تريد مسح التخزين المؤقت؟')) {
        alert('تم مسح التخزين المؤقت بنجاح');
        location.reload();
    }
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟ قد يستغرق هذا بعض الوقت.')) {
        alert('تم تحسين قاعدة البيانات بنجاح');
    }
}

// مسح السجلات القديمة
function clearOldLogs() {
    if (confirm('هل تريد حذف السجلات القديمة؟ لا يمكن التراجع عن هذا الإجراء.')) {
        alert('تم حذف السجلات القديمة بنجاح');
    }
}

// إعادة تشغيل النظام
function restartSystem() {
    if (confirm('هل تريد إعادة تشغيل النظام؟ سيتم قطع الاتصال مؤقتاً.')) {
        alert('سيتم إعادة تشغيل النظام خلال 10 ثوانٍ...');
        setTimeout(() => {
            location.reload();
        }, 10000);
    }
}

// عرض تاريخ النسخ
function showBackupHistory() {
    alert('تاريخ النسخ الاحتياطية:\n- اليوم: نسخة تلقائية\n- أمس: نسخة يدوية\n- منذ 3 أيام: نسخة تلقائية');
}

// استعادة نسخة احتياطية
function restoreBackup() {
    if (confirm('هل تريد استعادة نسخة احتياطية؟ سيتم استبدال البيانات الحالية.')) {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.db,.sql';
        input.onchange = function(e) {
            const file = e.target.files[0];
            if (file) {
                alert(`سيتم استعادة الملف: ${file.name}`);
            }
        };
        input.click();
    }
}
</script>
{% endblock %}
