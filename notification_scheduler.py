#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مجدول التنبيهات التلقائية
يمكن تشغيله كمهمة مجدولة (Cron Job) أو Windows Task Scheduler
"""

import schedule
import time
import logging
from datetime import datetime
from app.utils.notification_service import NotificationService

# إعداد السجلات
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('notifications.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def run_notification_check():
    """تشغيل فحص التنبيهات"""
    
    try:
        logger.info("🔄 بدء فحص التنبيهات التلقائية...")
        
        # تشغيل خدمة التنبيهات
        from app.utils.notification_service import run_notification_service
        results = run_notification_service()
        
        logger.info("📊 نتائج فحص التنبيهات:")
        logger.info(f"   - تنبيهات الجلسات: {results['audience_notifications']}")
        logger.info(f"   - تنبيهات المواعيد: {results['deadline_notifications']}")
        logger.info(f"   - تنبيهات الردود: {results['response_notifications']}")
        logger.info(f"   - التنبيهات المرسلة: {results['sent_notifications']}")
        logger.info(f"   - التنبيهات المحذوفة: {results['cleaned_notifications']}")
        
        logger.info("✅ تم إنجاز فحص التنبيهات بنجاح")
        
    except Exception as e:
        logger.error(f"❌ خطأ في فحص التنبيهات: {str(e)}")

def run_hourly_check():
    """فحص كل ساعة للتنبيهات العاجلة"""
    
    try:
        logger.info("⏰ فحص التنبيهات العاجلة...")
        
        from app import create_app
        from app.utils.notification_service import NotificationService
        
        app = create_app()
        with app.app_context():
            # إرسال التنبيهات المجدولة
            sent_count = NotificationService.send_pending_notifications()
            
            if sent_count > 0:
                logger.info(f"📤 تم إرسال {sent_count} تنبيه")
            else:
                logger.info("📭 لا توجد تنبيهات للإرسال")
                
    except Exception as e:
        logger.error(f"❌ خطأ في الفحص العاجل: {str(e)}")

def setup_scheduler():
    """إعداد المجدول"""
    
    logger.info("🚀 بدء تشغيل مجدول التنبيهات...")
    
    # فحص يومي في الساعة 8:00 صباحاً
    schedule.every().day.at("08:00").do(run_notification_check)
    
    # فحص كل ساعة للتنبيهات العاجلة
    schedule.every().hour.do(run_hourly_check)
    
    # فحص إضافي في المساء
    schedule.every().day.at("18:00").do(run_notification_check)
    
    logger.info("📅 تم إعداد المجدول:")
    logger.info("   - فحص يومي: 8:00 صباحاً و 6:00 مساءً")
    logger.info("   - فحص كل ساعة للتنبيهات العاجلة")

def run_scheduler():
    """تشغيل المجدول"""
    
    setup_scheduler()
    
    logger.info("⚡ المجدول يعمل الآن...")
    logger.info("   للإيقاف: اضغط Ctrl+C")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # فحص كل دقيقة
            
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف المجدول")

def run_once():
    """تشغيل فحص واحد فقط"""
    
    logger.info("🔄 تشغيل فحص واحد للتنبيهات...")
    run_notification_check()

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == '--once':
        # تشغيل مرة واحدة فقط
        run_once()
    else:
        # تشغيل المجدول المستمر
        run_scheduler()
