{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-alt me-2"></i>
        جلسة {{ audience.date_audience.strftime('%d/%m/%Y') }}
        {% if audience.is_important %}
        <span class="badge bg-warning ms-2">مهمة</span>
        {% endif %}
        <span class="badge bg-{{ audience.get_status_color() }} ms-2">{{ audience.statut }}</span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('update') %}
            <a href="{{ url_for('audiences.edit', id=audience.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            {% if audience.statut != 'منعقدة' %}
            <button type="button" class="btn btn-success" onclick="markAsCompleted()">
                <i class="fas fa-check me-1"></i>
                تم الانعقاد
            </button>
            {% endif %}
            {% endif %}
            <button type="button" class="btn btn-info" onclick="printAudience()">
                <i class="fas fa-print me-1"></i>
                طباعة
            </button>
        </div>
        <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات الجلسة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الجلسة
                </h5>
            </div>
            <div class="card-body">
                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-calendar-day text-primary"></i>
                        <div>
                            <strong>تاريخ الجلسة</strong>
                            <p>{{ audience.date_audience.strftime('%A, %d %B %Y') if audience.date_audience else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-clock text-info"></i>
                        <div>
                            <strong>وقت الجلسة</strong>
                            <p>{{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}</p>
                        </div>
                    </div>

                    {% if audience.reference_interne %}
                    <div class="info-item">
                        <i class="fas fa-hashtag text-secondary"></i>
                        <div>
                            <strong>المرجع الداخلي</strong>
                            <p>{{ audience.reference_interne }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <div class="info-item">
                        <i class="fas fa-info-circle text-{{ audience.get_status_color() }}"></i>
                        <div>
                            <strong>حالة الجلسة</strong>
                            <p>
                                <span class="badge bg-{{ audience.get_status_color() }}">
                                    {{ audience.statut }}
                                </span>
                            </p>
                        </div>
                    </div>

                    {% if audience.is_important %}
                    <div class="info-item">
                        <i class="fas fa-star text-warning"></i>
                        <div>
                            <strong>أولوية</strong>
                            <p>جلسة مهمة</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- معلومات التوقيت -->
                    <div class="info-item">
                        <i class="fas fa-hourglass-half text-muted"></i>
                        <div>
                            <strong>التوقيت</strong>
                            <p>
                                {% if audience.is_today() %}
                                <span class="badge bg-danger">اليوم</span>
                                {% elif audience.is_tomorrow() %}
                                <span class="badge bg-warning">غداً</span>
                                {% elif audience.is_upcoming() %}
                                <span class="badge bg-info">خلال {{ audience.days_until() }} أيام</span>
                                {% elif audience.is_past() %}
                                <span class="badge bg-secondary">منتهية</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mt-4 text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    تم الإنشاء: {{ audience.created_at.strftime('%d/%m/%Y %H:%M') if audience.created_at else '' }}
                </div>
            </div>
        </div>

        <!-- معلومات الملف -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    الملف المرتبط
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="file-icon mx-auto mb-2">
                        <i class="fas fa-folder-open fa-2x text-primary"></i>
                    </div>
                    <h6 class="mb-1">
                        <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                            {{ audience.numero_dossier }}
                        </a>
                    </h6>
                    <small class="text-muted">{{ audience.type_dossier }}</small>
                </div>

                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-university text-info"></i>
                        <div>
                            <strong>المحكمة</strong>
                            <p>{{ audience.tribunal }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-user text-success"></i>
                        <div>
                            <strong>الموكل</strong>
                            <p>
                                <a href="{{ url_for('clients.view', id=audience.dossier.client.id) }}" class="text-decoration-none">
                                    {{ audience.client_nom }}
                                </a>
                            </p>
                        </div>
                    </div>

                    {% if audience.dossier.client.telephone %}
                    <div class="info-item">
                        <i class="fas fa-phone text-primary"></i>
                        <div>
                            <strong>الهاتف</strong>
                            <p>{{ audience.dossier.client.telephone }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <div class="text-center mt-3">
                    <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>
                        عرض الملف
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الجلسة -->
    <div class="col-lg-8 mb-4">
        <!-- موضوع الجلسة -->
        {% if audience.demande %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    موضوع الجلسة / الطلب
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-light">
                    {{ audience.demande }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- نتيجة الجلسة -->
        {% if audience.resultat %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>
                    نتيجة الجلسة
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    {{ audience.resultat }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- ملاحظات -->
        {% if audience.notes %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    {{ audience.notes }}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- الجلسات الأخرى لنفس الملف -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    الجلسات الأخرى لنفس الملف
                </h5>
            </div>
            <div class="card-body">
                {% set other_audiences = audience.dossier.audiences|rejectattr('id', 'equalto', audience.id)|list %}
                {% if other_audiences %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>الحالة</th>
                                    <th>الموضوع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for other_audience in other_audiences|sort(attribute='date_audience', reverse=true) %}
                                <tr>
                                    <td>{{ other_audience.date_audience.strftime('%d/%m/%Y') if other_audience.date_audience else '' }}</td>
                                    <td>{{ other_audience.heure_audience.strftime('%H:%M') if other_audience.heure_audience else '' }}</td>
                                    <td>
                                        <span class="badge bg-{{ other_audience.get_status_color() }}">
                                            {{ other_audience.statut }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if other_audience.demande %}
                                        {{ other_audience.demande[:30] }}{% if other_audience.demande|length > 30 %}...{% endif %}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('audiences.view', id=other_audience.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد جلسات أخرى لهذا الملف</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Mark as Completed Modal -->
<div class="modal fade" id="completedModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد انعقاد الجلسة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد تسجيل هذه الجلسة كـ "منعقدة"؟</p>
                <div class="mb-3">
                    <label for="resultat" class="form-label">نتيجة الجلسة (اختياري)</label>
                    <textarea class="form-control" id="resultat" rows="3" placeholder="أدخل نتيجة الجلسة..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmCompleted()">
                    <i class="fas fa-check me-1"></i>
                    تأكيد الانعقاد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.file-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}

@media print {
    .btn-toolbar,
    .card-header,
    .btn,
    .modal {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .alert {
        border: 1px solid #ddd !important;
        background: #f8f9fa !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function markAsCompleted() {
    const modal = new bootstrap.Modal(document.getElementById('completedModal'));
    modal.show();
}

function confirmCompleted() {
    const resultat = document.getElementById('resultat').value;
    
    // إرسال طلب لتحديث حالة الجلسة
    fetch(`{{ url_for('audiences.edit', id=audience.id) }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            'statut': 'منعقدة',
            'resultat': resultat,
            'csrf_token': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        })
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('حدث خطأ في تحديث الجلسة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحديث الجلسة');
    });
    
    // إغلاق النافذة المنبثقة
    bootstrap.Modal.getInstance(document.getElementById('completedModal')).hide();
}

function printAudience() {
    window.print();
}

// تحديث الوقت المتبقي كل دقيقة
setInterval(function() {
    // يمكن إضافة تحديث ديناميكي للوقت المتبقي هنا
}, 60000);
</script>
{% endblock %}
