{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل الموكل: {{ client.nom_complet }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('clients.view', id=client.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-eye me-1"></i>
                عرض الموكل
            </a>
        </div>
        <a href="{{ url_for('clients.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تعديل بيانات الموكل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="nom_complet" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.nom_complet.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- رقم الهوية -->
                        <div class="col-md-6 mb-3">
                            <label for="cin" class="form-label">
                                <i class="fas fa-id-card me-1"></i>
                                رقم الهوية
                            </label>
                            {{ form.cin(class="form-control") }}
                            {% if form.cin.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.cin.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                الهاتف
                            </label>
                            {{ form.telephone(class="form-control") }}
                            {% if form.telephone.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.telephone.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="adresse" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            العنوان
                        </label>
                        {{ form.adresse(class="form-control") }}
                        {% if form.adresse.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.adresse.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <!-- المهنة -->
                        <div class="col-md-6 mb-3">
                            <label for="profession" class="form-label">
                                <i class="fas fa-briefcase me-1"></i>
                                المهنة
                            </label>
                            {{ form.profession(class="form-control") }}
                            {% if form.profession.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.profession.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ الميلاد -->
                        <div class="col-md-6 mb-3">
                            <label for="date_naissance" class="form-label">
                                <i class="fas fa-birthday-cake me-1"></i>
                                تاريخ الميلاد
                            </label>
                            {{ form.date_naissance(class="form-control") }}
                            {% if form.date_naissance.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_naissance.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- الجنسية -->
                    <div class="mb-3">
                        <label for="nationalite" class="form-label">
                            <i class="fas fa-flag me-1"></i>
                            الجنسية
                        </label>
                        {{ form.nationalite(class="form-control") }}
                        {% if form.nationalite.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.nationalite.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('clients.view', id=client.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {% if current_user.has_permission('delete') %}
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-1"></i>
                            حذف الموكل
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الموكل الحالية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الموكل الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-large mx-auto mb-2">
                        {{ client.nom_complet[0] }}
                    </div>
                    <h6 class="mb-1">{{ client.nom_complet }}</h6>
                    {% if client.profession %}
                    <small class="text-muted">{{ client.profession }}</small>
                    {% endif %}
                </div>

                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-calendar text-primary"></i>
                        <div>
                            <strong>تاريخ الإنشاء</strong>
                            <p>{{ client.created_at.strftime('%d/%m/%Y %H:%M') if client.created_at else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-folder text-info"></i>
                        <div>
                            <strong>عدد الملفات</strong>
                            <p>{{ client.dossiers|length }} ملف</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إرشادات التعديل -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات التعديل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> تنبيهات مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة البيانات قبل الحفظ</li>
                        <li>الاسم الكامل مطلوب</li>
                        <li>تحقق من صحة البريد الإلكتروني</li>
                        <li>يمكن إلغاء التعديلات في أي وقت</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير:</h6>
                    <p class="mb-0">حذف الموكل سيؤثر على جميع الملفات المرتبطة به.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموكل <strong>{{ client.nom_complet }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع الملفات المرتبطة
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('clients.delete', id=client.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
