{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        إدارة الوثائق
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('documents.upload') }}" class="btn btn-primary">
                <i class="fas fa-upload me-1"></i>
                رفع وثيقة جديدة
            </a>
            <a href="{{ url_for('documents.categories') }}" class="btn btn-outline-secondary">
                <i class="fas fa-tags me-1"></i>
                إدارة التصنيفات
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-primary">
            <div class="stats-number">{{ total_documents }}</div>
            <div class="stats-label">إجمالي الوثائق</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ categories_count }}</div>
            <div class="stats-label">التصنيفات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-success">
            <div class="stats-number">{{ total_size_formatted }}</div>
            <div class="stats-label">إجمالي الحجم</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-warning">
            <div class="stats-number">{{ documents.total if documents else 0 }}</div>
            <div class="stats-label">نتائج البحث</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ search_form.search_query.label(class="form-label") }}
                {{ search_form.search_query(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ search_form.category_id.label(class="form-label") }}
                {{ search_form.category_id(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.file_type.label(class="form-label") }}
                {{ search_form.file_type(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ search_form.date_from.label(class="form-label") }}
                {{ search_form.date_from(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ search_form.date_to.label(class="form-label") }}
                {{ search_form.date_to(class="form-control") }}
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
        
        {% if request.args %}
        <div class="mt-3">
            <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-times me-1"></i>
                إلغاء الفلاتر
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- تصنيفات سريعة -->
<div class="row mb-4">
    {% for category in categories %}
    <div class="col-md-3 mb-2">
        <a href="{{ url_for('documents.index', category_id=category.id) }}" 
           class="text-decoration-none">
            <div class="category-card bg-{{ category.color }}">
                <i class="{{ category.icon }} fa-2x mb-2"></i>
                <h6>{{ category.name_ar }}</h6>
                <small>{{ category.documents.count() }} وثيقة</small>
            </div>
        </a>
    </div>
    {% endfor %}
</div>

<!-- قائمة الوثائق -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الوثائق
        </h5>
        
        <!-- أدوات الترتيب -->
        <div class="btn-group btn-group-sm">
            <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                    data-bs-toggle="dropdown">
                <i class="fas fa-sort me-1"></i>
                ترتيب
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('documents.index', sort='created_at', order='desc', **request.args) }}">
                    الأحدث أولاً</a></li>
                <li><a class="dropdown-item" href="{{ url_for('documents.index', sort='created_at', order='asc', **request.args) }}">
                    الأقدم أولاً</a></li>
                <li><a class="dropdown-item" href="{{ url_for('documents.index', sort='title', order='asc', **request.args) }}">
                    العنوان (أ-ي)</a></li>
                <li><a class="dropdown-item" href="{{ url_for('documents.index', sort='size', order='desc', **request.args) }}">
                    الحجم (الأكبر أولاً)</a></li>
                <li><a class="dropdown-item" href="{{ url_for('documents.index', sort='category', order='asc', **request.args) }}">
                    التصنيف</a></li>
            </ul>
        </div>
    </div>
    
    <div class="card-body">
        {% if documents.items %}
            <!-- إجراءات مجمعة -->
            <div class="mb-3">
                <form id="bulkActionForm" method="POST" action="{{ url_for('documents.bulk_action') }}">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">إجراء مجمع:</label>
                            <select name="action" class="form-select" required>
                                <option value="">اختر الإجراء</option>
                                <option value="delete">حذف المحدد</option>
                                <option value="change_category">تغيير التصنيف</option>
                                <option value="mark_confidential">تحديد كسرية</option>
                                <option value="unmark_confidential">إلغاء السرية</option>
                                <option value="download_zip">تحميل كملف مضغوط</option>
                            </select>
                        </div>
                        <div class="col-md-3" id="newCategoryDiv" style="display: none;">
                            <label class="form-label">التصنيف الجديد:</label>
                            <select name="new_category_id" class="form-select">
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name_ar }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="hidden" name="selected_documents" id="selectedDocuments">
                            <button type="submit" class="btn btn-warning" id="bulkActionBtn" disabled>
                                <i class="fas fa-cogs me-1"></i>
                                تنفيذ
                            </button>
                        </div>
                        <div class="col-md-3 text-end">
                            <small class="text-muted">
                                <span id="selectedCount">0</span> وثيقة محددة
                            </small>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- جدول الوثائق -->
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th width="60">النوع</th>
                            <th>العنوان</th>
                            <th>التصنيف</th>
                            <th>الحجم</th>
                            <th>تاريخ الرفع</th>
                            <th>الحالة</th>
                            <th width="120">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for document in documents.items %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input document-checkbox" 
                                       value="{{ document.id }}">
                            </td>
                            <td>
                                <i class="{{ document.get_icon_class() }} fa-lg" 
                                   title="{{ document.file_extension }}"></i>
                            </td>
                            <td>
                                <div>
                                    <a href="{{ url_for('documents.view', id=document.id) }}" 
                                       class="text-decoration-none fw-bold">
                                        {{ document.title }}
                                    </a>
                                    {% if document.is_confidential %}
                                    <i class="fas fa-lock text-warning ms-1" title="وثيقة سرية"></i>
                                    {% endif %}
                                    {% if document.is_original %}
                                    <i class="fas fa-star text-success ms-1" title="النسخة الأصلية"></i>
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ document.original_filename }}</small>
                                {% if document.description %}
                                <div><small class="text-muted">{{ document.description[:100] }}{% if document.description|length > 100 %}...{% endif %}</small></div>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ document.category.color }}">
                                    <i class="{{ document.category.icon }} me-1"></i>
                                    {{ document.category.name_ar }}
                                </span>
                            </td>
                            <td>{{ document.file_size_formatted }}</td>
                            <td>
                                <small>{{ document.created_at.strftime('%d/%m/%Y') }}</small>
                                <br>
                                <small class="text-muted">{{ document.uploader.nom_complet }}</small>
                            </td>
                            <td>
                                {% if document.document_date %}
                                <small>{{ document.document_date.strftime('%d/%m/%Y') }}</small>
                                {% else %}
                                <small class="text-muted">غير محدد</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('documents.view', id=document.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('documents.download', id=document.id) }}" 
                                       class="btn btn-outline-success" title="تحميل">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="{{ url_for('documents.edit', id=document.id) }}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form method="POST" action="{{ url_for('documents.delete', id=document.id) }}" 
                                          style="display: inline;" 
                                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الوثيقة؟')">
                                        <button type="submit" class="btn btn-outline-danger" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- التصفح -->
            {% if documents.pages > 1 %}
            <nav aria-label="تصفح الوثائق">
                <ul class="pagination justify-content-center">
                    {% if documents.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('documents.index', page=documents.prev_num, **request.args) }}">السابق</a>
                    </li>
                    {% endif %}
                    
                    {% for page_num in documents.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != documents.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('documents.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if documents.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('documents.index', page=documents.next_num, **request.args) }}">التالي</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-4x text-muted mb-4"></i>
                <h4 class="text-muted">لا توجد وثائق</h4>
                {% if request.args %}
                <p class="text-muted">لم يتم العثور على وثائق تطابق معايير البحث</p>
                <a href="{{ url_for('documents.index') }}" class="btn btn-outline-primary">
                    عرض جميع الوثائق
                </a>
                {% else %}
                <p class="text-muted">ابدأ برفع وثائقك الأولى</p>
                <a href="{{ url_for('documents.upload') }}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>
                    رفع وثيقة جديدة
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.category-card {
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    color: white;
    transition: transform 0.2s;
}

.category-card:hover {
    transform: translateY(-2px);
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.document-checkbox:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// إدارة التحديد المجمع
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const documentCheckboxes = document.querySelectorAll('.document-checkbox');
    const selectedCountSpan = document.getElementById('selectedCount');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const selectedDocumentsInput = document.getElementById('selectedDocuments');
    const actionSelect = document.querySelector('select[name="action"]');
    const newCategoryDiv = document.getElementById('newCategoryDiv');
    
    // تحديد/إلغاء تحديد الكل
    selectAllCheckbox.addEventListener('change', function() {
        documentCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });
    
    // تحديث العداد عند تغيير التحديد
    documentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });
    
    // إظهار/إخفاء حقل التصنيف الجديد
    actionSelect.addEventListener('change', function() {
        if (this.value === 'change_category') {
            newCategoryDiv.style.display = 'block';
        } else {
            newCategoryDiv.style.display = 'none';
        }
    });
    
    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.document-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        selectedCountSpan.textContent = count;
        bulkActionBtn.disabled = count === 0;
        
        // تحديث حقل الوثائق المحددة
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);
        selectedDocumentsInput.value = selectedIds.join(',');
        
        // تحديث حالة "تحديد الكل"
        selectAllCheckbox.indeterminate = count > 0 && count < documentCheckboxes.length;
        selectAllCheckbox.checked = count === documentCheckboxes.length;
    }
    
    // تأكيد الحذف المجمع
    document.getElementById('bulkActionForm').addEventListener('submit', function(e) {
        const action = actionSelect.value;
        const count = document.querySelectorAll('.document-checkbox:checked').length;
        
        if (action === 'delete') {
            if (!confirm(`هل أنت متأكد من حذف ${count} وثيقة؟`)) {
                e.preventDefault();
            }
        }
    });
});
</script>
{% endblock %}
