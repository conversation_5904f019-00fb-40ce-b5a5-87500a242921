#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية للتطبيق
"""

from datetime import datetime, date, timedelta
import random
from app import create_app, db
from app.models.user import User
from app.models.client import Client
from app.models.type_dossier import TypeDossier
from app.models.dossier import Dossier
from app.models.audience import Audience
from app.models.journal_actions import JournalActions

def create_sample_data():
    """إنشاء بيانات تجريبية"""
    app = create_app()
    
    with app.app_context():
        print("🔄 بدء إضافة البيانات التجريبية...")
        
        # إنشاء مستخدمين إضافيين
        create_users()
        
        # إنشاء موكلين تجريبيين
        create_clients()
        
        # إنشاء ملفات تجريبية
        create_dossiers()
        
        # إنشاء جلسات تجريبية
        create_audiences()
        
        # إنشاء إجراءات تجريبية
        create_journal_actions()
        
        db.session.commit()
        print("✅ تم إنشاء البيانات التجريبية بنجاح!")

def create_users():
    """إنشاء مستخدمين تجريبيين"""
    users_data = [
        {
            'username': 'lawyer1',
            'email': '<EMAIL>',
            'nom_complet': 'الأستاذ أحمد المحامي',
            'role': 'lawyer',
            'telephone': '0661234567'
        },
        {
            'username': 'assistant1',
            'email': '<EMAIL>',
            'nom_complet': 'فاطمة المساعدة',
            'role': 'assistant',
            'telephone': '0662345678'
        },
        {
            'username': 'viewer1',
            'email': '<EMAIL>',
            'nom_complet': 'محمد المشاهد',
            'role': 'viewer',
            'telephone': '0663456789'
        }
    ]
    
    for user_data in users_data:
        if not User.query.filter_by(username=user_data['username']).first():
            user = User(**user_data)
            user.set_password('password123')
            user.is_active = True
            db.session.add(user)
            print(f"✅ تم إنشاء المستخدم: {user.nom_complet}")

def create_clients():
    """إنشاء موكلين تجريبيين"""
    # الحصول على أنواع الملفات
    types_dossier = TypeDossier.query.all()
    
    clients_data = [
        {
            'nom_complet': 'محمد بن أحمد الكريم',
            'email': '<EMAIL>',
            'telephone': '0661111111',
            'telephone_2': '0522111111',
            'adresse': 'حي السلام، شارع المقاومة، رقم 15، الرباط',
            'cin': 'AB123456',
            'profession': 'مهندس',
            'situation_familiale': 'متزوج'
        },
        {
            'nom_complet': 'فاطمة الزهراء العلوي',
            'email': '<EMAIL>',
            'telephone': '0662222222',
            'adresse': 'حي الرياض، زنقة الوردة، رقم 28، الدار البيضاء',
            'cin': 'CD789012',
            'profession': 'طبيبة',
            'situation_familiale': 'مطلق'
        },
        {
            'nom_complet': 'عبد الرحمن التازي',
            'email': '<EMAIL>',
            'telephone': '0663333333',
            'telephone_2': '0523333333',
            'adresse': 'المدينة القديمة، درب السقايين، رقم 7، فاس',
            'cin': 'EF345678',
            'profession': 'تاجر',
            'situation_familiale': 'أعزب'
        },
        {
            'nom_complet': 'خديجة الإدريسي',
            'email': '<EMAIL>',
            'telephone': '0664444444',
            'adresse': 'حي الأندلس، شارع الحسن الثاني، رقم 42، مراكش',
            'cin': 'GH901234',
            'profession': 'أستاذة',
            'situation_familiale': 'متزوج'
        },
        {
            'nom_complet': 'يوسف البركاني',
            'email': '<EMAIL>',
            'telephone': '0665555555',
            'adresse': 'حي المحيط، زنقة الأطلس، رقم 33، أكادير',
            'cin': 'IJ567890',
            'profession': 'محاسب',
            'situation_familiale': 'أرمل'
        }
    ]
    
    for i, client_data in enumerate(clients_data):
        if not Client.query.filter_by(cin=client_data['cin']).first():
            client_data['type_dossier_id'] = types_dossier[i % len(types_dossier)].id
            client = Client(**client_data)
            db.session.add(client)
            print(f"✅ تم إنشاء الموكل: {client.nom_complet}")

def create_dossiers():
    """إنشاء ملفات تجريبية"""
    clients = Client.query.all()
    types_dossier = TypeDossier.query.all()
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    situations = ['في الجلسات', 'في انتظار التعيين', 'مداولة', 'تأمل']
    
    for i, client in enumerate(clients):
        # إنشاء 1-3 ملفات لكل موكل
        num_dossiers = random.randint(1, 3)
        
        for j in range(num_dossiers):
            dossier_data = {
                'numero_affaire': f"2024/{random.randint(1000, 9999)}/{random.randint(10, 99)}",
                'tribunal': random.choice(tribunaux),
                'type_dossier_id': random.choice(types_dossier).id,
                'client_id': client.id,
                'situation': random.choice(situations),
                'date_ouverture': date.today() - timedelta(days=random.randint(1, 365)),
                'adversaire': f"الطرف المقابل {i+1}-{j+1}",
                'objet': f"موضوع الملف رقم {i+1}-{j+1} - قضية مدنية/تجارية",
                'montant_litige': random.randint(10000, 500000),
                'honoraires': random.randint(5000, 50000),
                'is_urgent': random.choice([True, False]),
                'notes': f"ملاحظات خاصة بالملف {i+1}-{j+1}"
            }
            
            if not Dossier.query.filter_by(numero_affaire=dossier_data['numero_affaire']).first():
                dossier = Dossier(**dossier_data)
                db.session.add(dossier)
                print(f"✅ تم إنشاء الملف: {dossier.numero_affaire}")

def create_audiences():
    """إنشاء جلسات تجريبية"""
    dossiers = Dossier.query.all()
    statuts = ['مبرمجة', 'منعقدة', 'مؤجلة']
    
    for dossier in dossiers:
        # إنشاء 1-4 جلسات لكل ملف
        num_audiences = random.randint(1, 4)
        
        for i in range(num_audiences):
            # تواريخ متنوعة (ماضية وقادمة)
            if i == 0:
                # جلسة قادمة
                date_audience = date.today() + timedelta(days=random.randint(1, 30))
                statut = 'مبرمجة'
            else:
                # جلسات ماضية
                date_audience = date.today() - timedelta(days=random.randint(1, 180))
                statut = random.choice(['منعقدة', 'مؤجلة'])
            
            audience_data = {
                'dossier_id': dossier.id,
                'date_audience': date_audience,
                'heure_audience': datetime.strptime(f"{random.randint(9, 16)}:00", '%H:%M').time(),
                'client_nom': dossier.client.nom_complet,
                'numero_dossier': dossier.numero_affaire,
                'tribunal': dossier.tribunal,
                'type_dossier': dossier.type_dossier_rel.nom,
                'demande': f"طلب الجلسة رقم {i+1} للملف {dossier.numero_affaire}",
                'resultat': f"نتيجة الجلسة رقم {i+1}" if statut == 'منعقدة' else None,
                'statut': statut,
                'is_important': random.choice([True, False]),
                'notes': f"ملاحظات الجلسة رقم {i+1}"
            }
            
            audience = Audience(**audience_data)
            db.session.add(audience)
        
        print(f"✅ تم إنشاء {num_audiences} جلسة للملف: {dossier.numero_affaire}")

def create_journal_actions():
    """إنشاء إجراءات تجريبية"""
    dossiers = Dossier.query.all()
    procedures = [
        'جلسة عادية',
        'جلسة استعجالية', 
        'تبليغ حكم',
        'إيداع مذكرة',
        'طلب تأجيل',
        'تعيين خبير'
    ]
    
    decisions = [
        'تأجيل الملف للجلسة القادمة',
        'الحكم في الموضوع',
        'تعيين خبير لفحص الملف',
        'طلب مذكرات إضافية',
        'رفض الطلب',
        'قبول الطلب جزئياً'
    ]
    
    for dossier in dossiers:
        # إنشاء 1-3 إجراءات لكل ملف
        num_actions = random.randint(1, 3)
        
        for i in range(num_actions):
            action_data = {
                'dossier_id': dossier.id,
                'tribunal': dossier.tribunal,
                'numero_affaire': dossier.numero_affaire,
                'date_extraction': datetime.utcnow() - timedelta(days=random.randint(1, 30)),
                'type_procedure': random.choice(procedures),
                'decision': random.choice(decisions),
                'date_decision': date.today() - timedelta(days=random.randint(1, 60)),
                'prochaine_audience': date.today() + timedelta(days=random.randint(7, 45)) if random.choice([True, False]) else None,
                'juge': f"القاضي {random.choice(['محمد الأمين', 'فاطمة الزهراء', 'عبد الرحمن', 'خديجة'])}",
                'greffier': f"كاتب الضبط {random.choice(['أحمد', 'سعاد', 'يوسف', 'مريم'])}",
                'extraction_status': 'success',
                'raw_data': f'{{"extracted_at": "{datetime.utcnow().isoformat()}", "source": "mahakim.ma"}}'
            }
            
            action = JournalActions(**action_data)
            db.session.add(action)
        
        print(f"✅ تم إنشاء {num_actions} إجراء للملف: {dossier.numero_affaire}")

if __name__ == '__main__':
    create_sample_data()
