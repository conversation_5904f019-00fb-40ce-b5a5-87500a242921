{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.create_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-1"></i>
                إضافة مستخدم جديد
            </a>
        </div>
        <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة تحكم المدير
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="ابحث بالاسم أو اسم المستخدم أو البريد الإلكتروني">
            </div>
            <div class="col-md-3">
                <label for="role" class="form-label">الدور</label>
                <select class="form-select" id="role" name="role">
                    <option value="">جميع الأدوار</option>
                    {% for role in roles %}
                    <option value="{{ role }}" {% if role_filter == role %}selected{% endif %}>
                        {% if role == 'admin' %}مدير
                        {% elif role == 'lawyer' %}محامي
                        {% elif role == 'assistant' %}مساعد
                        {% else %}مشاهد{% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الحالات</option>
                    {% for status in statuses %}
                    <option value="{{ status }}" {% if status_filter == status %}selected{% endif %}>
                        {% if status == 'active' %}نشط{% else %}غير نشط{% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المستخدمين ({{ users.total }} مستخدم)
        </h5>
    </div>
    <div class="card-body">
        {% if users.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المستخدم</th>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users.items %}
                        <tr class="{{ 'table-light' if not user.is_active else '' }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar me-3">
                                        {{ user.nom_complet[0] }}
                                    </div>
                                    <div>
                                        <strong>{{ user.nom_complet }}</strong>
                                        {% if user.id == current_user.id %}
                                        <span class="badge bg-primary ms-1">أنت</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <code>{{ user.username }}</code>
                            </td>
                            <td>
                                {% if user.email %}
                                {{ user.email }}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ 'primary' if user.role == 'admin' else 'success' if user.role == 'lawyer' else 'info' if user.role == 'assistant' else 'secondary' }}">
                                    {% if user.role == 'admin' %}مدير
                                    {% elif user.role == 'lawyer' %}محامي
                                    {% elif user.role == 'assistant' %}مساعد
                                    {% else %}مشاهد{% endif %}
                                </span>
                            </td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">نشط</span>
                                {% else %}
                                <span class="badge bg-danger">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ user.created_at.strftime('%d/%m/%Y') if user.created_at else 'غير محدد' }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('admin.edit_user', id=user.id) }}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    {% if user.id != current_user.id %}
                                    <button type="button" class="btn btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                            onclick="toggleUserStatus({{ user.id }}, '{{ user.nom_complet }}', {{ user.is_active|lower }})" 
                                            title="{{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}">
                                        <i class="fas fa-{{ 'user-slash' if user.is_active else 'user-check' }}"></i>
                                    </button>
                                    {% endif %}
                                    
                                    <button type="button" class="btn btn-outline-info" 
                                            onclick="resetPassword({{ user.id }}, '{{ user.nom_complet }}')" 
                                            title="إعادة تعيين كلمة المرور">
                                        <i class="fas fa-key"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if users.pages > 1 %}
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if users.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, search=search, role=role_filter, status=status_filter) }}">
                            السابق
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != users.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=page_num, search=search, role=role_filter, status=status_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, search=search, role=role_filter, status=status_filter) }}">
                            التالي
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد مستخدمين</h5>
                {% if search or role_filter or status_filter %}
                <p class="text-muted">لم يتم العثور على نتائج تطابق معايير البحث</p>
                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                    عرض جميع المستخدمين
                </a>
                {% else %}
                <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                <a href="{{ url_for('admin.create_user') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus me-1"></i>
                    إضافة مستخدم جديد
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="toggleStatusTitle">تأكيد العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="toggleStatusMessage"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="toggleStatusForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn" id="toggleStatusBtn">تأكيد</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="resetPasswordMessage"></p>
                <div class="mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" 
                           placeholder="أدخل كلمة المرور الجديدة" required minlength="6">
                    <small class="form-text text-muted">يجب أن تكون 6 أحرف على الأقل</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="resetPasswordForm" method="POST" style="display: inline;">
                    <input type="hidden" name="new_password" id="hidden_new_password">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-1"></i>
                        إعادة تعيين
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.table-light {
    opacity: 0.7;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus(userId, userName, isActive) {
    const action = isActive ? 'إلغاء تفعيل' : 'تفعيل';
    const btnClass = isActive ? 'btn-danger' : 'btn-success';
    
    document.getElementById('toggleStatusTitle').textContent = `تأكيد ${action} المستخدم`;
    document.getElementById('toggleStatusMessage').textContent = `هل أنت متأكد من ${action} المستخدم "${userName}"؟`;
    document.getElementById('toggleStatusForm').action = `/admin/users/${userId}/toggle_status`;
    document.getElementById('toggleStatusBtn').className = `btn ${btnClass}`;
    document.getElementById('toggleStatusBtn').textContent = action;
    
    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}

function resetPassword(userId, userName) {
    document.getElementById('resetPasswordMessage').textContent = `إعادة تعيين كلمة مرور المستخدم "${userName}"`;
    document.getElementById('resetPasswordForm').action = `/admin/users/${userId}/reset_password`;
    document.getElementById('new_password').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

// نسخ كلمة المرور إلى الحقل المخفي عند الإرسال
document.getElementById('resetPasswordForm').addEventListener('submit', function() {
    const password = document.getElementById('new_password').value;
    document.getElementById('hidden_new_password').value = password;
});
</script>
{% endblock %}
