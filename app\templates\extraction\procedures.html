{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-download me-2"></i>
        الإجراءات المستخرجة - {{ dossier.numero_affaire }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" onclick="extractNew()">
                <i class="fas fa-download me-1"></i>
                استخراج جديد
            </button>
            <a href="{{ url_for('extraction.extraction_history', dossier_id=dossier.id) }}" 
               class="btn btn-outline-info">
                <i class="fas fa-history me-1"></i>
                تاريخ الاستخراج
            </a>
        </div>
        <a href="{{ url_for('dossiers.view', id=dossier.id) }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للملف
        </a>
    </div>
</div>

<!-- معلومات الملف -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-folder me-2"></i>
                    معلومات الملف
                </h6>
                <div class="row">
                    <div class="col-sm-6">
                        <strong>رقم الملف:</strong><br>
                        <span class="text-primary">{{ dossier.numero_affaire }}</span>
                    </div>
                    <div class="col-sm-6">
                        <strong>المحكمة:</strong><br>
                        {{ dossier.tribunal or 'غير محدد' }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-sm-6">
                        <strong>الموكل:</strong><br>
                        {{ dossier.client.nom_complet if dossier.client else 'غير محدد' }}
                    </div>
                    <div class="col-sm-6">
                        <strong>الحالة:</strong><br>
                        <span class="badge bg-{{ dossier.get_status_color() }}">
                            {{ dossier.situation }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات الاستخراج
                </h6>
                {% set latest_extraction = dossier.extraction_logs.order_by(dossier.extraction_logs.created_at.desc()).first() %}
                <div class="row">
                    <div class="col-sm-6">
                        <strong>آخر استخراج:</strong><br>
                        {% if latest_extraction %}
                            <span class="text-{{ 'success' if latest_extraction.success else 'danger' }}">
                                {{ latest_extraction.created_at.strftime('%d/%m/%Y %H:%M') }}
                                <i class="fas fa-{{ 'check' if latest_extraction.success else 'times' }} ms-1"></i>
                            </span>
                        {% else %}
                            <span class="text-muted">لم يتم</span>
                        {% endif %}
                    </div>
                    <div class="col-sm-6">
                        <strong>عدد الإجراءات:</strong><br>
                        <span class="text-primary">{{ procedures|length }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات المستخرجة -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list-alt me-2"></i>
            الإجراءات المستخرجة ({{ procedures|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if procedures %}
            <div class="timeline">
                {% for procedure in procedures %}
                <div class="timeline-item">
                    <div class="timeline-marker bg-{{ procedure.status_color }}">
                        <i class="{{ procedure.type_icon }}"></i>
                    </div>
                    <div class="timeline-content">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="timeline-title">
                                    {{ procedure.procedure_type }}
                                    {% if procedure.is_future %}
                                    <span class="badge bg-warning ms-2">قادم</span>
                                    {% endif %}
                                    {% if procedure.is_audience %}
                                    <span class="badge bg-info ms-2">جلسة</span>
                                    {% endif %}
                                    {% if procedure.is_decision %}
                                    <span class="badge bg-success ms-2">قرار</span>
                                    {% endif %}
                                </h6>
                                {% if procedure.procedure_details %}
                                <p class="timeline-text">{{ procedure.procedure_details }}</p>
                                {% endif %}
                                <div class="timeline-meta">
                                    <span class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ procedure.formatted_date }}
                                    </span>
                                    {% if procedure.procedure_status %}
                                    <span class="badge bg-{{ procedure.status_color }} ms-2">
                                        {{ procedure.procedure_status }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                            <small class="text-muted">
                                #{{ procedure.sequence_number }}
                            </small>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد إجراءات مستخرجة</h5>
                <p class="text-muted">لم يتم استخراج أي إجراءات لهذا الملف بعد</p>
                <button type="button" class="btn btn-success" onclick="extractNew()">
                    <i class="fas fa-download me-1"></i>
                    استخراج الإجراءات الآن
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- مودال الاستخراج -->
<div class="modal fade" id="extractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استخراج إجراءات جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>سيتم استخراج آخر الإجراءات والقرارات من موقع المحاكم للملف:</p>
                <div class="alert alert-info">
                    <strong>{{ dossier.numero_affaire }}</strong> - {{ dossier.tribunal }}
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="forceRealExtraction">
                    <label class="form-check-label" for="forceRealExtraction">
                        استخراج حقيقي (تجاهل وضع المحاكاة)
                    </label>
                </div>
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status" id="extractSpinner" style="display: none;">
                        <span class="visually-hidden">جاري الاستخراج...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="extractBtn" onclick="executeExtraction()">
                    <i class="fas fa-download me-1"></i>
                    بدء الاستخراج
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -3rem;
    top: 0;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border-left: 4px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.timeline-title {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.timeline-text {
    margin-bottom: 0.75rem;
    color: #6c757d;
    line-height: 1.5;
}

.timeline-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.25rem;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #dee2e6, transparent);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function extractNew() {
    const modal = new bootstrap.Modal(document.getElementById('extractModal'));
    modal.show();
}

function executeExtraction() {
    const forceReal = document.getElementById('forceRealExtraction').checked;
    const spinner = document.getElementById('extractSpinner');
    const btn = document.getElementById('extractBtn');
    
    // إظهار spinner وتعطيل الزر
    spinner.style.display = 'block';
    btn.disabled = true;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستخراج...';
    
    // إرسال طلب الاستخراج
    fetch(`/extraction/api/extract/{{ dossier.id }}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            force_real: forceReal
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إغلاق المودال وإعادة تحميل الصفحة
            const modal = bootstrap.Modal.getInstance(document.getElementById('extractModal'));
            modal.hide();
            
            // إظهار رسالة نجاح
            showAlert('success', `تم الاستخراج بنجاح! تم العثور على ${data.data.procedures.length} إجراء`);
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showAlert('danger', `فشل في الاستخراج: ${data.error}`);
        }
    })
    .catch(error => {
        showAlert('danger', `خطأ في الاتصال: ${error.message}`);
    })
    .finally(() => {
        // إخفاء spinner وتفعيل الزر
        spinner.style.display = 'none';
        btn.disabled = false;
        btn.innerHTML = '<i class="fas fa-download me-1"></i>بدء الاستخراج';
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // إدراج التنبيه في أعلى الصفحة
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
