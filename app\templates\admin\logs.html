{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-file-alt me-2"></i>
        سجلات النظام
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-warning" onclick="clearLogs()">
                <i class="fas fa-trash me-1"></i>
                مسح السجلات
            </button>
            <button type="button" class="btn btn-info" onclick="exportLogs()">
                <i class="fas fa-download me-1"></i>
                تصدير السجلات
            </button>
        </div>
        <a href="{{ url_for('admin.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            لوحة تحكم المدير
        </a>
    </div>
</div>

<!-- فلاتر السجلات -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="level" class="form-label">مستوى السجل</label>
                <select class="form-select" id="level" name="level">
                    <option value="">جميع المستويات</option>
                    <option value="INFO" {% if level_filter == 'INFO' %}selected{% endif %}>معلومات</option>
                    <option value="WARNING" {% if level_filter == 'WARNING' %}selected{% endif %}>تحذير</option>
                    <option value="ERROR" {% if level_filter == 'ERROR' %}selected{% endif %}>خطأ</option>
                    <option value="CRITICAL" {% if level_filter == 'CRITICAL' %}selected{% endif %}>خطأ حرج</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="user" class="form-label">المستخدم</label>
                <select class="form-select" id="user" name="user">
                    <option value="">جميع المستخدمين</option>
                    {% for user in users %}
                    <option value="{{ user.id }}" {% if user_filter == user.id|string %}selected{% endif %}>
                        {{ user.nom_complet }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
            </div>
            <div class="col-12">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-1"></i>
                    تصفية
                </button>
                <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    إلغاء الفلتر
                </a>
                <button type="button" class="btn btn-outline-info" onclick="autoRefresh()">
                    <i class="fas fa-sync me-1"></i>
                    تحديث تلقائي
                </button>
            </div>
        </form>
    </div>
</div>

<!-- إحصائيات السجلات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ total_logs }}</div>
            <div class="stats-label">إجمالي السجلات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-success">
            <div class="stats-number">{{ info_logs }}</div>
            <div class="stats-label">سجلات المعلومات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-warning">
            <div class="stats-number">{{ warning_logs }}</div>
            <div class="stats-label">سجلات التحذير</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-danger">
            <div class="stats-number">{{ error_logs }}</div>
            <div class="stats-label">سجلات الأخطاء</div>
        </div>
    </div>
</div>

<!-- جدول السجلات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            سجلات النظام
            <span class="badge bg-secondary ms-2">{{ logs|length }} سجل</span>
        </h5>
    </div>
    <div class="card-body">
        {% if logs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الوقت</th>
                            <th>المستوى</th>
                            <th>المستخدم</th>
                            <th>العملية</th>
                            <th>التفاصيل</th>
                            <th>عنوان IP</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr class="log-row log-{{ log.level.lower() }}">
                            <td>
                                <div class="timestamp">
                                    <strong>{{ log.timestamp.strftime('%d/%m/%Y') if log.timestamp else 'اليوم' }}</strong>
                                    <br>
                                    <small class="text-muted">{{ log.timestamp.strftime('%H:%M:%S') if log.timestamp else '00:00:00' }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if log.level == 'INFO' else 'warning' if log.level == 'WARNING' else 'danger' if log.level == 'ERROR' else 'dark' }}">
                                    {% if log.level == 'INFO' %}معلومات
                                    {% elif log.level == 'WARNING' %}تحذير
                                    {% elif log.level == 'ERROR' %}خطأ
                                    {% elif log.level == 'CRITICAL' %}خطأ حرج
                                    {% else %}{{ log.level }}{% endif %}
                                </span>
                            </td>
                            <td>
                                {% if log.user %}
                                <div class="d-flex align-items-center">
                                    <div class="user-avatar-sm me-2">
                                        {{ log.user[0] }}
                                    </div>
                                    <div>
                                        <strong>{{ log.user }}</strong>
                                        <br>
                                        <small class="text-muted">{{ log.role or 'مستخدم' }}</small>
                                    </div>
                                </div>
                                {% else %}
                                <span class="text-muted">النظام</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="operation-badge">
                                    <i class="fas fa-{{ 'plus' if 'إضافة' in log.action else 'edit' if 'تعديل' in log.action else 'trash' if 'حذف' in log.action else 'eye' if 'عرض' in log.action else 'sign-in-alt' if 'دخول' in log.action else 'sign-out-alt' if 'خروج' in log.action else 'cog' }} me-1"></i>
                                    {{ log.action or 'عملية غير محددة' }}
                                </span>
                            </td>
                            <td>
                                <div class="log-details">
                                    {{ log.details or 'لا توجد تفاصيل' }}
                                    {% if log.target %}
                                    <br><small class="text-muted">الهدف: {{ log.target }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <code class="small">{{ log.ip_address or '127.0.0.1' }}</code>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد سجلات</h5>
                {% if level_filter or user_filter or date_from or date_to %}
                <p class="text-muted">لم يتم العثور على سجلات تطابق معايير البحث</p>
                <a href="{{ url_for('admin.logs') }}" class="btn btn-outline-primary">
                    عرض جميع السجلات
                </a>
                {% else %}
                <p class="text-muted">لم يتم تسجيل أي عمليات بعد</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    نشاط اليوم
                </h5>
            </div>
            <div class="card-body">
                <div class="activity-summary">
                    <div class="activity-item">
                        <i class="fas fa-sign-in-alt text-success"></i>
                        <span>{{ today_logins or 0 }} عملية دخول</span>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-plus text-primary"></i>
                        <span>{{ today_creates or 0 }} عملية إضافة</span>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-edit text-warning"></i>
                        <span>{{ today_updates or 0 }} عملية تعديل</span>
                    </div>
                    <div class="activity-item">
                        <i class="fas fa-trash text-danger"></i>
                        <span>{{ today_deletes or 0 }} عملية حذف</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    المستخدمين النشطين
                </h5>
            </div>
            <div class="card-body">
                <div class="active-users">
                    {% for user in active_users[:5] %}
                    <div class="user-activity">
                        <div class="user-avatar-sm">{{ user.name[0] }}</div>
                        <div class="user-info">
                            <strong>{{ user.name }}</strong>
                            <small class="text-muted">آخر نشاط: {{ user.last_activity or 'الآن' }}</small>
                        </div>
                        <span class="badge bg-success">نشط</span>
                    </div>
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-user-slash fa-2x mb-2"></i>
                        <p>لا يوجد مستخدمين نشطين حالياً</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.log-row {
    border-left: 4px solid transparent;
}

.log-row.log-info {
    border-left-color: #17a2b8;
}

.log-row.log-warning {
    border-left-color: #ffc107;
}

.log-row.log-error {
    border-left-color: #dc3545;
}

.log-row.log-critical {
    border-left-color: #6f42c1;
}

.user-avatar-sm {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.8rem;
}

.timestamp {
    white-space: nowrap;
}

.operation-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    font-size: 0.875rem;
}

.log-details {
    max-width: 300px;
    word-wrap: break-word;
}

.activity-summary {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.activity-item i {
    width: 20px;
    text-align: center;
}

.active-users {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.user-activity {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.user-info {
    flex: 1;
}

.user-info strong {
    display: block;
}

.user-info small {
    font-size: 0.75rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let autoRefreshInterval;

// تحديث تلقائي
function autoRefresh() {
    const btn = event.target;
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        btn.innerHTML = '<i class="fas fa-sync me-1"></i> تحديث تلقائي';
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-info');
    } else {
        autoRefreshInterval = setInterval(() => {
            location.reload();
        }, 30000); // كل 30 ثانية
        btn.innerHTML = '<i class="fas fa-pause me-1"></i> إيقاف التحديث';
        btn.classList.remove('btn-outline-info');
        btn.classList.add('btn-success');
    }
}

// مسح السجلات
function clearLogs() {
    if (confirm('هل أنت متأكد من مسح جميع السجلات؟ لا يمكن التراجع عن هذا الإجراء.')) {
        fetch('{{ url_for("admin.clear_logs") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            }
        })
        .then(response => {
            if (response.ok) {
                alert('تم مسح السجلات بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ في مسح السجلات');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في مسح السجلات');
        });
    }
}

// تصدير السجلات
function exportLogs() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'true');
    window.open(`{{ url_for('admin.logs') }}?${params.toString()}`, '_blank');
}

// تحديث الوقت كل ثانية
function updateTimestamps() {
    const timestamps = document.querySelectorAll('.timestamp');
    timestamps.forEach(timestamp => {
        // يمكن إضافة منطق لتحديث الوقت النسبي
    });
}

setInterval(updateTimestamps, 60000); // كل دقيقة

// تمييز السجلات الجديدة
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('.log-row');
    rows.forEach((row, index) => {
        if (index < 5) { // أول 5 سجلات
            row.style.animation = 'fadeIn 0.5s ease-in';
        }
    });
});

// إضافة CSS للأنيميشن
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
