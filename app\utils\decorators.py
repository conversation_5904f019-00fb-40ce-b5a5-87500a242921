# -*- coding: utf-8 -*-
"""
ديكوريتر مساعدة للتطبيق
"""

from functools import wraps
from flask import abort
from flask_login import current_user

def permission_required(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # التحقق من تسجيل الدخول
            if not current_user.is_authenticated:
                abort(401)
            
            # التحقق من الصلاحية
            if not has_permission(current_user, permission):
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def has_permission(user, permission):
    """التحقق من صلاحية المستخدم"""
    
    # المدير له جميع الصلاحيات
    if user.role == 'admin':
        return True
    
    # تعريف الصلاحيات حسب الدور
    permissions = {
        'admin': [
            'create_document', 'edit_document', 'delete_document', 'view_confidential_documents',
            'manage_document_categories', 'manage_documents'
        ],
        'lawyer': [
            'create_document', 'edit_document', 'view_confidential_documents'
        ],
        'assistant': [
            'create_document', 'edit_document'
        ],
        'viewer': []
    }
    
    user_permissions = permissions.get(user.role, [])
    return permission in user_permissions

def admin_required(f):
    """ديكوريتر للمدير فقط"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            abort(403)
        return f(*args, **kwargs)
    return decorated_function

def lawyer_required(f):
    """ديكوريتر للمحامي أو أعلى"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role not in ['admin', 'lawyer']:
            abort(403)
        return f(*args, **kwargs)
    return decorated_function
