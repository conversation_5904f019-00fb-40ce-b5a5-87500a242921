# -*- coding: utf-8 -*-
"""
خدمة إدارة استخراج البيانات من المحاكم
"""

import time
from datetime import datetime, timedelta
from flask import current_app
from app import db
from app.models.extraction import ExtractionLog, CaseProcedure, ExtractionSettings
from app.models.dossier import Dossier
from app.models.audience import Audience
from app.services.mahakim_scraper import MahakimScraper

class ExtractionService:
    """خدمة إدارة الاستخراج"""

    @staticmethod
    def extract_dossier_data(dossier_id, user_id, force_real=False):
        """
        استخراج بيانات ملف من موقع المحاكم

        Args:
            dossier_id (int): معرف الملف
            user_id (int): معرف المستخدم
            force_real (bool): إجبار الاستخراج الحقيقي (تجاهل وضع المحاكاة)

        Returns:
            dict: نتيجة الاستخراج
        """

        start_time = time.time()

        try:
            # الحصول على الملف
            dossier = Dossier.query.get(dossier_id)
            if not dossier:
                return {"success": False, "error": "الملف غير موجود"}

            # الحصول على إعدادات المستخدم
            settings = ExtractionSettings.get_user_settings(user_id)

            # إنشاء سجل استخراج
            extraction_log = ExtractionLog(
                dossier_id=dossier_id,
                numero_dossier=dossier.numero_affaire,
                tribunal=dossier.tribunal or "غير محدد",
                extracted_by=user_id
            )

            # تحديد طريقة الاستخراج
            use_simulation = settings.simulation_mode and not force_real

            if use_simulation:
                # استخدام المحاكاة
                result = ExtractionService._simulate_extraction(dossier, extraction_log)
            else:
                # استخراج حقيقي
                result = ExtractionService._real_extraction(dossier, extraction_log, settings)

            # حساب مدة الاستخراج
            extraction_log.extraction_duration = time.time() - start_time

            # حفظ النتيجة
            extraction_log.success = result["success"]
            if result["success"]:
                extraction_log.set_data(result["data"])
                ExtractionService._save_procedures(dossier_id, extraction_log.id, result["data"])
                ExtractionService._update_dossier_info(dossier, result["data"])
            else:
                extraction_log.error_message = result.get("error", "خطأ غير محدد")

            db.session.add(extraction_log)
            db.session.commit()

            current_app.logger.info(f"تم استخراج بيانات الملف {dossier.numero_affaire} بنجاح")

            return {
                "success": result["success"],
                "data": result.get("data"),
                "error": result.get("error"),
                "extraction_log_id": extraction_log.id,
                "duration": extraction_log.extraction_duration
            }

        except Exception as e:
            current_app.logger.error(f"خطأ في استخراج بيانات الملف: {str(e)}")
            return {"success": False, "error": f"خطأ في الاستخراج: {str(e)}"}

    @staticmethod
    def _simulate_extraction(dossier, extraction_log):
        """محاكاة الاستخراج للاختبار"""
        extraction_log.extraction_method = 'simulation'

        scraper = MahakimScraper()
        result = scraper.simulate_extraction(dossier.numero_affaire, dossier.tribunal)

        return result

    @staticmethod
    def _real_extraction(dossier, extraction_log, settings):
        """الاستخراج الحقيقي من الموقع"""
        extraction_log.extraction_method = 'selenium'

        scraper = MahakimScraper()

        # محاولة الاستخراج مع إعادة المحاولة
        max_retries = settings.max_retries
        retry_delay = settings.retry_delay

        for attempt in range(max_retries):
            try:
                result = scraper.extract_case_info(
                    dossier.numero_affaire,
                    dossier.tribunal,
                    dossier.type_dossier.nom if dossier.type_dossier else "ابتدائية"
                )

                if result["success"]:
                    return result

                # إذا فشلت المحاولة، انتظر قبل المحاولة التالية
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

            except Exception as e:
                current_app.logger.error(f"محاولة {attempt + 1} فشلت: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)

        return {"success": False, "error": "فشل في جميع المحاولات"}

    @staticmethod
    def _save_procedures(dossier_id, extraction_log_id, data):
        """حفظ الإجراءات المستخرجة"""
        if not data or 'procedures' not in data:
            return

        # حذف الإجراءات السابقة لهذا الاستخراج
        CaseProcedure.query.filter_by(
            dossier_id=dossier_id,
            extraction_log_id=extraction_log_id
        ).delete()

        # إضافة الإجراءات الجديدة
        for i, procedure in enumerate(data['procedures']):
            case_procedure = CaseProcedure(
                dossier_id=dossier_id,
                extraction_log_id=extraction_log_id,
                procedure_type=procedure.get('procedure', ''),
                procedure_details=procedure.get('details', ''),
                procedure_status=procedure.get('status', ''),
                sequence_number=i + 1
            )

            # تحليل التاريخ
            date_str = procedure.get('date', '')
            if date_str:
                case_procedure.procedure_date = ExtractionService._parse_date(date_str)
                case_procedure.is_future = case_procedure.procedure_date and case_procedure.procedure_date > datetime.now().date()

            # تحديد نوع الإجراء
            procedure_text = procedure.get('procedure', '').lower()
            case_procedure.is_audience = any(word in procedure_text for word in ['جلسة', 'مرافعة', 'استماع'])
            case_procedure.is_decision = any(word in procedure_text for word in ['قرار', 'حكم', 'نطق'])

            db.session.add(case_procedure)

    @staticmethod
    def _update_dossier_info(dossier, data):
        """تحديث معلومات الملف بناءً على البيانات المستخرجة"""
        if not data:
            return

        # تحديث حالة الملف
        if 'case_status' in data and data['case_status']:
            dossier.situation = data['case_status']

        # إنشاء جلسة قادمة إذا وجدت
        next_audience = data.get('next_audience')
        if next_audience and next_audience.get('date'):
            audience_date = ExtractionService._parse_date(next_audience['date'])
            if audience_date and audience_date > datetime.now().date():
                # التحقق من عدم وجود جلسة بنفس التاريخ
                existing_audience = Audience.query.filter_by(
                    dossier_id=dossier.id,
                    date_audience=audience_date
                ).first()

                if not existing_audience:
                    new_audience = Audience(
                        dossier_id=dossier.id,
                        date_audience=audience_date,
                        heure_audience="09:00",  # وقت افتراضي
                        tribunal=dossier.tribunal,
                        type_audience=next_audience.get('procedure', 'جلسة'),
                        statut='مجدولة',
                        notes=f"تم إنشاؤها تلقائياً من استخراج المحاكم: {next_audience.get('details', '')}"
                    )
                    db.session.add(new_audience)

    @staticmethod
    def _parse_date(date_str):
        """تحليل التاريخ من النص"""
        if not date_str:
            return None

        date_formats = [
            "%d/%m/%Y",
            "%Y-%m-%d",
            "%d-%m-%Y",
            "%d.%m.%Y"
        ]

        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt).date()
            except ValueError:
                continue

        return None

    @staticmethod
    def get_extraction_history(dossier_id, limit=10):
        """الحصول على تاريخ الاستخراج للملف"""
        return ExtractionLog.query.filter_by(dossier_id=dossier_id)\
                                 .order_by(ExtractionLog.created_at.desc())\
                                 .limit(limit).all()

    @staticmethod
    def get_latest_procedures(dossier_id):
        """الحصول على آخر الإجراءات المستخرجة"""
        from app.models.dossier import Dossier
        dossier = Dossier.query.get(dossier_id)
        if dossier:
            return dossier.get_latest_procedures()
        return []

    @staticmethod
    def schedule_auto_extraction():
        """جدولة الاستخراج التلقائي"""
        # الحصول على المستخدمين الذين فعلوا الاستخراج التلقائي
        auto_users = ExtractionSettings.query.filter_by(auto_extraction=True).all()

        for settings in auto_users:
            # الحصول على الملفات النشطة للمستخدم
            active_dossiers = Dossier.query.filter(
                Dossier.situation.in_(['في الجلسات', 'مداولة', 'في الانتظار'])
            ).all()

            for dossier in active_dossiers:
                # التحقق من آخر استخراج
                last_extraction = ExtractionLog.query.filter_by(dossier_id=dossier.id)\
                                                    .order_by(ExtractionLog.created_at.desc())\
                                                    .first()

                should_extract = False

                if not last_extraction:
                    should_extract = True
                else:
                    # تحديد متى يجب الاستخراج التالي
                    frequency_days = {
                        'daily': 1,
                        'weekly': 7,
                        'monthly': 30
                    }

                    days_since_last = (datetime.now() - last_extraction.created_at).days
                    required_days = frequency_days.get(settings.extraction_frequency, 7)

                    should_extract = days_since_last >= required_days

                if should_extract:
                    # تشغيل الاستخراج في الخلفية
                    ExtractionService.extract_dossier_data(dossier.id, settings.user_id)

    @staticmethod
    def get_extraction_statistics():
        """الحصول على إحصائيات الاستخراج"""
        total_extractions = ExtractionLog.query.count()
        successful_extractions = ExtractionLog.query.filter_by(success=True).count()
        failed_extractions = ExtractionLog.query.filter_by(success=False).count()

        # آخر 30 يوم
        thirty_days_ago = datetime.now() - timedelta(days=30)
        recent_extractions = ExtractionLog.query.filter(
            ExtractionLog.created_at >= thirty_days_ago
        ).count()

        return {
            'total_extractions': total_extractions,
            'successful_extractions': successful_extractions,
            'failed_extractions': failed_extractions,
            'success_rate': (successful_extractions / total_extractions * 100) if total_extractions > 0 else 0,
            'recent_extractions': recent_extractions
        }
