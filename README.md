# 🏛️ نظام إدارة مكتب المحامي

نظام شامل لإدارة مكتب المحامي باللغة العربية مع دعم RTL وربط مباشر بموقع المحاكم المغربية.

## ✨ المميزات

### 🔧 المميزات الأساسية
- **إدارة الموكلين**: نظام شامل لإدارة بيانات الموكلين مع البحث المتقدم
- **إدارة الملفات**: تتبع جميع الملفات القانونية مع تصنيفها حسب النوع والمحكمة
- **إدارة الجلسات**: جدولة ومتابعة الجلسات مع التنبيهات التلقائية
- **أنواع الملفات**: تصنيف مرن للملفات (طلاق، جنحي، مدني، تجاري، إلخ)
- **سجل الإجراءات**: تتبع جميع الإجراءات والقرارات

### 🌐 الربط مع موقع المحاكم
- **استخراج تلقائي**: جلب الإجراءات والقرارات من موقع mahakim.ma
- **تحديث الجلسات**: استخراج مواعيد الجلسات القادمة تلقائياً
- **حفظ البيانات**: تخزين جميع البيانات المستخرجة في قاعدة البيانات

### 📊 التقارير والإحصائيات
- **تقارير PDF**: طباعة جلسات اليوم والتقارير المخصصة
- **إحصائيات شاملة**: تحليل البيانات حسب النوع والمحكمة والفترة
- **تصدير البيانات**: تصدير إلى Excel وPDF

### 📁 نظام إدارة الوثائق ✨ **جديد**
- **رفع متقدم**: رفع جميع أنواع الوثائق (PDF، Word، صور، Excel، ملفات مضغوطة)
- **تصنيف ذكي**: 9 تصنيفات افتراضية (أحكام، مقالات افتتاحية، محاضر تبليغ، وثائق تعريفية، إلخ)
- **ربط تلقائي**: ربط الوثائق بالملفات والموكلين والجلسات
- **بحث متقدم**: بحث في العناوين والأوصاف مع فلترة متعددة المعايير
- **معاينة مدمجة**: معاينة PDF والصور مباشرة في المتصفح
- **إدارة الصلاحيات**: وثائق سرية ونسخ أصلية مع تحكم في الوصول
- **إجراءات مجمعة**: حذف وتغيير تصنيف وتحميل متعدد
- **تحميل مضغوط**: تحميل وثائق متعددة كملف ZIP منظم

### 🔄 نظام استخراج البيانات من المحاكم ✨ **جديد**
- **ربط تلقائي**: اتصال مباشر مع موقع المحاكم المغربية
- **استخراج ذكي**: استخراج الإجراءات والقرارات والجلسات القادمة
- **وضع المحاكاة**: اختبار النظام ببيانات تجريبية
- **استخراج مجمع**: استخراج بيانات عدة ملفات مرة واحدة
- **جدولة تلقائية**: استخراج دوري حسب الجدولة المحددة
- **تتبع شامل**: سجلات مفصلة لجميع عمليات الاستخراج
- **إعدادات متقدمة**: تحكم في المتصفح وإعادة المحاولة والتنبيهات

### 🔔 نظام التنبيهات التلقائية ✨ **جديد**
- **تنبيهات الجلسات**: تذكير قبل 24 أو 48 ساعة من موعد الجلسة
- **تنبيهات المواعيد النهائية**: تنبيه قبل انتهاء آجال الاستئناف والطعن والتبليغ
- **تنبيهات الردود المطلوبة**: تذكير بإعداد الردود على مذكرات الخصوم
- **إشعارات البريد الإلكتروني**: إرسال التنبيهات عبر البريد (اختياري)
- **إعدادات قابلة للتخصيص**: تحكم كامل في أنواع التنبيهات والتوقيت
- **أولويات متعددة**: تنبيهات عاجلة وعالية ومتوسطة ومنخفضة
- **تنبيهات ذكية**: ربط التنبيهات بالملفات والجلسات والموكلين

### 🔐 نظام المستخدمين
- **أدوار متعددة**: مدير، محامي، مساعد، مشاهد
- **صلاحيات مرنة**: تحكم دقيق في الوصول للبيانات
- **أمان عالي**: تشفير كلمات المرور وحماية الجلسات

### 🎨 واجهة المستخدم
- **دعم RTL**: واجهة عربية كاملة مع دعم الاتجاه من اليمين لليسار
- **تصميم عصري**: استخدام Bootstrap مع تخصيصات عربية
- **متجاوب**: يعمل على جميع الأجهزة (كمبيوتر، تابلت، هاتف)

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- Python 3.8+
- SQLite (أو PostgreSQL للإنتاج)
- Chrome/Chromium (لاستخراج البيانات)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/mohami.git
cd mohami
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate  # Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتعديل ملف .env حسب إعداداتك
```

5. **تهيئة قاعدة البيانات**
```bash
python init_db.py
```

6. **تشغيل التطبيق**
```bash
python run.py
```

7. **الوصول للتطبيق**
افتح المتصفح وانتقل إلى: `http://localhost:5001`

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## 📁 هيكل المشروع

```
mohami/
├── app/
│   ├── __init__.py              # إعداد التطبيق الرئيسي
│   ├── models/                  # نماذج قاعدة البيانات
│   │   ├── user.py             # نموذج المستخدمين
│   │   ├── client.py           # نموذج الموكلين
│   │   ├── dossier.py          # نموذج الملفات
│   │   ├── audience.py         # نموذج الجلسات
│   │   ├── type_dossier.py     # نموذج أنواع الملفات
│   │   └── journal_actions.py  # نموذج سجل الإجراءات
│   ├── blueprints/             # وحدات التطبيق
│   │   ├── auth/               # المصادقة
│   │   ├── dashboard/          # لوحة التحكم
│   │   ├── clients/            # إدارة الموكلين
│   │   ├── dossiers/           # إدارة الملفات
│   │   └── audiences/          # إدارة الجلسات
│   ├── forms/                  # نماذج الإدخال
│   ├── templates/              # قوالب HTML
│   ├── static/                 # الملفات الثابتة
│   └── utils/                  # أدوات مساعدة
│       ├── mahakim_scraper.py  # استخراج من موقع المحاكم
│       └── pdf_generator.py    # توليد ملفات PDF
├── migrations/                 # ملفات الهجرة
├── uploads/                    # الملفات المرفوعة
├── config.py                   # إعدادات التطبيق
├── run.py                      # نقطة البداية
├── init_db.py                  # تهيئة قاعدة البيانات
└── requirements.txt            # المتطلبات
```

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
```python
# للتطوير (SQLite)
DATABASE_URL = 'sqlite:///mohami.db'

# للإنتاج (PostgreSQL)
DATABASE_URL = 'postgresql://user:password@localhost/mohami'
```

### إعدادات البريد الإلكتروني
```python
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-app-password'
```

## 📚 الاستخدام

### إضافة موكل جديد
1. انتقل إلى "الموكلين" → "إضافة موكل جديد"
2. املأ البيانات المطلوبة
3. اختر نوع الملف المناسب
4. احفظ البيانات

### إنشاء ملف جديد
1. انتقل إلى "الملفات" → "إضافة ملف جديد"
2. اختر الموكل ونوع الملف
3. أدخل رقم الملف والمحكمة
4. احفظ الملف

### جدولة جلسة
1. انتقل إلى "الجلسات" → "إضافة جلسة جديدة"
2. اختر الملف وتاريخ الجلسة
3. أدخل تفاصيل الجلسة
4. احفظ الجلسة

### استخراج الإجراءات
1. افتح صفحة الملف
2. انقر على "جلب الإجراء"
3. سيتم استخراج البيانات تلقائياً من موقع المحاكم

## 🚀 التشغيل

```bash
# تشغيل التطبيق
python run.py

# أو باستخدام Flask CLI
flask run --host=0.0.0.0 --port=5001 --debug
```

**الوصول للتطبيق**: http://localhost:5001

### 🔄 تفعيل الأنظمة المتقدمة

```bash
# تفعيل نظام التنبيهات
python update_notifications.py

# تفعيل نظام الوثائق
python update_documents.py

# تفعيل نظام الاستخراج
python update_extraction.py

# تثبيت متطلبات الاستخراج
pip install selenium webdriver-manager
```

**ملاحظة**: تأكد من تثبيت متصفح Chrome للاستخراج الحقيقي من موقع المحاكم

## 🛠️ التطوير

### إضافة ميزة جديدة
1. أنشئ Blueprint جديد في `app/blueprints/`
2. أضف النماذج في `app/models/`
3. أنشئ النماذج في `app/forms/`
4. أضف القوالب في `app/templates/`
5. سجل Blueprint في `app/__init__.py`

### تشغيل الاختبارات
```bash
python -m pytest tests/
```

### إنشاء هجرة جديدة
```bash
flask db migrate -m "وصف التغيير"
flask db upgrade
```

## 🔄 نظام استخراج البيانات من المحاكم

### تفعيل نظام الاستخراج
```bash
# تحديث قاعدة البيانات لإضافة جداول الاستخراج
python update_extraction.py
```

### المتطلبات التقنية

#### متطلبات أساسية
- **Python 3.8+**: لتشغيل النظام
- **متصفح Chrome**: للاستخراج الحقيقي
- **اتصال بالإنترنت**: للوصول لموقع المحاكم

#### مكتبات Python المطلوبة
```bash
pip install selenium webdriver-manager
```

### طرق الاستخراج

#### 1. الاستخراج التجريبي (المحاكاة)
- **الغرض**: للاختبار والتطوير
- **المميزات**:
  - سريع وفوري
  - لا يحتاج اتصال بالإنترنت
  - بيانات تجريبية واقعية
- **الاستخدام**: مفعل افتراضياً في الإعدادات

#### 2. الاستخراج الحقيقي
- **الغرض**: استخراج حقيقي من موقع المحاكم
- **المتطلبات**:
  - اتصال مستقر بالإنترنت
  - متصفح Chrome مثبت
  - رقم ملف صحيح
- **المدة**: 30-60 ثانية حسب سرعة الموقع

### البيانات المستخرجة

#### الإجراءات القضائية
- **تاريخ الإجراء**: متى تم تنفيذ الإجراء
- **نوع الإجراء**: إيداع، تبليغ، جلسة، قرار
- **تفاصيل الإجراء**: وصف مفصل للإجراء
- **حالة الإجراء**: مكتمل، مجدولة، ملغية، مؤجلة

#### الجلسات القادمة
- **تاريخ الجلسة**: موعد الجلسة القادمة
- **نوع الجلسة**: مرافعات، نطق بالحكم، استماع
- **تفاصيل الجلسة**: الغرض والموضوع
- **إنشاء تلقائي**: إضافة الجلسة لجدول الجلسات

#### القرارات والأحكام
- **تاريخ القرار**: متى صدر القرار
- **نوع القرار**: حكم ابتدائي، قرار استئنافي، أمر
- **محتوى القرار**: نص القرار أو ملخصه

### استخدام النظام

#### الوصول للنظام
```
http://localhost:5001/extraction
```

#### استخراج ملف واحد
1. **من صفحة الاستخراج**:
   - اختر الملف من القائمة
   - انقر "استخراج"
   - انتظر النتيجة

2. **من صفحة الملف**:
   - افتح صفحة الملف
   - انقر "جلب الإجراء"
   - راجع النتائج في تبويب "الإجراءات المستخرجة"

#### الاستخراج المجمع
1. حدد عدة ملفات من القائمة
2. انقر "استخراج مجمع"
3. اختر نوع الاستخراج (تجريبي/حقيقي)
4. انقر "بدء الاستخراج"

### الإعدادات المتقدمة

#### إعدادات عامة
- **الاستخراج التلقائي**: تفعيل/إلغاء الاستخراج الدوري
- **تكرار الاستخراج**: يومياً، أسبوعياً، شهرياً
- **وضع المحاكاة**: للاختبار بدون اتصال حقيقي

#### إعدادات المتصفح
- **متصفح خفي**: تشغيل Chrome في الخلفية
- **مهلة الانتظار**: الوقت الأقصى لتحميل الصفحة (30-120 ثانية)
- **إعادة المحاولة**: عدد المحاولات عند الفشل (1-10)

#### إعدادات التنبيهات
- **تنبيه عند النجاح**: إشعار عند نجاح الاستخراج
- **تنبيه عند الفشل**: إشعار عند فشل الاستخراج
- **تنبيه عند إجراء جديد**: إشعار عند اكتشاف إجراء جديد

### مراقبة النظام

#### السجلات والتقارير
- **سجلات الاستخراج**: تاريخ جميع العمليات
- **إحصائيات الأداء**: معدل النجاح والفشل
- **تقارير مفصلة**: تحليل الأداء والاستخدام

#### استكشاف الأخطاء
- **اختبار الاتصال**: فحص الاتصال بموقع المحاكم
- **سجلات الأخطاء**: تفاصيل أسباب الفشل
- **إعادة المحاولة التلقائية**: محاولات متعددة عند الفشل

## 📁 نظام إدارة الوثائق

### تفعيل نظام الوثائق
```bash
# تحديث قاعدة البيانات لإضافة جداول الوثائق
python update_documents.py
```

### أنواع الوثائق المدعومة

#### 📄 المستندات
- **PDF**: ملفات PDF مع معاينة مدمجة
- **Word**: ملفات .doc و .docx
- **نصوص**: ملفات .txt و .rtf و .odt

#### 🖼️ الصور
- **صور عادية**: JPG, PNG, GIF, BMP
- **صور ويب**: WebP
- **معاينة مباشرة**: عرض الصور في المتصفح

#### 📊 جداول البيانات
- **Excel**: ملفات .xls و .xlsx

#### 🗜️ ملفات مضغوطة
- **ZIP, RAR, 7Z**: للوثائق المتعددة

### التصنيفات الافتراضية

1. **⚖️ الأحكام**: نسخ من الأحكام والقرارات القضائية
2. **📝 المقالات الافتتاحية**: المقالات الافتتاحية والدعاوى
3. **📢 محاضر التبليغ**: محاضر التبليغ والإعلان
4. **🆔 الوثائق التعريفية**: البطائق الوطنية وجوازات السفر
5. **🏛️ الوثائق الرسمية**: الوثائق الرسمية والشهادات
6. **🤝 العقود والاتفاقيات**: العقود والاتفاقيات القانونية
7. **✉️ المراسلات**: المراسلات والخطابات
8. **🔍 الأدلة والمستندات**: الأدلة والمستندات المؤيدة
9. **📋 أخرى**: وثائق أخرى متنوعة

### المميزات المتقدمة

#### 🔗 الربط التلقائي
- **ربط بالملفات**: ربط الوثائق بملفات القضايا
- **ربط بالموكلين**: ربط الوثائق بالموكلين
- **ربط بالجلسات**: ربط الوثائق بجلسات المحكمة

#### 🔒 إدارة الصلاحيات
- **وثائق سرية**: تقييد الوصول للوثائق الحساسة
- **نسخ أصلية**: تمييز النسخ الأصلية من النسخ
- **صلاحيات المستخدمين**: تحكم في من يمكنه رفع/تعديل/حذف الوثائق

#### 🔍 البحث والفلترة
- **بحث نصي**: البحث في العناوين والأوصاف
- **فلترة بالتصنيف**: عرض وثائق تصنيف معين
- **فلترة بنوع الملف**: PDF، صور، مستندات، إلخ
- **فلترة بالتاريخ**: البحث حسب تاريخ الوثيقة أو الرفع
- **فلترة بالحالة**: سرية، أصلية، إلخ

#### ⚡ الإجراءات المجمعة
- **حذف متعدد**: حذف عدة وثائق مرة واحدة
- **تغيير التصنيف**: تغيير تصنيف وثائق متعددة
- **تحديد السرية**: تحديد وثائق متعددة كسرية أو إلغاء السرية
- **تحميل مضغوط**: تحميل وثائق متعددة كملف ZIP منظم

### استخدام النظام

#### رفع وثيقة جديدة
1. اذهب إلى `/documents/upload`
2. اختر الملف (سحب وإفلات أو تصفح)
3. أدخل عنوان ووصف الوثيقة
4. اختر التصنيف المناسب
5. حدد الخيارات (سرية، نسخة أصلية)
6. اربط بملف أو موكل أو جلسة (اختياري)

#### إدارة الوثائق
- **عرض جميع الوثائق**: `/documents`
- **البحث والفلترة**: استخدم نموذج البحث المتقدم
- **عرض تفاصيل وثيقة**: انقر على عنوان الوثيقة
- **تحميل وثيقة**: انقر على زر التحميل
- **تعديل وثيقة**: انقر على زر التعديل
- **حذف وثيقة**: انقر على زر الحذف مع التأكيد

#### إدارة التصنيفات
- **عرض التصنيفات**: `/documents/categories`
- **إضافة تصنيف جديد**: للمديرين فقط
- **تعديل تصنيف**: تغيير الاسم والوصف والأيقونة

## 🔔 نظام التنبيهات التلقائية

### تفعيل التنبيهات
```bash
# تحديث قاعدة البيانات لإضافة جداول التنبيهات
python update_notifications.py

# تشغيل فحص التنبيهات مرة واحدة
python notification_scheduler.py --once

# تشغيل مجدول التنبيهات المستمر
python notification_scheduler.py
```

### أنواع التنبيهات

#### 📅 تنبيهات الجلسات
- تذكير قبل 24 ساعة من موعد الجلسة (افتراضي)
- تنبيه عاجل قبل ساعتين من الجلسة
- معلومات شاملة: الملف، الموكل، المحكمة، الوقت

#### ⏰ تنبيهات المواعيد النهائية
- **آجال الاستئناف**: تنبيه قبل 48 ساعة من انتهاء أجل الاستئناف (30 يوم من تاريخ الحكم)
- **آجال الطعن**: تنبيه قبل انتهاء مواعيد الطعن
- **آجال التبليغ**: تذكير بمواعيد التبليغ المهمة

#### 📝 تنبيهات الردود المطلوبة
- تذكير بإعداد الردود على مذكرات الخصوم
- تنبيه قبل 24 ساعة من موعد تقديم الرد
- ربط مع الجلسات والملفات ذات الصلة

### إعدادات التنبيهات

يمكن تخصيص التنبيهات من خلال:
1. **صفحة إعدادات التنبيهات**: `/notifications/settings`
2. **تحديد أنواع التنبيهات المطلوبة**
3. **تخصيص التوقيت** (1 ساعة إلى أسبوعين)
4. **تفعيل إشعارات البريد الإلكتروني**

### التشغيل التلقائي

#### Windows Task Scheduler
```cmd
# إنشاء مهمة يومية في الساعة 8:00 صباحاً
schtasks /create /tn "Mohami Notifications" /tr "python C:\path\to\notification_scheduler.py --once" /sc daily /st 08:00
```

#### Linux Cron Job
```bash
# إضافة إلى crontab للتشغيل يومياً في 8:00 صباحاً
0 8 * * * cd /path/to/mohami && python notification_scheduler.py --once

# فحص كل ساعة للتنبيهات العاجلة
0 * * * * cd /path/to/mohami && python -c "from app.utils.notification_service import NotificationService; from app import create_app; app = create_app(); app.app_context().push(); NotificationService.send_pending_notifications()"
```

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راسلنا على: <EMAIL>
- انضم لمجتمعنا على Discord

## 🙏 شكر وتقدير

- فريق Flask لإطار العمل الرائع
- مجتمع Bootstrap للتصميم
- جميع المساهمين في المشروع

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض المميزات قد تكون غير مكتملة.
