# -*- coding: utf-8 -*-
"""
نماذج الجلسات
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DateField, TimeField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from datetime import date, time
from app.models.dossier import Dossier
from app.models.type_dossier import TypeDossier

class AudienceForm(FlaskForm):
    """نموذج إضافة/تعديل الجلسة"""
    
    dossier_id = SelectField('الملف', validators=[DataRequired()],
                            coerce=int,
                            render_kw={'class': 'form-select'})
    
    date_audience = DateField('تاريخ الجلسة', validators=[DataRequired()],
                             render_kw={'class': 'form-control'})
    
    heure_audience = TimeField('وقت الجلسة', validators=[Optional()],
                              default=time(9, 0),
                              render_kw={'class': 'form-control'})
    
    reference_interne = StringField('المرجع الداخلي', validators=[Optional(), Length(max=50)],
                                   render_kw={'class': 'form-control', 'placeholder': 'مرجع داخلي اختياري'})
    
    demande = TextAreaField('الطلب/الموضوع', validators=[Optional()],
                           render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'موضوع الجلسة أو الطلب'})
    
    resultat = TextAreaField('نتيجة الجلسة', validators=[Optional()],
                            render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'نتيجة الجلسة (يملأ بعد انعقادها)'})
    
    statut = SelectField('حالة الجلسة', validators=[DataRequired()],
                        choices=[
                            ('مبرمجة', 'مبرمجة'),
                            ('منعقدة', 'منعقدة'),
                            ('مؤجلة', 'مؤجلة'),
                            ('ملغية', 'ملغية')
                        ],
                        default='مبرمجة',
                        render_kw={'class': 'form-select'})
    
    is_important = BooleanField('جلسة مهمة', render_kw={'class': 'form-check-input'})
    
    notes = TextAreaField('ملاحظات', validators=[Optional()],
                         render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'ملاحظات إضافية'})
    
    def __init__(self, *args, **kwargs):
        super(AudienceForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات الملفات النشطة
        self.dossier_id.choices = [(0, 'اختر الملف')] + [
            (d.id, f"{d.numero_affaire} - {d.client.nom_complet}") 
            for d in Dossier.query.filter_by(is_archived=False).order_by(Dossier.numero_affaire).all()
        ]
    
    def validate_date_audience(self, date_audience):
        """التحقق من صحة تاريخ الجلسة"""
        if date_audience.data and date_audience.data < date.today():
            # السماح بالتواريخ الماضية فقط إذا كانت الحالة "منعقدة" أو "ملغية"
            if self.statut.data not in ['منعقدة', 'ملغية']:
                raise ValidationError('لا يمكن برمجة جلسة في تاريخ ماضي')

class AudienceSearchForm(FlaskForm):
    """نموذج البحث في الجلسات"""
    
    date_filter = DateField('تاريخ الجلسة', validators=[Optional()],
                           render_kw={'class': 'form-control'})
    
    tribunal = SelectField('المحكمة', validators=[Optional()],
                          choices=[
                              ('', 'جميع المحاكم'),
                              ('ابتدائية', 'ابتدائية'),
                              ('استئناف', 'استئناف'),
                              ('نقض', 'نقض'),
                              ('تجارية', 'تجارية'),
                              ('إدارية', 'إدارية')
                          ],
                          render_kw={'class': 'form-select'})
    
    type_dossier = SelectField('نوع الملف', validators=[Optional()],
                              render_kw={'class': 'form-select'})
    
    statut = SelectField('حالة الجلسة', validators=[Optional()],
                        choices=[
                            ('', 'جميع الحالات'),
                            ('مبرمجة', 'مبرمجة'),
                            ('منعقدة', 'منعقدة'),
                            ('مؤجلة', 'مؤجلة'),
                            ('ملغية', 'ملغية')
                        ],
                        render_kw={'class': 'form-select'})
    
    def __init__(self, *args, **kwargs):
        super(AudienceSearchForm, self).__init__(*args, **kwargs)
        # تحديث خيارات نوع الملف
        self.type_dossier.choices = [('', 'جميع الأنواع')] + [
            (t.nom, t.nom) for t in TypeDossier.get_active_types()
        ]

class AudienceBulkForm(FlaskForm):
    """نموذج إضافة جلسات متعددة"""
    
    tribunal = SelectField('المحكمة', validators=[DataRequired()],
                          choices=[
                              ('', 'اختر المحكمة'),
                              ('ابتدائية', 'ابتدائية'),
                              ('استئناف', 'استئناف'),
                              ('نقض', 'نقض'),
                              ('تجارية', 'تجارية'),
                              ('إدارية', 'إدارية')
                          ],
                          render_kw={'class': 'form-select'})
    
    type_dossier_id = SelectField('نوع الملف', validators=[DataRequired()],
                                 coerce=int,
                                 render_kw={'class': 'form-select'})
    
    date_audience = DateField('تاريخ الجلسة', validators=[DataRequired()],
                             render_kw={'class': 'form-control'})
    
    heure_audience = TimeField('وقت الجلسة', validators=[Optional()],
                              default=time(9, 0),
                              render_kw={'class': 'form-control'})
    
    def __init__(self, *args, **kwargs):
        super(AudienceBulkForm, self).__init__(*args, **kwargs)
        # تحديث خيارات نوع الملف
        self.type_dossier_id.choices = [(0, 'اختر نوع الملف')] + [
            (t.id, t.nom) for t in TypeDossier.get_active_types()
        ]
