# -*- coding: utf-8 -*-
"""
وحدة توليد ملفات PDF
"""

from datetime import datetime, date
from typing import List, Dict
import os
from io import BytesIO
from flask import render_template_string

try:
    import weasyprint
    WEASYPRINT_AVAILABLE = True
except ImportError:
    WEASYPRINT_AVAILABLE = False
    print("تحذير: مكتبة weasyprint غير مثبتة. سيتم استخدام HTML بدلاً من PDF")

class PDFGenerator:
    """فئة توليد ملفات PDF"""

    def __init__(self):
        """تهيئة مولد PDF"""
        pass

    def generate_audiences_report(self, audiences: List[Dict], date_filter: date = None,
                                 lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
        """
        توليد تقرير PDF للجلسات

        Args:
            audiences: قائمة الجلسات
            date_filter: تاريخ الفلتر
            lawyer_name: اسم المحامي

        Returns:
            BytesIO: ملف PDF
        """
        buffer = BytesIO()

        try:
            if WEASYPRINT_AVAILABLE:
                # توليد PDF حقيقي باستخدام WeasyPrint
                html_content = self._generate_html_content(audiences, date_filter, lawyer_name)
                pdf_bytes = weasyprint.HTML(string=html_content).write_pdf()
                buffer.write(pdf_bytes)
            else:
                # توليد HTML كبديل
                html_content = self._generate_html_content(audiences, date_filter, lawyer_name)
                buffer.write(html_content.encode('utf-8'))

            buffer.seek(0)

        except Exception as e:
            print(f"خطأ في توليد تقرير PDF: {e}")
            # إنشاء محتوى خطأ
            error_content = self._generate_error_html(str(e))
            buffer.write(error_content.encode('utf-8'))
            buffer.seek(0)

        return buffer

    def _generate_html_content(self, audiences: List[Dict], date_filter: date, lawyer_name: str) -> str:
        """توليد محتوى HTML للجلسات"""

        # تحديد التاريخ المعروض
        date_str = date_filter.strftime('%d/%m/%Y') if date_filter else 'جميع التواريخ'
        current_time = datetime.now().strftime('%d/%m/%Y %H:%M')

        # بناء جدول الجلسات
        table_rows = ""
        for i, audience in enumerate(audiences, 1):
            # تحديد لون الصف حسب الأولوية
            row_class = "table-warning" if audience.get('is_important') else ""

            # تحديد أيقونة الحالة
            status_icon = "✓" if audience.get('statut') == 'منعقدة' else "⏰"

            table_rows += f"""
            <tr class="{row_class}">
                <td class="text-center">{i}</td>
                <td>{audience.get('numero_dossier', 'غير محدد')}</td>
                <td>{audience.get('client_nom', 'غير محدد')}</td>
                <td>{audience.get('tribunal', 'غير محدد')}</td>
                <td class="text-center">{audience.get('date_audience', 'غير محدد')}</td>
                <td class="text-center">{audience.get('heure_audience', '09:00')}</td>
                <td>{audience.get('demande', 'غير محدد')}</td>
                <td class="text-center">
                    <span class="badge bg-primary">{status_icon} {audience.get('statut', 'مبرمجة')}</span>
                </td>
            </tr>
            """

        # إذا لم توجد جلسات
        if not audiences:
            table_rows = """
            <tr>
                <td colspan="8" class="text-center text-muted">
                    <i class="fas fa-calendar-times fa-2x mb-2"></i>
                    <br>لا توجد جلسات للعرض
                </td>
            </tr>
            """

        html_template = f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير الجلسات</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

                body {{
                    font-family: 'Noto Sans Arabic', Arial, sans-serif;
                    direction: rtl;
                    margin: 0;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}

                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                }}

                .header {{
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                }}

                .header h1 {{
                    color: #007bff;
                    margin: 0;
                    font-size: 2.5em;
                    font-weight: 700;
                }}

                .info-section {{
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-bottom: 30px;
                    border-right: 4px solid #007bff;
                }}

                .info-row {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    font-size: 1.1em;
                }}

                .info-label {{
                    font-weight: bold;
                    color: #495057;
                }}

                .info-value {{
                    color: #007bff;
                    font-weight: 600;
                }}

                .table-container {{
                    overflow-x: auto;
                    margin-bottom: 30px;
                }}

                table {{
                    width: 100%;
                    border-collapse: collapse;
                    font-size: 0.9em;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    border-radius: 8px;
                    overflow: hidden;
                }}

                th {{
                    background: linear-gradient(135deg, #007bff, #0056b3);
                    color: white;
                    padding: 15px 10px;
                    text-align: center;
                    font-weight: 700;
                    font-size: 1em;
                }}

                td {{
                    padding: 12px 10px;
                    border-bottom: 1px solid #dee2e6;
                    vertical-align: middle;
                }}

                tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}

                tr:hover {{
                    background-color: #e3f2fd;
                }}

                .table-warning {{
                    background-color: #fff3cd !important;
                }}

                .badge {{
                    padding: 5px 10px;
                    border-radius: 15px;
                    font-size: 0.8em;
                    font-weight: 600;
                }}

                .bg-primary {{
                    background-color: #007bff;
                    color: white;
                }}

                .summary {{
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    font-size: 1.2em;
                    font-weight: 600;
                }}

                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 2px solid #dee2e6;
                    color: #6c757d;
                    font-size: 0.9em;
                }}

                @media print {{
                    body {{
                        background: white;
                        padding: 0;
                    }}

                    .container {{
                        box-shadow: none;
                        padding: 20px;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>📅 تقرير الجلسات</h1>
                </div>

                <div class="info-section">
                    <div class="info-row">
                        <span class="info-label">👨‍💼 المحامي:</span>
                        <span class="info-value">{lawyer_name}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">📅 التاريخ:</span>
                        <span class="info-value">{date_str}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">🕐 تاريخ التوليد:</span>
                        <span class="info-value">{current_time}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">📊 عدد الجلسات:</span>
                        <span class="info-value">{len(audiences)} جلسة</span>
                    </div>
                </div>

                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>رقم الملف</th>
                                <th>الموكل</th>
                                <th>المحكمة</th>
                                <th>التاريخ</th>
                                <th>الوقت</th>
                                <th>الموضوع</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {table_rows}
                        </tbody>
                    </table>
                </div>

                <div class="summary">
                    📈 إجمالي الجلسات: {len(audiences)} جلسة
                </div>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة نظام إدارة مكتب المحاماة</p>
                    <p>© {datetime.now().year} - جميع الحقوق محفوظة</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_template

    def _generate_error_html(self, error_message: str) -> str:
        """توليد صفحة HTML للخطأ"""
        return f"""
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>خطأ في التقرير</title>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; padding: 20px; }}
                .error {{ color: red; background: #ffe6e6; padding: 20px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="error">
                <h2>❌ خطأ في توليد التقرير</h2>
                <p>{error_message}</p>
                <p>يرجى المحاولة مرة أخرى أو الاتصال بالدعم التقني.</p>
            </div>
        </body>
        </html>
        """

    def _generate_mock_pdf_content(self, audiences: List[Dict], date_filter: date, lawyer_name: str) -> str:
        """توليد محتوى PDF تجريبي"""
        content = f"""
تقرير الجلسات
=============

المحامي: {lawyer_name}
التاريخ: {date_filter.strftime('%d/%m/%Y') if date_filter else 'جميع التواريخ'}
تاريخ التوليد: {datetime.now().strftime('%d/%m/%Y %H:%M')}

الجلسات:
--------
"""

        for i, audience in enumerate(audiences, 1):
            content += f"""
{i}. الملف: {audience.get('numero_dossier', 'غير محدد')}
   الموكل: {audience.get('client_nom', 'غير محدد')}
   المحكمة: {audience.get('tribunal', 'غير محدد')}
   التاريخ: {audience.get('date_audience', 'غير محدد')}
   الوقت: {audience.get('heure_audience', '09:00')}
   الموضوع: {audience.get('demande', 'غير محدد')}

"""

        content += f"\nإجمالي الجلسات: {len(audiences)}"
        return content

    def _real_generate_audiences_report(self, audiences: List[Dict], date_filter: date = None,
                                       lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
        """توليد تقرير PDF حقيقي (سيتم تفعيله لاحقاً)"""
        buffer = BytesIO()

        try:
            # إنشاء مستند PDF
            # doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
            #                        topMargin=2*cm, bottomMargin=2*cm)

            # إعداد الأنماط
            # styles = getSampleStyleSheet()
            # title_style = ParagraphStyle(
            #     'CustomTitle',
            #     parent=styles['Heading1'],
            #     fontName='Arabic',
            #     fontSize=18,
            #     alignment=2,  # محاذاة يمين
            #     spaceAfter=30
            # )

            # header_style = ParagraphStyle(
            #     'CustomHeader',
            #     parent=styles['Heading2'],
            #     fontName='Arabic',
            #     fontSize=14,
            #     alignment=2,
            #     spaceAfter=12
            # )

            # normal_style = ParagraphStyle(
            #     'CustomNormal',
            #     parent=styles['Normal'],
            #     fontName='Arabic',
            #     fontSize=12,
            #     alignment=2,
            #     spaceAfter=6
            # )

            # بناء المحتوى
            # story = []

            # العنوان
            # title = Paragraph("تقرير الجلسات", title_style)
            # story.append(title)
            # story.append(Spacer(1, 12))

            # معلومات المحامي والتاريخ
            # info_data = [
            #     ['المحامي:', lawyer_name],
            #     ['التاريخ:', date_filter.strftime('%d/%m/%Y') if date_filter else 'جميع التواريخ'],
            #     ['تاريخ التوليد:', datetime.now().strftime('%d/%m/%Y %H:%M')]
            # ]

            # info_table = Table(info_data, colWidths=[4*cm, 8*cm])
            # info_table.setStyle(TableStyle([
            #     ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            #     ('FONTSIZE', (0, 0), (-1, -1), 12),
            #     ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            #     ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            #     ('GRID', (0, 0), (-1, -1), 1, colors.black)
            # ]))

            # story.append(info_table)
            # story.append(Spacer(1, 20))

            # جدول الجلسات
            # if audiences:
            #     table_data = [['#', 'رقم الملف', 'الموكل', 'المحكمة', 'التاريخ', 'الوقت', 'الموضوع']]
            #
            #     for i, audience in enumerate(audiences, 1):
            #         row = [
            #             str(i),
            #             audience.get('numero_dossier', ''),
            #             audience.get('client_nom', ''),
            #             audience.get('tribunal', ''),
            #             audience.get('date_audience', ''),
            #             audience.get('heure_audience', ''),
            #             audience.get('demande', '')[:50] + '...' if len(audience.get('demande', '')) > 50 else audience.get('demande', '')
            #         ]
            #         table_data.append(row)
            #
            #     audiences_table = Table(table_data, colWidths=[1*cm, 3*cm, 3*cm, 2*cm, 2*cm, 1.5*cm, 4*cm])
            #     audiences_table.setStyle(TableStyle([
            #         ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            #         ('FONTSIZE', (0, 0), (-1, -1), 10),
            #         ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            #         ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            #         ('GRID', (0, 0), (-1, -1), 1, colors.black),
            #         ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            #         ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            #     ]))
            #
            #     story.append(audiences_table)
            # else:
            #     no_data = Paragraph("لا توجد جلسات للعرض", normal_style)
            #     story.append(no_data)

            # إضافة إجمالي
            # story.append(Spacer(1, 20))
            # total = Paragraph(f"إجمالي الجلسات: {len(audiences)}", header_style)
            # story.append(total)

            # بناء المستند
            # doc.build(story)

            pass

        except Exception as e:
            print(f"خطأ في توليد PDF: {e}")

        buffer.seek(0)
        return buffer

    def generate_dossier_report(self, dossier: Dict, audiences: List[Dict] = None,
                               actions: List[Dict] = None) -> BytesIO:
        """
        توليد تقرير PDF لملف محدد

        Args:
            dossier: معلومات الملف
            audiences: جلسات الملف
            actions: إجراءات الملف

        Returns:
            BytesIO: ملف PDF
        """
        buffer = BytesIO()

        try:
            # محاكاة توليد تقرير الملف
            content = f"""
تقرير الملف
===========

رقم الملف: {dossier.get('numero_affaire', 'غير محدد')}
الموكل: {dossier.get('client_nom', 'غير محدد')}
المحكمة: {dossier.get('tribunal', 'غير محدد')}
نوع الملف: {dossier.get('type_dossier', 'غير محدد')}
الحالة: {dossier.get('situation', 'غير محدد')}
تاريخ الفتح: {dossier.get('date_ouverture', 'غير محدد')}

الجلسات:
--------
"""

            if audiences:
                for i, audience in enumerate(audiences, 1):
                    content += f"{i}. {audience.get('date_audience')} - {audience.get('statut')}\n"
            else:
                content += "لا توجد جلسات\n"

            content += f"\nتاريخ التوليد: {datetime.now().strftime('%d/%m/%Y %H:%M')}"

            buffer.write(content.encode('utf-8'))
            buffer.seek(0)

        except Exception as e:
            print(f"خطأ في توليد تقرير الملف: {e}")

        return buffer

def generate_today_audiences_pdf(audiences: List[Dict], lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
    """
    دالة مساعدة لتوليد PDF جلسات اليوم

    Args:
        audiences: قائمة جلسات اليوم
        lawyer_name: اسم المحامي

    Returns:
        BytesIO: ملف PDF
    """
    generator = PDFGenerator()
    return generator.generate_audiences_report(audiences, date.today(), lawyer_name)
