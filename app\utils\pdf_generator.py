# -*- coding: utf-8 -*-
"""
وحدة توليد ملفات PDF
"""

from datetime import datetime, date
from typing import List, Dict
import os
from io import BytesIO

# تعليق استيراد مكتبات PDF مؤقتاً حتى يتم تثبيتها
# from reportlab.lib.pagesizes import A4
# from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
# from reportlab.lib.units import cm
# from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
# from reportlab.lib import colors
# from reportlab.pdfbase import pdfmetrics
# from reportlab.pdfbase.ttfonts import TTFont

class PDFGenerator:
    """فئة توليد ملفات PDF"""
    
    def __init__(self):
        """تهيئة مولد PDF"""
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # تسجيل خط عربي (سيتم تفعيله عند توفر الخط)
            # font_path = os.path.join(os.path.dirname(__file__), 'fonts', 'NotoSansArabic-Regular.ttf')
            # if os.path.exists(font_path):
            #     pdfmetrics.registerFont(TTFont('Arabic', font_path))
            pass
        except Exception as e:
            print(f"تعذر تحميل الخط العربي: {e}")
    
    def generate_audiences_report(self, audiences: List[Dict], date_filter: date = None, 
                                 lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
        """
        توليد تقرير PDF للجلسات
        
        Args:
            audiences: قائمة الجلسات
            date_filter: تاريخ الفلتر
            lawyer_name: اسم المحامي
            
        Returns:
            BytesIO: ملف PDF
        """
        buffer = BytesIO()
        
        try:
            # محاكاة توليد PDF (سيتم استبدالها بالكود الحقيقي)
            report_content = self._generate_mock_pdf_content(audiences, date_filter, lawyer_name)
            buffer.write(report_content.encode('utf-8'))
            buffer.seek(0)
            
        except Exception as e:
            print(f"خطأ في توليد تقرير PDF: {e}")
            # إنشاء محتوى خطأ
            error_content = f"خطأ في توليد التقرير: {e}"
            buffer.write(error_content.encode('utf-8'))
            buffer.seek(0)
        
        return buffer
    
    def _generate_mock_pdf_content(self, audiences: List[Dict], date_filter: date, lawyer_name: str) -> str:
        """توليد محتوى PDF تجريبي"""
        content = f"""
تقرير الجلسات
=============

المحامي: {lawyer_name}
التاريخ: {date_filter.strftime('%d/%m/%Y') if date_filter else 'جميع التواريخ'}
تاريخ التوليد: {datetime.now().strftime('%d/%m/%Y %H:%M')}

الجلسات:
--------
"""
        
        for i, audience in enumerate(audiences, 1):
            content += f"""
{i}. الملف: {audience.get('numero_dossier', 'غير محدد')}
   الموكل: {audience.get('client_nom', 'غير محدد')}
   المحكمة: {audience.get('tribunal', 'غير محدد')}
   التاريخ: {audience.get('date_audience', 'غير محدد')}
   الوقت: {audience.get('heure_audience', '09:00')}
   الموضوع: {audience.get('demande', 'غير محدد')}
   
"""
        
        content += f"\nإجمالي الجلسات: {len(audiences)}"
        return content
    
    def _real_generate_audiences_report(self, audiences: List[Dict], date_filter: date = None, 
                                       lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
        """توليد تقرير PDF حقيقي (سيتم تفعيله لاحقاً)"""
        buffer = BytesIO()
        
        try:
            # إنشاء مستند PDF
            # doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
            #                        topMargin=2*cm, bottomMargin=2*cm)
            
            # إعداد الأنماط
            # styles = getSampleStyleSheet()
            # title_style = ParagraphStyle(
            #     'CustomTitle',
            #     parent=styles['Heading1'],
            #     fontName='Arabic',
            #     fontSize=18,
            #     alignment=2,  # محاذاة يمين
            #     spaceAfter=30
            # )
            
            # header_style = ParagraphStyle(
            #     'CustomHeader',
            #     parent=styles['Heading2'],
            #     fontName='Arabic',
            #     fontSize=14,
            #     alignment=2,
            #     spaceAfter=12
            # )
            
            # normal_style = ParagraphStyle(
            #     'CustomNormal',
            #     parent=styles['Normal'],
            #     fontName='Arabic',
            #     fontSize=12,
            #     alignment=2,
            #     spaceAfter=6
            # )
            
            # بناء المحتوى
            # story = []
            
            # العنوان
            # title = Paragraph("تقرير الجلسات", title_style)
            # story.append(title)
            # story.append(Spacer(1, 12))
            
            # معلومات المحامي والتاريخ
            # info_data = [
            #     ['المحامي:', lawyer_name],
            #     ['التاريخ:', date_filter.strftime('%d/%m/%Y') if date_filter else 'جميع التواريخ'],
            #     ['تاريخ التوليد:', datetime.now().strftime('%d/%m/%Y %H:%M')]
            # ]
            
            # info_table = Table(info_data, colWidths=[4*cm, 8*cm])
            # info_table.setStyle(TableStyle([
            #     ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            #     ('FONTSIZE', (0, 0), (-1, -1), 12),
            #     ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            #     ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            #     ('GRID', (0, 0), (-1, -1), 1, colors.black)
            # ]))
            
            # story.append(info_table)
            # story.append(Spacer(1, 20))
            
            # جدول الجلسات
            # if audiences:
            #     table_data = [['#', 'رقم الملف', 'الموكل', 'المحكمة', 'التاريخ', 'الوقت', 'الموضوع']]
            #     
            #     for i, audience in enumerate(audiences, 1):
            #         row = [
            #             str(i),
            #             audience.get('numero_dossier', ''),
            #             audience.get('client_nom', ''),
            #             audience.get('tribunal', ''),
            #             audience.get('date_audience', ''),
            #             audience.get('heure_audience', ''),
            #             audience.get('demande', '')[:50] + '...' if len(audience.get('demande', '')) > 50 else audience.get('demande', '')
            #         ]
            #         table_data.append(row)
            #     
            #     audiences_table = Table(table_data, colWidths=[1*cm, 3*cm, 3*cm, 2*cm, 2*cm, 1.5*cm, 4*cm])
            #     audiences_table.setStyle(TableStyle([
            #         ('FONTNAME', (0, 0), (-1, -1), 'Arabic'),
            #         ('FONTSIZE', (0, 0), (-1, -1), 10),
            #         ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            #         ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            #         ('GRID', (0, 0), (-1, -1), 1, colors.black),
            #         ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            #         ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            #     ]))
            #     
            #     story.append(audiences_table)
            # else:
            #     no_data = Paragraph("لا توجد جلسات للعرض", normal_style)
            #     story.append(no_data)
            
            # إضافة إجمالي
            # story.append(Spacer(1, 20))
            # total = Paragraph(f"إجمالي الجلسات: {len(audiences)}", header_style)
            # story.append(total)
            
            # بناء المستند
            # doc.build(story)
            
            pass
            
        except Exception as e:
            print(f"خطأ في توليد PDF: {e}")
        
        buffer.seek(0)
        return buffer
    
    def generate_dossier_report(self, dossier: Dict, audiences: List[Dict] = None, 
                               actions: List[Dict] = None) -> BytesIO:
        """
        توليد تقرير PDF لملف محدد
        
        Args:
            dossier: معلومات الملف
            audiences: جلسات الملف
            actions: إجراءات الملف
            
        Returns:
            BytesIO: ملف PDF
        """
        buffer = BytesIO()
        
        try:
            # محاكاة توليد تقرير الملف
            content = f"""
تقرير الملف
===========

رقم الملف: {dossier.get('numero_affaire', 'غير محدد')}
الموكل: {dossier.get('client_nom', 'غير محدد')}
المحكمة: {dossier.get('tribunal', 'غير محدد')}
نوع الملف: {dossier.get('type_dossier', 'غير محدد')}
الحالة: {dossier.get('situation', 'غير محدد')}
تاريخ الفتح: {dossier.get('date_ouverture', 'غير محدد')}

الجلسات:
--------
"""
            
            if audiences:
                for i, audience in enumerate(audiences, 1):
                    content += f"{i}. {audience.get('date_audience')} - {audience.get('statut')}\n"
            else:
                content += "لا توجد جلسات\n"
            
            content += f"\nتاريخ التوليد: {datetime.now().strftime('%d/%m/%Y %H:%M')}"
            
            buffer.write(content.encode('utf-8'))
            buffer.seek(0)
            
        except Exception as e:
            print(f"خطأ في توليد تقرير الملف: {e}")
        
        return buffer

def generate_today_audiences_pdf(audiences: List[Dict], lawyer_name: str = "الأستاذ محمد الأمين") -> BytesIO:
    """
    دالة مساعدة لتوليد PDF جلسات اليوم
    
    Args:
        audiences: قائمة جلسات اليوم
        lawyer_name: اسم المحامي
        
    Returns:
        BytesIO: ملف PDF
    """
    generator = PDFGenerator()
    return generator.generate_audiences_report(audiences, date.today(), lawyer_name)
