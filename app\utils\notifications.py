# -*- coding: utf-8 -*-
"""
وحدة التنبيهات والإشعارات
"""

from datetime import datetime, date, timedelta
from typing import List, Dict, Optional
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class NotificationManager:
    """مدير التنبيهات والإشعارات"""
    
    def __init__(self):
        """تهيئة مدير التنبيهات"""
        self.notification_types = {
            'audience_today': {
                'title': 'جلسات اليوم',
                'icon': 'fas fa-calendar-day',
                'color': 'danger'
            },
            'audience_tomorrow': {
                'title': 'جلسات الغد',
                'icon': 'fas fa-calendar-plus',
                'color': 'warning'
            },
            'audience_upcoming': {
                'title': 'جلسات قادمة',
                'icon': 'fas fa-calendar-week',
                'color': 'info'
            },
            'dossier_urgent': {
                'title': 'ملفات مستعجلة',
                'icon': 'fas fa-exclamation-triangle',
                'color': 'warning'
            },
            'extraction_failed': {
                'title': 'فشل في الاستخراج',
                'icon': 'fas fa-exclamation-circle',
                'color': 'danger'
            },
            'new_action': {
                'title': 'إجراء جديد',
                'icon': 'fas fa-gavel',
                'color': 'success'
            }
        }
    
    def get_all_notifications(self, user_id: Optional[int] = None) -> List[Dict]:
        """الحصول على جميع التنبيهات"""
        notifications = []
        
        try:
            # تنبيهات الجلسات
            notifications.extend(self._get_audience_notifications())
            
            # تنبيهات الملفات المستعجلة
            notifications.extend(self._get_urgent_dossiers_notifications())
            
            # تنبيهات الاستخراج
            notifications.extend(self._get_extraction_notifications())
            
            # ترتيب التنبيهات حسب الأولوية والتاريخ
            notifications.sort(key=lambda x: (x['priority'], x['created_at']), reverse=True)
            
        except Exception as e:
            logger.error(f"خطأ في جلب التنبيهات: {str(e)}")
        
        return notifications
    
    def _get_audience_notifications(self) -> List[Dict]:
        """تنبيهات الجلسات"""
        from app.models.audience import Audience
        
        notifications = []
        
        try:
            # جلسات اليوم
            today_audiences = Audience.get_today_audiences()
            if today_audiences:
                notifications.append({
                    'id': f'audiences_today_{date.today().isoformat()}',
                    'type': 'audience_today',
                    'title': f'لديك {len(today_audiences)} جلسة اليوم',
                    'message': f'جلسات مبرمجة لتاريخ {date.today().strftime("%d/%m/%Y")}',
                    'url': '/audiences/today',
                    'priority': 1,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(today_audiences),
                        'audiences': [a.to_dict() for a in today_audiences[:3]]  # أول 3 جلسات
                    }
                })
            
            # جلسات الغد
            tomorrow = date.today() + timedelta(days=1)
            tomorrow_audiences = Audience.query.filter_by(
                date_audience=tomorrow,
                statut='مبرمجة'
            ).all()
            
            if tomorrow_audiences:
                notifications.append({
                    'id': f'audiences_tomorrow_{tomorrow.isoformat()}',
                    'type': 'audience_tomorrow',
                    'title': f'لديك {len(tomorrow_audiences)} جلسة غداً',
                    'message': f'جلسات مبرمجة لتاريخ {tomorrow.strftime("%d/%m/%Y")}',
                    'url': f'/audiences?date={tomorrow.isoformat()}',
                    'priority': 2,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(tomorrow_audiences),
                        'audiences': [a.to_dict() for a in tomorrow_audiences[:3]]
                    }
                })
            
            # الجلسات القادمة (خلال أسبوع)
            upcoming_audiences = Audience.get_upcoming_audiences(days=7)
            urgent_upcoming = [a for a in upcoming_audiences if a.is_important]
            
            if urgent_upcoming:
                notifications.append({
                    'id': f'audiences_urgent_upcoming_{date.today().isoformat()}',
                    'type': 'audience_upcoming',
                    'title': f'{len(urgent_upcoming)} جلسة مهمة قادمة',
                    'message': 'جلسات مهمة خلال الأسبوع القادم',
                    'url': '/audiences/upcoming',
                    'priority': 3,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(urgent_upcoming),
                        'audiences': [a.to_dict() for a in urgent_upcoming[:3]]
                    }
                })
            
        except Exception as e:
            logger.error(f"خطأ في تنبيهات الجلسات: {str(e)}")
        
        return notifications
    
    def _get_urgent_dossiers_notifications(self) -> List[Dict]:
        """تنبيهات الملفات المستعجلة"""
        from app.models.dossier import Dossier
        
        notifications = []
        
        try:
            urgent_dossiers = Dossier.query.filter_by(
                is_urgent=True,
                is_archived=False
            ).filter(
                Dossier.situation.in_(['في الجلسات', 'في انتظار التعيين'])
            ).all()
            
            if urgent_dossiers:
                notifications.append({
                    'id': f'urgent_dossiers_{date.today().isoformat()}',
                    'type': 'dossier_urgent',
                    'title': f'{len(urgent_dossiers)} ملف مستعجل',
                    'message': 'ملفات تحتاج متابعة عاجلة',
                    'url': '/dossiers?urgent=true',
                    'priority': 2,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(urgent_dossiers),
                        'dossiers': [d.to_dict() for d in urgent_dossiers[:3]]
                    }
                })
            
        except Exception as e:
            logger.error(f"خطأ في تنبيهات الملفات المستعجلة: {str(e)}")
        
        return notifications
    
    def _get_extraction_notifications(self) -> List[Dict]:
        """تنبيهات الاستخراج"""
        from app.models.journal_actions import JournalActions
        
        notifications = []
        
        try:
            # الاستخراجات الفاشلة خلال آخر 24 ساعة
            cutoff = datetime.utcnow() - timedelta(hours=24)
            failed_extractions = JournalActions.query.filter(
                JournalActions.extraction_status == 'failed',
                JournalActions.date_extraction >= cutoff
            ).all()
            
            if failed_extractions:
                notifications.append({
                    'id': f'failed_extractions_{date.today().isoformat()}',
                    'type': 'extraction_failed',
                    'title': f'{len(failed_extractions)} استخراج فاشل',
                    'message': 'فشل في استخراج بيانات من موقع المحاكم',
                    'url': '/dossiers?extraction_failed=true',
                    'priority': 3,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(failed_extractions),
                        'extractions': [e.to_dict() for e in failed_extractions[:3]]
                    }
                })
            
            # الإجراءات الجديدة خلال آخر 24 ساعة
            new_actions = JournalActions.query.filter(
                JournalActions.extraction_status == 'success',
                JournalActions.date_extraction >= cutoff,
                JournalActions.prochaine_audience.isnot(None)
            ).all()
            
            if new_actions:
                notifications.append({
                    'id': f'new_actions_{date.today().isoformat()}',
                    'type': 'new_action',
                    'title': f'{len(new_actions)} إجراء جديد',
                    'message': 'إجراءات جديدة مع جلسات قادمة',
                    'url': '/dossiers?new_actions=true',
                    'priority': 4,
                    'created_at': datetime.utcnow(),
                    'data': {
                        'count': len(new_actions),
                        'actions': [a.to_dict() for a in new_actions[:3]]
                    }
                })
            
        except Exception as e:
            logger.error(f"خطأ في تنبيهات الاستخراج: {str(e)}")
        
        return notifications
    
    def get_notification_count(self, user_id: Optional[int] = None) -> int:
        """عدد التنبيهات"""
        notifications = self.get_all_notifications(user_id)
        return len(notifications)
    
    def format_notification(self, notification: Dict) -> Dict:
        """تنسيق التنبيه للعرض"""
        notification_type = self.notification_types.get(
            notification['type'], 
            self.notification_types['new_action']
        )
        
        return {
            **notification,
            'icon': notification_type['icon'],
            'color': notification_type['color'],
            'formatted_time': self._format_time_ago(notification['created_at'])
        }
    
    def _format_time_ago(self, dt: datetime) -> str:
        """تنسيق الوقت المنقضي"""
        now = datetime.utcnow()
        diff = now - dt
        
        if diff.days > 0:
            return f"منذ {diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"منذ {hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"منذ {minutes} دقيقة"
        else:
            return "الآن"
    
    def send_email_notification(self, notification: Dict, recipients: List[str]) -> bool:
        """إرسال تنبيه بالبريد الإلكتروني"""
        try:
            # سيتم تنفيذ هذا لاحقاً مع إعداد البريد الإلكتروني
            logger.info(f"إرسال تنبيه بالبريد: {notification['title']} إلى {recipients}")
            return True
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
            return False
    
    def create_custom_notification(self, title: str, message: str, 
                                 notification_type: str = 'info',
                                 url: Optional[str] = None,
                                 user_id: Optional[int] = None) -> Dict:
        """إنشاء تنبيه مخصص"""
        return {
            'id': f'custom_{datetime.utcnow().timestamp()}',
            'type': notification_type,
            'title': title,
            'message': message,
            'url': url,
            'priority': 5,
            'created_at': datetime.utcnow(),
            'user_id': user_id,
            'data': {}
        }

# إنشاء مثيل عام
notification_manager = NotificationManager()

def get_user_notifications(user_id: Optional[int] = None) -> List[Dict]:
    """دالة مساعدة للحصول على تنبيهات المستخدم"""
    notifications = notification_manager.get_all_notifications(user_id)
    return [notification_manager.format_notification(n) for n in notifications]

def get_notification_count(user_id: Optional[int] = None) -> int:
    """دالة مساعدة لعدد التنبيهات"""
    return notification_manager.get_notification_count(user_id)
