{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cog me-2"></i>
        إعدادات التنبيهات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للتنبيهات
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <form method="POST">
            <!-- إعدادات أنواع التنبيهات -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>
                        أنواع التنبيهات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="audience_notifications" 
                                       name="audience_notifications" {{ 'checked' if settings.audience_notifications }}>
                                <label class="form-check-label" for="audience_notifications">
                                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    <strong>تنبيهات الجلسات</strong>
                                    <br>
                                    <small class="text-muted">تنبيهات قبل مواعيد الجلسات</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="deadline_notifications" 
                                       name="deadline_notifications" {{ 'checked' if settings.deadline_notifications }}>
                                <label class="form-check-label" for="deadline_notifications">
                                    <i class="fas fa-clock text-warning me-2"></i>
                                    <strong>تنبيهات المواعيد النهائية</strong>
                                    <br>
                                    <small class="text-muted">تنبيهات الاستئناف والطعن</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="response_notifications" 
                                       name="response_notifications" {{ 'checked' if settings.response_notifications }}>
                                <label class="form-check-label" for="response_notifications">
                                    <i class="fas fa-reply text-info me-2"></i>
                                    <strong>تنبيهات الردود المطلوبة</strong>
                                    <br>
                                    <small class="text-muted">تذكير بالردود على المذكرات</small>
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="general_notifications" 
                                       name="general_notifications" {{ 'checked' if settings.general_notifications }}>
                                <label class="form-check-label" for="general_notifications">
                                    <i class="fas fa-bell text-secondary me-2"></i>
                                    <strong>التنبيهات العامة</strong>
                                    <br>
                                    <small class="text-muted">تنبيهات النظام والإشعارات العامة</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات التوقيت -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        إعدادات التوقيت
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="audience_hours_before" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>
                                تنبيه الجلسات (ساعات قبل الموعد)
                            </label>
                            <select class="form-select" id="audience_hours_before" name="audience_hours_before">
                                <option value="1" {{ 'selected' if settings.audience_hours_before == 1 }}>ساعة واحدة</option>
                                <option value="2" {{ 'selected' if settings.audience_hours_before == 2 }}>ساعتين</option>
                                <option value="6" {{ 'selected' if settings.audience_hours_before == 6 }}>6 ساعات</option>
                                <option value="12" {{ 'selected' if settings.audience_hours_before == 12 }}>12 ساعة</option>
                                <option value="24" {{ 'selected' if settings.audience_hours_before == 24 }}>24 ساعة (يوم)</option>
                                <option value="48" {{ 'selected' if settings.audience_hours_before == 48 }}>48 ساعة (يومين)</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="deadline_hours_before" class="form-label">
                                <i class="fas fa-clock me-1"></i>
                                تنبيه المواعيد النهائية (ساعات قبل الموعد)
                            </label>
                            <select class="form-select" id="deadline_hours_before" name="deadline_hours_before">
                                <option value="24" {{ 'selected' if settings.deadline_hours_before == 24 }}>24 ساعة (يوم)</option>
                                <option value="48" {{ 'selected' if settings.deadline_hours_before == 48 }}>48 ساعة (يومين)</option>
                                <option value="72" {{ 'selected' if settings.deadline_hours_before == 72 }}>72 ساعة (3 أيام)</option>
                                <option value="168" {{ 'selected' if settings.deadline_hours_before == 168 }}>أسبوع</option>
                                <option value="336" {{ 'selected' if settings.deadline_hours_before == 336 }}>أسبوعين</option>
                            </select>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <label for="response_hours_before" class="form-label">
                                <i class="fas fa-reply me-1"></i>
                                تنبيه الردود (ساعات قبل الموعد)
                            </label>
                            <select class="form-select" id="response_hours_before" name="response_hours_before">
                                <option value="6" {{ 'selected' if settings.response_hours_before == 6 }}>6 ساعات</option>
                                <option value="12" {{ 'selected' if settings.response_hours_before == 12 }}>12 ساعة</option>
                                <option value="24" {{ 'selected' if settings.response_hours_before == 24 }}>24 ساعة (يوم)</option>
                                <option value="48" {{ 'selected' if settings.response_hours_before == 48 }}>48 ساعة (يومين)</option>
                                <option value="72" {{ 'selected' if settings.response_hours_before == 72 }}>72 ساعة (3 أيام)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إعدادات البريد الإلكتروني -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-envelope me-2"></i>
                        إعدادات البريد الإلكتروني
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>ملاحظة:</strong> تأكد من إدخال بريدك الإلكتروني في الملف الشخصي لتلقي التنبيهات عبر البريد.
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input class="form-check-input" type="checkbox" id="email_notifications" 
                                       name="email_notifications" {{ 'checked' if settings.email_notifications }}>
                                <label class="form-check-label" for="email_notifications">
                                    <strong>تفعيل إشعارات البريد الإلكتروني</strong>
                                    <br>
                                    <small class="text-muted">إرسال التنبيهات عبر البريد الإلكتروني</small>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="email-options" style="display: {{ 'block' if settings.email_notifications else 'none' }};">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_audience" 
                                           name="email_audience" {{ 'checked' if settings.email_audience }}>
                                    <label class="form-check-label" for="email_audience">
                                        <i class="fas fa-calendar-alt text-primary me-1"></i>
                                        تنبيهات الجلسات
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_deadline" 
                                           name="email_deadline" {{ 'checked' if settings.email_deadline }}>
                                    <label class="form-check-label" for="email_deadline">
                                        <i class="fas fa-clock text-warning me-1"></i>
                                        المواعيد النهائية
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="email_response" 
                                           name="email_response" {{ 'checked' if settings.email_response }}>
                                    <label class="form-check-label" for="email_response">
                                        <i class="fas fa-reply text-info me-1"></i>
                                        الردود المطلوبة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- أزرار الحفظ -->
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>
                    حفظ الإعدادات
                </button>
                <a href="{{ url_for('notifications.index') }}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i>
                    إلغاء
                </a>
                <button type="button" class="btn btn-info" onclick="testNotification()">
                    <i class="fas fa-bell me-1"></i>
                    اختبار التنبيه
                </button>
            </div>
        </form>
    </div>

    <div class="col-lg-4">
        <!-- معلومات التنبيهات -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات التنبيهات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary">
                    <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>تنبيهات الجلسات تساعدك في عدم تفويت المواعيد</li>
                        <li>تنبيهات المواعيد النهائية تحميك من فوات آجال الاستئناف</li>
                        <li>تنبيهات الردود تذكرك بإعداد المذكرات في الوقت المناسب</li>
                        <li>يمكنك تخصيص التوقيت حسب احتياجاتك</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تنبيه:</h6>
                    <p class="mb-0">تأكد من تفعيل التنبيهات المهمة لعملك لتجنب فوات المواعيد الحرجة.</p>
                </div>
            </div>
        </div>

        <!-- أنواع التنبيهات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    أنواع التنبيهات
                </h5>
            </div>
            <div class="card-body">
                <div class="notification-types">
                    <div class="notification-type-item">
                        <i class="fas fa-calendar-alt text-primary"></i>
                        <div>
                            <strong>تنبيهات الجلسات</strong>
                            <p>تذكير قبل مواعيد الجلسات بالوقت المحدد</p>
                        </div>
                    </div>
                    
                    <div class="notification-type-item">
                        <i class="fas fa-clock text-warning"></i>
                        <div>
                            <strong>المواعيد النهائية</strong>
                            <p>تنبيهات آجال الاستئناف والطعن والتبليغ</p>
                        </div>
                    </div>
                    
                    <div class="notification-type-item">
                        <i class="fas fa-reply text-info"></i>
                        <div>
                            <strong>الردود المطلوبة</strong>
                            <p>تذكير بإعداد الردود على مذكرات الخصوم</p>
                        </div>
                    </div>
                    
                    <div class="notification-type-item">
                        <i class="fas fa-bell text-secondary"></i>
                        <div>
                            <strong>التنبيهات العامة</strong>
                            <p>إشعارات النظام والتحديثات المهمة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.notification-types {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.notification-type-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.notification-type-item i {
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.notification-type-item strong {
    display: block;
    margin-bottom: 0.25rem;
}

.notification-type-item p {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
}

.form-check-label strong {
    display: block;
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// إظهار/إخفاء خيارات البريد الإلكتروني
document.getElementById('email_notifications').addEventListener('change', function() {
    const emailOptions = document.getElementById('email-options');
    if (this.checked) {
        emailOptions.style.display = 'block';
    } else {
        emailOptions.style.display = 'none';
    }
});

// اختبار التنبيه
function testNotification() {
    if (confirm('هل تريد إرسال تنبيه تجريبي؟')) {
        // يمكن إضافة منطق إرسال تنبيه تجريبي هنا
        alert('تم إرسال تنبيه تجريبي! تحقق من قائمة التنبيهات.');
    }
}

// تحديث الإعدادات تلقائياً عند التغيير
document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
    checkbox.addEventListener('change', function() {
        // يمكن إضافة حفظ تلقائي هنا
        console.log(`تم تغيير ${this.name} إلى ${this.checked}`);
    });
});
</script>
{% endblock %}
