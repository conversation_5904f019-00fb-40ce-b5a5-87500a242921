# -*- coding: utf-8 -*-
"""
نماذج الوثائق والملفات
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, SelectField, DateField, BooleanField, HiddenField
from wtforms.validators import DataRequired, Length, Optional
from wtforms.widgets import TextArea
from app.models.document import DocumentCategory

class DocumentUploadForm(FlaskForm):
    """نموذج رفع وثيقة"""
    
    title = StringField(
        'عنوان الوثيقة',
        validators=[DataRequired(message='عنوان الوثيقة مطلوب'), 
                   Length(min=2, max=200, message='العنوان يجب أن يكون بين 2 و 200 حرف')]
    )
    
    description = TextAreaField(
        'وصف الوثيقة',
        validators=[Optional(), Length(max=1000, message='الوصف لا يجب أن يتجاوز 1000 حرف')],
        widget=TextArea(),
        render_kw={"rows": 3, "placeholder": "وصف مختصر للوثيقة (اختياري)"}
    )
    
    category_id = SelectField(
        'تصنيف الوثيقة',
        validators=[DataRequired(message='تصنيف الوثيقة مطلوب')],
        coerce=int,
        choices=[]
    )
    
    document_date = DateField(
        'تاريخ الوثيقة',
        validators=[Optional()],
        render_kw={"placeholder": "تاريخ إصدار الوثيقة"}
    )
    
    file = FileField(
        'الملف',
        validators=[
            FileRequired(message='يجب اختيار ملف للرفع'),
            FileAllowed(['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'xls', 'xlsx', 'zip', 'rar', '7z'], 
                       message='نوع الملف غير مدعوم')
        ]
    )
    
    is_confidential = BooleanField(
        'وثيقة سرية',
        default=False,
        render_kw={"title": "تحديد الوثيقة كسرية يقيد الوصول إليها"}
    )
    
    is_original = BooleanField(
        'النسخة الأصلية',
        default=False,
        render_kw={"title": "هل هذه النسخة الأصلية من الوثيقة؟"}
    )
    
    # حقول مخفية للربط
    dossier_id = HiddenField()
    client_id = HiddenField()
    audience_id = HiddenField()
    
    def __init__(self, *args, **kwargs):
        super(DocumentUploadForm, self).__init__(*args, **kwargs)
        # تحميل تصنيفات الوثائق
        self.category_id.choices = [(0, 'اختر التصنيف')] + [
            (cat.id, cat.name_ar) for cat in DocumentCategory.query.filter_by(is_active=True).order_by(DocumentCategory.sort_order).all()
        ]

class DocumentEditForm(FlaskForm):
    """نموذج تعديل وثيقة"""
    
    title = StringField(
        'عنوان الوثيقة',
        validators=[DataRequired(message='عنوان الوثيقة مطلوب'), 
                   Length(min=2, max=200, message='العنوان يجب أن يكون بين 2 و 200 حرف')]
    )
    
    description = TextAreaField(
        'وصف الوثيقة',
        validators=[Optional(), Length(max=1000, message='الوصف لا يجب أن يتجاوز 1000 حرف')],
        widget=TextArea(),
        render_kw={"rows": 3}
    )
    
    category_id = SelectField(
        'تصنيف الوثيقة',
        validators=[DataRequired(message='تصنيف الوثيقة مطلوب')],
        coerce=int,
        choices=[]
    )
    
    document_date = DateField(
        'تاريخ الوثيقة',
        validators=[Optional()]
    )
    
    is_confidential = BooleanField(
        'وثيقة سرية',
        default=False
    )
    
    is_original = BooleanField(
        'النسخة الأصلية',
        default=False
    )
    
    def __init__(self, *args, **kwargs):
        super(DocumentEditForm, self).__init__(*args, **kwargs)
        # تحميل تصنيفات الوثائق
        self.category_id.choices = [
            (cat.id, cat.name_ar) for cat in DocumentCategory.query.filter_by(is_active=True).order_by(DocumentCategory.sort_order).all()
        ]

class DocumentCategoryForm(FlaskForm):
    """نموذج إدارة تصنيفات الوثائق"""
    
    name = StringField(
        'الاسم الإنجليزي',
        validators=[DataRequired(message='الاسم الإنجليزي مطلوب'), 
                   Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')]
    )
    
    name_ar = StringField(
        'الاسم العربي',
        validators=[DataRequired(message='الاسم العربي مطلوب'), 
                   Length(min=2, max=100, message='الاسم يجب أن يكون بين 2 و 100 حرف')]
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[Optional(), Length(max=500, message='الوصف لا يجب أن يتجاوز 500 حرف')],
        widget=TextArea(),
        render_kw={"rows": 2}
    )
    
    icon = StringField(
        'الأيقونة',
        validators=[Optional(), Length(max=50)],
        default='fas fa-file',
        render_kw={"placeholder": "fas fa-file"}
    )
    
    color = SelectField(
        'اللون',
        choices=[
            ('primary', 'أزرق'),
            ('secondary', 'رمادي'),
            ('success', 'أخضر'),
            ('danger', 'أحمر'),
            ('warning', 'أصفر'),
            ('info', 'سماوي'),
            ('light', 'فاتح'),
            ('dark', 'داكن')
        ],
        default='primary'
    )
    
    sort_order = StringField(
        'ترتيب العرض',
        validators=[Optional()],
        render_kw={"type": "number", "min": "0", "max": "999"}
    )
    
    is_active = BooleanField(
        'نشط',
        default=True
    )

class DocumentSearchForm(FlaskForm):
    """نموذج البحث في الوثائق"""
    
    search_query = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={"placeholder": "ابحث في العناوين والأوصاف..."}
    )
    
    category_id = SelectField(
        'التصنيف',
        coerce=int,
        choices=[]
    )
    
    file_type = SelectField(
        'نوع الملف',
        choices=[
            ('', 'جميع الأنواع'),
            ('pdf', 'PDF'),
            ('image', 'صور'),
            ('document', 'مستندات'),
            ('spreadsheet', 'جداول بيانات'),
            ('archive', 'ملفات مضغوطة')
        ]
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()]
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()]
    )
    
    is_confidential = SelectField(
        'السرية',
        choices=[
            ('', 'الكل'),
            ('1', 'سرية فقط'),
            ('0', 'غير سرية فقط')
        ]
    )
    
    is_original = SelectField(
        'النسخة الأصلية',
        choices=[
            ('', 'الكل'),
            ('1', 'أصلية فقط'),
            ('0', 'نسخ فقط')
        ]
    )
    
    def __init__(self, *args, **kwargs):
        super(DocumentSearchForm, self).__init__(*args, **kwargs)
        # تحميل تصنيفات الوثائق
        self.category_id.choices = [(0, 'جميع التصنيفات')] + [
            (cat.id, cat.name_ar) for cat in DocumentCategory.query.filter_by(is_active=True).order_by(DocumentCategory.sort_order).all()
        ]

class BulkDocumentActionForm(FlaskForm):
    """نموذج الإجراءات المجمعة على الوثائق"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('delete', 'حذف'),
            ('change_category', 'تغيير التصنيف'),
            ('mark_confidential', 'تحديد كسرية'),
            ('unmark_confidential', 'إلغاء السرية'),
            ('download_zip', 'تحميل كملف مضغوط')
        ],
        validators=[DataRequired(message='يجب اختيار إجراء')]
    )
    
    new_category_id = SelectField(
        'التصنيف الجديد',
        coerce=int,
        choices=[],
        validators=[Optional()]
    )
    
    selected_documents = HiddenField(
        'الوثائق المحددة',
        validators=[DataRequired(message='يجب تحديد وثائق للإجراء')]
    )
    
    def __init__(self, *args, **kwargs):
        super(BulkDocumentActionForm, self).__init__(*args, **kwargs)
        # تحميل تصنيفات الوثائق
        self.new_category_id.choices = [
            (cat.id, cat.name_ar) for cat in DocumentCategory.query.filter_by(is_active=True).order_by(DocumentCategory.sort_order).all()
        ]
