# 📁 دليل نظام إدارة الوثائق

## نظرة عامة

نظام إدارة الوثائق في نظام إدارة مكتب المحامي يوفر حلاً شاملاً لتنظيم وإدارة جميع الوثائق القانونية والملفات المرتبطة بالقضايا والموكلين.

## 🚀 البدء السريع

### 1. تفعيل النظام
```bash
python update_documents.py
```

### 2. الوصول للنظام
- افتح المتصفح واذهب إلى: `http://localhost:5001/documents`
- سجل الدخول بحسابك
- ستجد رابط "الوثائق" في القائمة العلوية

### 3. رفع أول وثيقة
- انقر على "رفع وثيقة جديدة"
- اسحب الملف أو انقر لاختياره
- أدخل العنوان والوصف
- اختر التصنيف المناسب
- انقر "رفع الوثيقة"

## 📋 أنواع الوثائق المدعومة

### 📄 المستندات النصية
- **PDF** (.pdf) - مع معاينة مدمجة
- **Microsoft Word** (.doc, .docx)
- **نصوص عادية** (.txt)
- **Rich Text Format** (.rtf)
- **OpenDocument Text** (.odt)

### 🖼️ الصور
- **JPEG** (.jpg, .jpeg)
- **PNG** (.png)
- **GIF** (.gif)
- **Bitmap** (.bmp)
- **WebP** (.webp)

### 📊 جداول البيانات
- **Microsoft Excel** (.xls, .xlsx)

### 🗜️ ملفات مضغوطة
- **ZIP** (.zip)
- **RAR** (.rar)
- **7-Zip** (.7z)

## 🏷️ التصنيفات

### التصنيفات الافتراضية

1. **⚖️ الأحكام**
   - نسخ من الأحكام والقرارات القضائية
   - أحكام ابتدائية واستئنافية ونقضية

2. **📝 المقالات الافتتاحية**
   - المقالات الافتتاحية للدعاوى
   - طلبات التدخل والإدخال

3. **📢 محاضر التبليغ**
   - محاضر تبليغ الأحكام
   - محاضر إعلان الدعاوى

4. **🆔 الوثائق التعريفية**
   - البطائق الوطنية
   - جوازات السفر
   - شهادات الميلاد

5. **🏛️ الوثائق الرسمية**
   - الشهادات الرسمية
   - الوثائق الحكومية
   - التصاريح والرخص

6. **🤝 العقود والاتفاقيات**
   - عقود البيع والشراء
   - اتفاقيات التسوية
   - عقود العمل

7. **✉️ المراسلات**
   - الخطابات الرسمية
   - المراسلات مع المحاكم
   - التواصل مع الخصوم

8. **🔍 الأدلة والمستندات**
   - الأدلة المؤيدة للدعوى
   - المستندات الداعمة
   - التقارير الفنية

9. **📋 أخرى**
   - وثائق متنوعة أخرى

### إضافة تصنيفات جديدة
- اذهب إلى "إدارة التصنيفات"
- انقر "إضافة تصنيف جديد"
- أدخل الاسم والوصف
- اختر الأيقونة واللون
- حدد ترتيب العرض

## 🔗 الربط والتنظيم

### ربط الوثائق

#### ربط بالملفات
- ربط الوثيقة بملف قضية محدد
- عرض جميع وثائق الملف في مكان واحد
- تنظيم تلقائي حسب نوع الوثيقة

#### ربط بالموكلين
- ربط الوثائق الشخصية بالموكل
- الوثائق التعريفية والعقود الشخصية
- سهولة الوصول لوثائق موكل معين

#### ربط بالجلسات
- ربط الوثائق بجلسات محددة
- المذكرات والأدلة المقدمة في الجلسة
- تنظيم الوثائق حسب مواعيد الجلسات

### التنظيم التلقائي
- تجميع الوثائق حسب التصنيف
- ترتيب حسب تاريخ الرفع أو تاريخ الوثيقة
- فهرسة تلقائية للبحث السريع

## 🔍 البحث والفلترة

### البحث النصي
- البحث في عناوين الوثائق
- البحث في أوصاف الوثائق
- البحث في أسماء الملفات الأصلية

### الفلترة المتقدمة

#### حسب التصنيف
- عرض وثائق تصنيف معين فقط
- تصفح سريع للتصنيفات

#### حسب نوع الملف
- PDF فقط
- صور فقط
- مستندات نصية
- جداول بيانات
- ملفات مضغوطة

#### حسب التاريخ
- تاريخ رفع الوثيقة
- تاريخ الوثيقة نفسها
- فترة زمنية محددة

#### حسب الحالة
- وثائق سرية فقط
- نسخ أصلية فقط
- وثائق عادية

### الترتيب
- الأحدث أولاً
- الأقدم أولاً
- حسب العنوان (أبجدياً)
- حسب الحجم
- حسب التصنيف

## 🔒 إدارة الصلاحيات

### مستويات الوصول

#### المدير (Admin)
- رفع وتعديل وحذف جميع الوثائق
- إدارة التصنيفات
- الوصول للوثائق السرية
- إجراءات مجمعة

#### المحامي (Lawyer)
- رفع وتعديل الوثائق
- الوصول للوثائق السرية
- عرض جميع الوثائق

#### المساعد (Assistant)
- رفع وتعديل الوثائق العادية
- عرض الوثائق غير السرية

#### المشاهد (Viewer)
- عرض الوثائق غير السرية فقط
- تحميل الوثائق المسموحة

### الوثائق السرية
- تحديد الوثائق كسرية عند الرفع
- تقييد الوصول للمستخدمين المخولين
- إشارة بصرية للوثائق السرية

### النسخ الأصلية
- تمييز النسخ الأصلية من النسخ
- أولوية في العرض والبحث
- حماية إضافية من الحذف

## ⚡ الإجراءات المجمعة

### تحديد متعدد
- تحديد وثائق متعددة باستخدام صناديق الاختيار
- تحديد الكل أو إلغاء تحديد الكل
- عداد للوثائق المحددة

### الإجراءات المتاحة

#### حذف متعدد
- حذف عدة وثائق مرة واحدة
- تأكيد قبل الحذف
- حذف الملفات من النظام

#### تغيير التصنيف
- تغيير تصنيف وثائق متعددة
- اختيار التصنيف الجديد
- تحديث فوري

#### إدارة السرية
- تحديد وثائق متعددة كسرية
- إلغاء السرية عن وثائق متعددة
- تحديث الصلاحيات

#### تحميل مضغوط
- تحميل وثائق متعددة كملف ZIP
- تنظيم تلقائي حسب التصنيف
- أسماء ملفات واضحة

## 🛠️ الإدارة والصيانة

### تنظيف الملفات
```python
from app.utils.file_manager import FileManager

# تنظيف الملفات غير المرجعية
referenced_files = [doc.filename for doc in Document.query.all()]
cleaned_count = FileManager.cleanup_orphaned_files('uploads', referenced_files)
```

### إحصائيات التخزين
```python
stats = FileManager.get_storage_stats('uploads')
print(f"إجمالي الملفات: {stats['file_count']}")
print(f"إجمالي الحجم: {stats['total_size_formatted']}")
```

### النسخ الاحتياطي
- نسخ احتياطي دوري لمجلد uploads
- تصدير قاعدة بيانات الوثائق
- استعادة الوثائق عند الحاجة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### الملف لا يرفع
1. تحقق من نوع الملف المدعوم
2. تحقق من حجم الملف (أقل من 50 ميجابايت)
3. تحقق من صلاحيات المجلد uploads

#### الوثيقة لا تظهر
1. تحقق من الفلاتر المطبقة
2. تحقق من صلاحيات المستخدم
3. تحقق من حالة الوثيقة (سرية/عادية)

#### خطأ في التحميل
1. تحقق من وجود الملف في النظام
2. تحقق من صلاحيات القراءة
3. تحقق من مسار الملف في قاعدة البيانات

### ملفات السجلات
- سجلات رفع الملفات في app.log
- سجلات أخطاء النظام
- سجلات الوصول للوثائق

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجلات
3. افتح Issue في GitHub مع تفاصيل المشكلة
4. تواصل مع فريق الدعم

---

**نصيحة**: ابدأ بتنظيم الوثائق حسب التصنيفات، ثم استخدم الربط لتجميع الوثائق ذات الصلة.
