# -*- coding: utf-8 -*-
"""
مسارات الجلسات
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import date, timedelta
from app import db
from app.models.audience import Audience
from app.models.dossier import Dossier
from app.models.type_dossier import TypeDossier
from app.forms.audience import AudienceForm, AudienceSearchForm, AudienceBulkForm
from app.blueprints.audiences import bp

@bp.route('/')
@login_required
def index():
    """قائمة الجلسات"""
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date', '')
    tribunal_filter = request.args.get('tribunal', '')
    type_filter = request.args.get('type', '')
    statut_filter = request.args.get('statut', '')

    query = Audience.query

    # تطبيق الفلاتر
    if date_filter:
        try:
            filter_date = date.fromisoformat(date_filter)
            query = query.filter_by(date_audience=filter_date)
        except ValueError:
            pass

    if tribunal_filter:
        query = query.filter_by(tribunal=tribunal_filter)

    if type_filter:
        query = query.filter_by(type_dossier=type_filter)

    if statut_filter:
        query = query.filter_by(statut=statut_filter)

    audiences = query.order_by(Audience.date_audience.desc(), Audience.heure_audience.asc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # خيارات الفلاتر
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    types_dossier = [t.nom for t in TypeDossier.get_active_types()]
    statuts = ['مبرمجة', 'منعقدة', 'مؤجلة', 'ملغية']

    return render_template('audiences/index.html',
                         title='الجلسات',
                         audiences=audiences,
                         tribunaux=tribunaux,
                         types_dossier=types_dossier,
                         statuts=statuts,
                         date_filter=date_filter,
                         tribunal_filter=tribunal_filter,
                         type_filter=type_filter,
                         statut_filter=statut_filter)

@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """إضافة جلسة جديدة"""
    if not current_user.has_permission('create'):
        flash('ليس لديك صلاحية لإضافة جلسات جديدة', 'error')
        return redirect(url_for('audiences.index'))

    form = AudienceForm()

    # إذا تم تمرير dossier_id في الرابط
    dossier_id = request.args.get('dossier_id', type=int)
    if dossier_id:
        form.dossier_id.data = dossier_id

    if form.validate_on_submit():
        # الحصول على معلومات الملف
        dossier = Dossier.query.get(form.dossier_id.data)

        audience = Audience(
            dossier_id=form.dossier_id.data,
            date_audience=form.date_audience.data,
            heure_audience=form.heure_audience.data,
            reference_interne=form.reference_interne.data,
            client_nom=dossier.client.nom_complet,
            numero_dossier=dossier.numero_affaire,
            tribunal=dossier.tribunal,
            type_dossier=dossier.type_dossier_rel.nom,
            demande=form.demande.data,
            resultat=form.resultat.data,
            statut=form.statut.data,
            is_important=form.is_important.data,
            notes=form.notes.data
        )

        db.session.add(audience)
        db.session.commit()

        flash(f'تم إضافة الجلسة بنجاح', 'success')
        return redirect(url_for('audiences.view', id=audience.id))

    return render_template('audiences/create.html', title='إضافة جلسة جديدة', form=form)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الجلسة"""
    audience = Audience.query.get_or_404(id)

    return render_template('audiences/view.html',
                         title=f'الجلسة: {audience.numero_dossier}',
                         audience=audience)

@bp.route('/today')
@login_required
def today():
    """جلسات اليوم"""
    today_audiences = Audience.get_today_audiences()
    today_date = date.today()

    return render_template('audiences/today.html',
                         title='جلسات اليوم',
                         audiences=today_audiences,
                         today_date=today_date)

@bp.route('/upcoming')
@login_required
def upcoming():
    """الجلسات القادمة"""
    days = request.args.get('days', 7, type=int)
    tribunal_filter = request.args.get('tribunal', '')
    important_filter = request.args.get('important', '', type=str)

    # الحصول على الجلسات القادمة
    upcoming_audiences = Audience.get_upcoming_audiences(days=days)

    # تطبيق الفلاتر
    if tribunal_filter:
        upcoming_audiences = [a for a in upcoming_audiences if a.tribunal == tribunal_filter]

    if important_filter == '1':
        upcoming_audiences = [a for a in upcoming_audiences if a.is_important]

    # قائمة المحاكم للفلتر
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']

    return render_template('audiences/upcoming.html',
                         title='الجلسات القادمة',
                         audiences=upcoming_audiences,
                         days=days,
                         tribunaux=tribunaux,
                         tribunal_filter=tribunal_filter,
                         important_filter=important_filter == '1')

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الجلسة"""
    if not current_user.has_permission('update'):
        flash('ليس لديك صلاحية لتعديل الجلسات', 'error')
        return redirect(url_for('audiences.view', id=id))

    audience = Audience.query.get_or_404(id)
    form = AudienceForm(obj=audience)

    if form.validate_on_submit():
        form.populate_obj(audience)
        db.session.commit()

        flash('تم تحديث الجلسة بنجاح', 'success')
        return redirect(url_for('audiences.view', id=audience.id))

    return render_template('audiences/edit.html',
                         title=f'تعديل الجلسة: {audience.numero_dossier}',
                         form=form,
                         audience=audience)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف الجلسة"""
    if not current_user.has_permission('delete'):
        flash('ليس لديك صلاحية لحذف الجلسات', 'error')
        return redirect(url_for('audiences.view', id=id))

    audience = Audience.query.get_or_404(id)

    db.session.delete(audience)
    db.session.commit()

    flash('تم حذف الجلسة بنجاح', 'success')
    return redirect(url_for('audiences.index'))

@bp.route('/bulk_create', methods=['GET', 'POST'])
@login_required
def bulk_create():
    """إنشاء جلسات متعددة للملفات حسب المحكمة والنوع"""
    if not current_user.has_permission('create'):
        flash('ليس لديك صلاحية لإنشاء جلسات', 'error')
        return redirect(url_for('audiences.index'))

    form = AudienceBulkForm()

    if form.validate_on_submit():
        # الحصول على الملفات المطابقة
        dossiers = Dossier.get_by_tribunal_and_type(
            form.tribunal.data,
            form.type_dossier_id.data
        ).all()

        created_count = 0
        for dossier in dossiers:
            # التحقق من عدم وجود جلسة بنفس التاريخ
            existing = Audience.query.filter_by(
                dossier_id=dossier.id,
                date_audience=form.date_audience.data
            ).first()

            if not existing:
                audience = Audience(
                    dossier_id=dossier.id,
                    date_audience=form.date_audience.data,
                    heure_audience=form.heure_audience.data,
                    client_nom=dossier.client.nom_complet,
                    numero_dossier=dossier.numero_affaire,
                    tribunal=dossier.tribunal,
                    type_dossier=dossier.type_dossier_rel.nom,
                    demande=f"جلسة {form.date_audience.data.strftime('%d/%m/%Y')}",
                    statut='مبرمجة',
                    notes=f"تم إنشاؤها تلقائياً للمحكمة {form.tribunal.data}"
                )

                db.session.add(audience)
                created_count += 1

        db.session.commit()

        flash(f'تم إنشاء {created_count} جلسة بنجاح', 'success')
        return redirect(url_for('audiences.index'))

    return render_template('audiences/bulk_create.html',
                         title='إنشاء جلسات متعددة',
                         form=form)

@bp.route('/print_today')
@login_required
def print_today():
    """طباعة جلسات اليوم"""
    from flask import make_response
    from app.utils.pdf_generator import generate_today_audiences_pdf

    today_audiences = Audience.get_today_audiences()

    # تحويل إلى قاموس للـ PDF generator
    audiences_data = [audience.to_dict() for audience in today_audiences]

    # توليد PDF
    pdf_buffer = generate_today_audiences_pdf(
        audiences_data,
        current_user.nom_complet
    )

    response = make_response(pdf_buffer.read())
    response.headers['Content-Type'] = 'application/pdf'
    today = date.today()
    response.headers['Content-Disposition'] = f'attachment; filename=audiences_{today.strftime("%Y%m%d")}.pdf'

    return response
