# -*- coding: utf-8 -*-
"""
مسارات الجلسات
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import date, timedelta
from app import db
from app.models.audience import Audience
from app.models.dossier import Dossier
from app.models.type_dossier import TypeDossier
from app.blueprints.audiences import bp

@bp.route('/')
@login_required
def index():
    """قائمة الجلسات"""
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date', '')
    tribunal_filter = request.args.get('tribunal', '')
    type_filter = request.args.get('type', '')
    statut_filter = request.args.get('statut', '')
    
    query = Audience.query
    
    # تطبيق الفلاتر
    if date_filter:
        try:
            filter_date = date.fromisoformat(date_filter)
            query = query.filter_by(date_audience=filter_date)
        except ValueError:
            pass
    
    if tribunal_filter:
        query = query.filter_by(tribunal=tribunal_filter)
    
    if type_filter:
        query = query.filter_by(type_dossier=type_filter)
    
    if statut_filter:
        query = query.filter_by(statut=statut_filter)
    
    audiences = query.order_by(Audience.date_audience.desc(), Audience.heure_audience.asc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # خيارات الفلاتر
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    types_dossier = [t.nom for t in TypeDossier.get_active_types()]
    statuts = ['مبرمجة', 'منعقدة', 'مؤجلة', 'ملغية']
    
    return render_template('audiences/index.html',
                         title='الجلسات',
                         audiences=audiences,
                         tribunaux=tribunaux,
                         types_dossier=types_dossier,
                         statuts=statuts,
                         date_filter=date_filter,
                         tribunal_filter=tribunal_filter,
                         type_filter=type_filter,
                         statut_filter=statut_filter)

@bp.route('/create')
@login_required
def create():
    """إضافة جلسة جديدة"""
    if not current_user.has_permission('create'):
        flash('ليس لديك صلاحية لإضافة جلسات جديدة', 'error')
        return redirect(url_for('audiences.index'))
    
    return render_template('audiences/create.html', title='إضافة جلسة جديدة')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الجلسة"""
    audience = Audience.query.get_or_404(id)
    
    return render_template('audiences/view.html',
                         title=f'الجلسة: {audience.numero_dossier}',
                         audience=audience)

@bp.route('/today')
@login_required
def today():
    """جلسات اليوم"""
    today_audiences = Audience.get_today_audiences()
    
    return render_template('audiences/today.html',
                         title='جلسات اليوم',
                         audiences=today_audiences)

@bp.route('/upcoming')
@login_required
def upcoming():
    """الجلسات القادمة"""
    days = request.args.get('days', 7, type=int)
    upcoming_audiences = Audience.get_upcoming_audiences(days=days)
    
    return render_template('audiences/upcoming.html',
                         title='الجلسات القادمة',
                         audiences=upcoming_audiences,
                         days=days)
