# -*- coding: utf-8 -*-
"""
تطبيق إدارة مكتب المحامي
"""

from flask import Flask, request, session
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import LoginManager
from flask_wtf.csrf import CSRFProtect
from config import config

# تهيئة الإضافات
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
csrf = CSRFProtect()

def create_app(config_name='default'):
    """إنشاء وتكوين التطبيق"""
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # تهيئة الإضافات
    db.init_app(app)
    migrate.init_app(app, db)
    csrf.init_app(app)

    # تكوين Flask-Login
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(int(user_id))

    # تسجيل Blueprints
    from app.blueprints.auth import bp as auth_bp
    app.register_blueprint(auth_bp, url_prefix='/auth')

    from app.blueprints.dashboard import bp as dashboard_bp
    app.register_blueprint(dashboard_bp, url_prefix='/')

    from app.blueprints.clients import bp as clients_bp
    app.register_blueprint(clients_bp, url_prefix='/clients')

    from app.blueprints.dossiers import bp as dossiers_bp
    app.register_blueprint(dossiers_bp, url_prefix='/dossiers')

    from app.blueprints.audiences import bp as audiences_bp
    app.register_blueprint(audiences_bp, url_prefix='/audiences')

    from app.blueprints.admin import bp as admin_bp
    app.register_blueprint(admin_bp, url_prefix='/admin')

    from app.blueprints.notifications import bp as notifications_bp
    app.register_blueprint(notifications_bp, url_prefix='/notifications')

    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        from flask import render_template
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        from flask import render_template
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # Context processors
    @app.context_processor
    def inject_conf_vars():
        from flask_login import current_user

        context = {
            'LANGUAGES': app.config['LANGUAGES'],
            'CURRENT_LANGUAGE': session.get('language', app.config['DEFAULT_LANGUAGE'])
        }

        # إضافة عدد التنبيهات غير المقروءة
        if current_user.is_authenticated:
            from app.models.notification import Notification
            unread_notifications = Notification.query.filter_by(
                user_id=current_user.id,
                is_read=False
            ).count()
            context['unread_notifications_count'] = unread_notifications

        return context

    # Before request handlers
    @app.before_request
    def before_request():
        # تحديد اللغة
        if 'language' not in session:
            session['language'] = app.config['DEFAULT_LANGUAGE']

    return app

# استيراد النماذج لضمان إنشاء الجداول
from app.models import user, client, type_dossier, dossier, audience, journal_actions, notification
