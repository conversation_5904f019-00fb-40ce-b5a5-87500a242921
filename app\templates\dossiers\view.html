{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        {{ dossier.numero_affaire }}
        {% if dossier.is_urgent %}
        <span class="badge bg-danger ms-2">مستعجل</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('update') %}
            <a href="{{ url_for('dossiers.edit', id=dossier.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <button type="button" class="btn btn-success" onclick="extractActions()">
                <i class="fas fa-download me-1"></i>
                جلب الإجراء
            </button>
            {% endif %}
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('audiences.create') }}?dossier_id={{ dossier.id }}" class="btn btn-info">
                <i class="fas fa-calendar-plus me-1"></i>
                إضافة جلسة
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات الملف -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الملف
                </h5>
            </div>
            <div class="card-body">
                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-hashtag text-primary"></i>
                        <div>
                            <strong>رقم الملف</strong>
                            <p>{{ dossier.numero_affaire }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-university text-info"></i>
                        <div>
                            <strong>المحكمة</strong>
                            <p>{{ dossier.tribunal }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-tags text-success"></i>
                        <div>
                            <strong>نوع الملف</strong>
                            <p>
                                {% if dossier.type_dossier_rel %}
                                <span class="badge" style="background-color: {{ dossier.type_dossier_rel.couleur }}">
                                    {{ dossier.type_dossier_rel.nom }}
                                </span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-info-circle text-{{ dossier.get_status_color() }}"></i>
                        <div>
                            <strong>الحالة</strong>
                            <p>
                                <span class="badge bg-{{ dossier.get_status_color() }}">
                                    {{ dossier.situation }}
                                </span>
                            </p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-calendar text-primary"></i>
                        <div>
                            <strong>تاريخ الفتح</strong>
                            <p>{{ dossier.date_ouverture.strftime('%d/%m/%Y') if dossier.date_ouverture else 'غير محدد' }}</p>
                        </div>
                    </div>

                    {% if dossier.date_cloture %}
                    <div class="info-item">
                        <i class="fas fa-calendar-times text-danger"></i>
                        <div>
                            <strong>تاريخ الإغلاق</strong>
                            <p>{{ dossier.date_cloture.strftime('%d/%m/%Y') }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if dossier.adversaire %}
                    <div class="info-item">
                        <i class="fas fa-user-times text-warning"></i>
                        <div>
                            <strong>الطرف المقابل</strong>
                            <p>{{ dossier.adversaire }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if dossier.montant_litige %}
                    <div class="info-item">
                        <i class="fas fa-money-bill text-success"></i>
                        <div>
                            <strong>مبلغ النزاع</strong>
                            <p>{{ "{:,.2f}".format(dossier.montant_litige) }} درهم</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if dossier.honoraires %}
                    <div class="info-item">
                        <i class="fas fa-coins text-warning"></i>
                        <div>
                            <strong>الأتعاب</strong>
                            <p>{{ "{:,.2f}".format(dossier.honoraires) }} درهم</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if dossier.objet %}
                <div class="mt-4">
                    <h6><i class="fas fa-file-alt me-1"></i> موضوع الملف</h6>
                    <div class="alert alert-light">
                        {{ dossier.objet }}
                    </div>
                </div>
                {% endif %}

                {% if dossier.notes %}
                <div class="mt-4">
                    <h6><i class="fas fa-sticky-note me-1"></i> ملاحظات</h6>
                    <div class="alert alert-info">
                        {{ dossier.notes }}
                    </div>
                </div>
                {% endif %}

                <div class="mt-4 text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    تم الإنشاء: {{ dossier.created_at.strftime('%d/%m/%Y %H:%M') if dossier.created_at else '' }}
                </div>
            </div>
        </div>

        <!-- معلومات الموكل -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    الموكل
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-large mx-auto mb-2">
                        {{ dossier.client.nom_complet[0] }}
                    </div>
                    <h6 class="mb-1">
                        <a href="{{ url_for('clients.view', id=dossier.client.id) }}" class="text-decoration-none">
                            {{ dossier.client.nom_complet }}
                        </a>
                    </h6>
                    {% if dossier.client.profession %}
                    <small class="text-muted">{{ dossier.client.profession }}</small>
                    {% endif %}
                </div>

                {% if dossier.client.telephone %}
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-phone text-success me-2"></i>
                    <span>{{ dossier.client.telephone }}</span>
                </div>
                {% endif %}

                {% if dossier.client.email %}
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-envelope text-primary me-2"></i>
                    <span>{{ dossier.client.email }}</span>
                </div>
                {% endif %}

                <div class="text-center mt-3">
                    <a href="{{ url_for('clients.view', id=dossier.client.id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>
                        عرض التفاصيل
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- الجلسات والإجراءات -->
    <div class="col-lg-8 mb-4">
        <!-- تبويبات -->
        <ul class="nav nav-tabs" id="dossierTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="audiences-tab" data-bs-toggle="tab"
                        data-bs-target="#audiences" type="button" role="tab">
                    <i class="fas fa-calendar-alt me-1"></i>
                    الجلسات ({{ audiences|length }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="actions-tab" data-bs-toggle="tab"
                        data-bs-target="#actions" type="button" role="tab">
                    <i class="fas fa-list-alt me-1"></i>
                    الإجراءات ({{ journal_actions|length }})
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="procedures-tab" data-bs-toggle="tab"
                        data-bs-target="#procedures" type="button" role="tab">
                    <i class="fas fa-download me-1"></i>
                    الإجراءات المستخرجة
                </button>
            </li>
        </ul>

        <div class="tab-content" id="dossierTabsContent">
            <!-- تبويب الجلسات -->
            <div class="tab-pane fade show active" id="audiences" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            الجلسات ({{ audiences|length }})
                        </h5>
                        {% if current_user.has_permission('create') %}
                        <a href="{{ url_for('audiences.create') }}?dossier_id={{ dossier.id }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة جلسة
                        </a>
                        {% endif %}
                    </div>
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    الجلسات ({{ audiences|length }})
                </h5>
                {% if current_user.has_permission('create') %}
                <a href="{{ url_for('audiences.create') }}?dossier_id={{ dossier.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة جلسة
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if audiences %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>الوقت</th>
                                    <th>الموضوع</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for audience in audiences %}
                                <tr class="{{ audience.get_urgency_class() }}">
                                    <td>
                                        <strong>{{ audience.date_audience.strftime('%d/%m/%Y') }}</strong>
                                        {% if audience.is_important %}
                                        <i class="fas fa-star text-warning ms-1" title="جلسة مهمة"></i>
                                        {% endif %}
                                    </td>
                                    <td>{{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}</td>
                                    <td>
                                        {% if audience.demande %}
                                        {{ audience.demande[:50] }}{% if audience.demande|length > 50 %}...{% endif %}
                                        {% else %}
                                        <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ audience.get_status_color() }}">
                                            {{ audience.statut }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('audiences.view', id=audience.id) }}"
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_permission('update') %}
                                            <a href="{{ url_for('audiences.edit', id=audience.id) }}"
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد جلسات لهذا الملف</h6>
                        <p class="text-muted">ابدأ بإضافة جلسة جديدة</p>
                        {% if current_user.has_permission('create') %}
                        <a href="{{ url_for('audiences.create') }}?dossier_id={{ dossier.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة جلسة جديدة
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
                </div>
            </div>

            <!-- تبويب الإجراءات -->
            <div class="tab-pane fade" id="actions" role="tabpanel">
                <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    آخر الإجراءات ({{ journal_actions|length }})
                </h5>
                {% if current_user.has_permission('update') %}
                <button type="button" class="btn btn-sm btn-success" onclick="extractActions()">
                    <i class="fas fa-download me-1"></i>
                    جلب الإجراء
                </button>
                {% endif %}
            </div>
            <div class="card-body">
                {% if journal_actions %}
                    {% for action in journal_actions %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="card-title">
                                        <i class="fas fa-gavel me-1"></i>
                                        {{ action.type_procedure or 'إجراء عام' }}
                                        <span class="badge bg-{{ action.get_status_color() }} ms-2">
                                            {{ action.extraction_status }}
                                        </span>
                                    </h6>
                                    {% if action.decision %}
                                    <p class="card-text">{{ action.format_decision() }}</p>
                                    {% endif %}

                                    <div class="row text-muted small">
                                        {% if action.date_decision %}
                                        <div class="col-md-6">
                                            <i class="fas fa-calendar me-1"></i>
                                            تاريخ القرار: {{ action.date_decision.strftime('%d/%m/%Y') }}
                                        </div>
                                        {% endif %}
                                        {% if action.prochaine_audience %}
                                        <div class="col-md-6">
                                            <i class="fas fa-calendar-plus me-1"></i>
                                            الجلسة القادمة: {{ action.prochaine_audience.strftime('%d/%m/%Y') }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ action.date_extraction.strftime('%d/%m/%Y %H:%M') if action.date_extraction else '' }}
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد إجراءات مستخرجة</h6>
                        <p class="text-muted">استخرج آخر الإجراءات من موقع المحاكم</p>
                        {% if current_user.has_permission('update') %}
                        <button type="button" class="btn btn-success" onclick="extractActions()">
                            <i class="fas fa-download me-1"></i>
                            جلب الإجراء الآن
                        </button>
                        {% endif %}
                    </div>
                {% endif %}
            </div>

            <!-- تبويب الإجراءات المستخرجة -->
            <div class="tab-pane fade" id="procedures" role="tabpanel">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            الإجراءات المستخرجة من المحاكم
                        </h5>
                        <div class="btn-group">
                            {% if current_user.has_permission('update') %}
                            <button type="button" class="btn btn-sm btn-success" onclick="extractActions()">
                                <i class="fas fa-download me-1"></i>
                                استخراج جديد
                            </button>
                            {% endif %}
                            <a href="{{ url_for('extraction.procedures', dossier_id=dossier.id) }}"
                               class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye me-1"></i>
                                عرض الكل
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="extractedProcedures">
                            <div class="text-center py-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">جاري التحميل...</span>
                                </div>
                                <p class="mt-2 text-muted">جاري تحميل الإجراءات المستخرجة...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Extract Actions Modal -->
<div class="modal fade" id="extractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استخراج الإجراءات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>سيتم استخراج آخر الإجراءات والقرارات من موقع المحاكم المغربية للملف:</p>
                <div class="alert alert-info">
                    <strong>{{ dossier.numero_affaire }}</strong> - {{ dossier.tribunal }}
                </div>
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status" id="extractSpinner" style="display: none;">
                        <span class="visually-hidden">جاري الاستخراج...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('extraction.extract_dossier', dossier_id=dossier.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-success" id="extractBtn">
                        <i class="fas fa-download me-1"></i>
                        استخراج الآن
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-large {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.5rem;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function extractActions() {
    const modal = new bootstrap.Modal(document.getElementById('extractModal'));
    modal.show();

    // إظهار spinner عند الإرسال
    document.querySelector('#extractModal form').addEventListener('submit', function() {
        document.getElementById('extractSpinner').style.display = 'block';
        document.getElementById('extractBtn').disabled = true;
        document.getElementById('extractBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستخراج...';
    });
}

// تحميل الإجراءات المستخرجة عند فتح التبويب
document.addEventListener('DOMContentLoaded', function() {
    const proceduresTab = document.getElementById('procedures-tab');
    if (proceduresTab) {
        proceduresTab.addEventListener('shown.bs.tab', function() {
            loadExtractedProcedures();
        });
    }

    // تحميل الإجراءات إذا كان التبويب مفتوح من البداية
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('tab') === 'procedures') {
        // تفعيل التبويب
        const tab = new bootstrap.Tab(proceduresTab);
        tab.show();
        loadExtractedProcedures();
    }
});

function loadExtractedProcedures() {
    const container = document.getElementById('extractedProcedures');

    fetch(`/extraction/procedures/{{ dossier.id }}?partial=1`)
        .then(response => {
            if (!response.ok) {
                throw new Error('فشل في تحميل البيانات');
            }
            return response.text();
        })
        .then(html => {
            container.innerHTML = html;
        })
        .catch(error => {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6 class="text-muted">خطأ في تحميل الإجراءات</h6>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-outline-primary" onclick="loadExtractedProcedures()">
                        <i class="fas fa-redo me-1"></i>
                        إعادة المحاولة
                    </button>
                </div>
            `;
        });
}
</script>
{% endblock %}
