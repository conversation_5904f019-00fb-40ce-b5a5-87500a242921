{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-plus me-2"></i>
        إضافة جلسة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    بيانات الجلسة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="dossier_id" class="form-label">
                                <i class="fas fa-folder me-1"></i>
                                الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.dossier_id(class="form-select") }}
                            {% if form.dossier_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.dossier_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                <a href="{{ url_for('dossiers.create') }}" target="_blank">إضافة ملف جديد</a>
                            </small>
                        </div>

                        <!-- تاريخ الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="date_audience" class="form-label">
                                <i class="fas fa-calendar-day me-1"></i>
                                تاريخ الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.date_audience(class="form-control") }}
                            {% if form.date_audience.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_audience.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- وقت الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="heure_audience" class="form-label">
                                <i class="fas fa-clock me-1"></i>
                                وقت الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.heure_audience(class="form-control") }}
                            {% if form.heure_audience.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.heure_audience.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">مثال: 09:30</small>
                        </div>

                        <!-- المرجع الداخلي -->
                        <div class="col-md-6 mb-3">
                            <label for="reference_interne" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                المرجع الداخلي
                            </label>
                            {{ form.reference_interne(class="form-control") }}
                            {% if form.reference_interne.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.reference_interne.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">مرجع داخلي للمتابعة</small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- حالة الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-info-circle me-1"></i>
                                حالة الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.statut(class="form-select") }}
                            {% if form.statut.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.statut.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- جلسة مهمة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="form-check">
                                {{ form.is_important(class="form-check-input") }}
                                <label class="form-check-label" for="is_important">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    جلسة مهمة
                                </label>
                            </div>
                            <small class="form-text text-muted">ستظهر بلون مميز في القائمة</small>
                        </div>
                    </div>

                    <!-- موضوع الجلسة -->
                    <div class="mb-3">
                        <label for="demande" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            موضوع الجلسة / الطلب
                        </label>
                        {{ form.demande(class="form-control") }}
                        {% if form.demande.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.demande.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">وصف مختصر لموضوع الجلسة أو الطلب المقدم</small>
                    </div>

                    <!-- نتيجة الجلسة -->
                    <div class="mb-3">
                        <label for="resultat" class="form-label">
                            <i class="fas fa-clipboard-check me-1"></i>
                            نتيجة الجلسة
                        </label>
                        {{ form.resultat(class="form-control") }}
                        {% if form.resultat.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.resultat.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">يمكن ملؤها بعد انعقاد الجلسة</small>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الجلسة
                        </button>
                        <a href="{{ url_for('audiences.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-info" onclick="fillSampleData()">
                            <i class="fas fa-magic me-1"></i>
                            بيانات تجريبية
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الملف المحدد -->
        <div class="card mb-4" id="dossierInfo" style="display: none;">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    معلومات الملف
                </h5>
            </div>
            <div class="card-body" id="dossierDetails">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
        </div>

        <!-- إرشادات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة</li>
                        <li>تأكد من صحة تاريخ ووقت الجلسة</li>
                        <li>اختر الملف المناسب بعناية</li>
                        <li>يمكن تعديل البيانات لاحقاً</li>
                        <li>الجلسات المهمة تظهر بلون مميز</li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-calendar-check me-1"></i> بعد الحفظ:</h6>
                    <ul class="mb-0">
                        <li>ستظهر الجلسة في القائمة الرئيسية</li>
                        <li>ستحصل على تنبيهات قبل موعدها</li>
                        <li>يمكن طباعة جدول الجلسات</li>
                        <li>يمكن ربطها بالإجراءات المستخرجة</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-clock me-1"></i> تذكير:</h6>
                    <p class="mb-0">تأكد من مراجعة جلسات اليوم كل صباح وتحضير الملفات المطلوبة مسبقاً.</p>
                </div>
            </div>
        </div>

        <!-- اختصارات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    اختصارات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('audiences.today') }}" class="btn btn-outline-danger btn-sm">
                        <i class="fas fa-calendar-day me-1"></i>
                        جلسات اليوم
                    </a>
                    <a href="{{ url_for('audiences.upcoming') }}" class="btn btn-outline-warning btn-sm">
                        <i class="fas fa-calendar-week me-1"></i>
                        الجلسات القادمة
                    </a>
                    <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-folder me-1"></i>
                        قائمة الملفات
                    </a>
                    <a href="{{ url_for('audiences.bulk_create') }}" class="btn btn-outline-success btn-sm">
                        <i class="fas fa-layer-group me-1"></i>
                        إنشاء جلسات متعددة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث معلومات الملف عند تغيير الاختيار
document.addEventListener('DOMContentLoaded', function() {
    const dossierSelect = document.getElementById('dossier_id');
    const dossierInfo = document.getElementById('dossierInfo');
    const dossierDetails = document.getElementById('dossierDetails');
    
    // تعيين التاريخ الحالي كافتراضي
    const dateInput = document.getElementById('date_audience');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
    
    // تعيين الوقت الافتراضي
    const timeInput = document.getElementById('heure_audience');
    if (!timeInput.value) {
        timeInput.value = '09:00';
    }
    
    // معالج تغيير الملف
    dossierSelect.addEventListener('change', function() {
        const dossierId = this.value;
        
        if (dossierId) {
            // محاكاة جلب معلومات الملف (يمكن استبدالها بـ AJAX)
            fetch(`/api/dossiers/${dossierId}`)
                .then(response => response.json())
                .then(data => {
                    dossierDetails.innerHTML = `
                        <div class="info-list">
                            <div class="info-item">
                                <i class="fas fa-hashtag text-primary"></i>
                                <div>
                                    <strong>رقم الملف</strong>
                                    <p>${data.numero_affaire}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-user text-info"></i>
                                <div>
                                    <strong>الموكل</strong>
                                    <p>${data.client_nom}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-university text-success"></i>
                                <div>
                                    <strong>المحكمة</strong>
                                    <p>${data.tribunal}</p>
                                </div>
                            </div>
                            <div class="info-item">
                                <i class="fas fa-tags text-warning"></i>
                                <div>
                                    <strong>نوع الملف</strong>
                                    <p>${data.type_dossier}</p>
                                </div>
                            </div>
                        </div>
                    `;
                    dossierInfo.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error:', error);
                    dossierInfo.style.display = 'none';
                });
        } else {
            dossierInfo.style.display = 'none';
        }
    });
    
    // إذا كان هناك ملف محدد مسبقاً، اعرض معلوماته
    if (dossierSelect.value) {
        dossierSelect.dispatchEvent(new Event('change'));
    }
});

function fillSampleData() {
    // ملء بيانات تجريبية
    document.getElementById('demande').value = 'جلسة للنظر في الطلب الأساسي';
    document.getElementById('notes').value = 'جلسة تجريبية - يرجى تحديث البيانات';
    
    // تعيين وقت افتراضي
    const timeInput = document.getElementById('heure_audience');
    if (!timeInput.value) {
        timeInput.value = '09:30';
    }
    
    alert('تم ملء البيانات التجريبية. يرجى مراجعتها وتعديلها حسب الحاجة.');
}

// التحقق من صحة الوقت
document.getElementById('heure_audience').addEventListener('change', function() {
    const time = this.value;
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    
    if (time && !timeRegex.test(time)) {
        alert('يرجى إدخال وقت صحيح بالصيغة HH:MM (مثال: 09:30)');
        this.focus();
    }
});

// التحقق من التاريخ
document.getElementById('date_audience').addEventListener('change', function() {
    const selectedDate = new Date(this.value);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (selectedDate < today) {
        if (!confirm('التاريخ المحدد في الماضي. هل تريد المتابعة؟')) {
            this.value = '';
        }
    }
});
</script>
{% endblock %}

{% block extra_css %}
<style>
.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}
