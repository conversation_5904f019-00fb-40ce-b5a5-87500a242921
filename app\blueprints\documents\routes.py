# -*- coding: utf-8 -*-
"""
مسارات إدارة الوثائق
"""

import os
import uuid
import zipfile
from datetime import datetime
from flask import render_template, request, redirect, url_for, flash, jsonify, send_file, abort, current_app
from flask_login import login_required, current_user
from werkzeug.utils import secure_filename
from sqlalchemy import or_, and_

from app.blueprints.documents import bp
from app import db
from app.models.document import Document, DocumentCategory
from app.models.dossier import Dossier
from app.models.client import Client
from app.models.audience import Audience
from app.forms.document_forms import (
    DocumentUploadForm, DocumentEditForm, DocumentCategoryForm,
    DocumentSearchForm, BulkDocumentActionForm
)
# from app.utils.decorators import permission_required

def permission_required(permission):
    """ديكوريتر مؤقت للصلاحيات"""
    def decorator(f):
        from functools import wraps
        @wraps(f)
        def decorated_function(*args, **kwargs):
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@bp.route('/')
@login_required
def index():
    """صفحة الوثائق الرئيسية"""

    # نموذج البحث
    search_form = DocumentSearchForm()

    # بناء الاستعلام
    query = Document.query

    # تطبيق الفلاتر
    if request.args.get('search_query'):
        search_term = f"%{request.args.get('search_query')}%"
        query = query.filter(
            or_(
                Document.title.like(search_term),
                Document.description.like(search_term),
                Document.original_filename.like(search_term)
            )
        )
        search_form.search_query.data = request.args.get('search_query')

    if request.args.get('category_id') and int(request.args.get('category_id')) > 0:
        query = query.filter(Document.category_id == int(request.args.get('category_id')))
        search_form.category_id.data = int(request.args.get('category_id'))

    if request.args.get('file_type'):
        file_type = request.args.get('file_type')
        if file_type == 'pdf':
            query = query.filter(Document.file_extension == '.pdf')
        elif file_type == 'image':
            query = query.filter(Document.file_extension.in_(['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']))
        elif file_type == 'document':
            query = query.filter(Document.file_extension.in_(['.doc', '.docx', '.txt', '.rtf', '.odt']))
        elif file_type == 'spreadsheet':
            query = query.filter(Document.file_extension.in_(['.xls', '.xlsx']))
        elif file_type == 'archive':
            query = query.filter(Document.file_extension.in_(['.zip', '.rar', '.7z']))
        search_form.file_type.data = file_type

    if request.args.get('date_from'):
        try:
            date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
            query = query.filter(Document.document_date >= date_from)
            search_form.date_from.data = date_from
        except ValueError:
            pass

    if request.args.get('date_to'):
        try:
            date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
            query = query.filter(Document.document_date <= date_to)
            search_form.date_to.data = date_to
        except ValueError:
            pass

    if request.args.get('is_confidential'):
        is_confidential = request.args.get('is_confidential') == '1'
        query = query.filter(Document.is_confidential == is_confidential)
        search_form.is_confidential.data = request.args.get('is_confidential')

    if request.args.get('is_original'):
        is_original = request.args.get('is_original') == '1'
        query = query.filter(Document.is_original == is_original)
        search_form.is_original.data = request.args.get('is_original')

    # ترتيب النتائج
    sort_by = request.args.get('sort', 'created_at')
    sort_order = request.args.get('order', 'desc')

    if sort_by == 'title':
        query = query.order_by(Document.title.desc() if sort_order == 'desc' else Document.title.asc())
    elif sort_by == 'category':
        query = query.join(DocumentCategory).order_by(
            DocumentCategory.name_ar.desc() if sort_order == 'desc' else DocumentCategory.name_ar.asc()
        )
    elif sort_by == 'size':
        query = query.order_by(Document.file_size.desc() if sort_order == 'desc' else Document.file_size.asc())
    elif sort_by == 'document_date':
        query = query.order_by(Document.document_date.desc() if sort_order == 'desc' else Document.document_date.asc())
    else:  # created_at
        query = query.order_by(Document.created_at.desc() if sort_order == 'desc' else Document.created_at.asc())

    # تطبيق التصفح
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('DOCUMENTS_PER_PAGE', 20)

    documents = query.paginate(
        page=page, per_page=per_page, error_out=False
    )

    # إحصائيات
    total_documents = Document.query.count()
    total_size = db.session.query(db.func.sum(Document.file_size)).scalar() or 0
    categories_count = DocumentCategory.query.filter_by(is_active=True).count()

    # تصنيفات للعرض السريع
    categories = DocumentCategory.query.filter_by(is_active=True).order_by(DocumentCategory.sort_order).all()

    return render_template(
        'documents/index.html',
        documents=documents,
        search_form=search_form,
        categories=categories,
        total_documents=total_documents,
        total_size=total_size,
        total_size_formatted=format_file_size(total_size),
        categories_count=categories_count,
        current_sort=sort_by,
        current_order=sort_order
    )

@bp.route('/upload', methods=['GET', 'POST'])
@login_required
@permission_required('create_document')
def upload():
    """رفع وثيقة جديدة"""

    form = DocumentUploadForm()

    # الحصول على معرفات الربط من الرابط
    dossier_id = request.args.get('dossier_id', type=int)
    client_id = request.args.get('client_id', type=int)
    audience_id = request.args.get('audience_id', type=int)

    # التحقق من صحة الربط
    dossier = None
    client = None
    audience = None

    if dossier_id:
        dossier = Dossier.query.get_or_404(dossier_id)
        form.dossier_id.data = dossier_id
        if not client_id:
            client_id = dossier.client_id

    if client_id:
        client = Client.query.get_or_404(client_id)
        form.client_id.data = client_id

    if audience_id:
        audience = Audience.query.get_or_404(audience_id)
        form.audience_id.data = audience_id
        if not dossier_id and audience.dossier_id:
            dossier_id = audience.dossier_id
            dossier = audience.dossier
            form.dossier_id.data = dossier_id

    if form.validate_on_submit():
        try:
            # رفع الملف
            file = form.file.data
            if file and Document.allowed_file(file.filename):

                # إنشاء اسم ملف آمن وفريد
                original_filename = secure_filename(file.filename)
                file_extension = Document.get_file_extension(original_filename)
                unique_filename = f"{uuid.uuid4().hex}{file_extension}"

                # إنشاء مجلد الرفع إذا لم يكن موجوداً
                upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
                documents_folder = os.path.join(upload_folder, 'documents')

                if not os.path.exists(documents_folder):
                    os.makedirs(documents_folder)

                # حفظ الملف
                file_path = os.path.join(documents_folder, unique_filename)
                file.save(file_path)

                # الحصول على معلومات الملف
                file_size = os.path.getsize(file_path)
                file_type = file.content_type

                # إنشاء سجل الوثيقة
                document = Document(
                    title=form.title.data,
                    description=form.description.data,
                    filename=unique_filename,
                    original_filename=original_filename,
                    file_path=file_path,
                    file_size=file_size,
                    file_type=file_type,
                    file_extension=file_extension,
                    category_id=form.category_id.data,
                    dossier_id=dossier_id,
                    client_id=client_id,
                    audience_id=audience_id,
                    document_date=form.document_date.data,
                    is_confidential=form.is_confidential.data,
                    is_original=form.is_original.data,
                    uploaded_by=current_user.id
                )

                db.session.add(document)
                db.session.commit()

                flash('تم رفع الوثيقة بنجاح', 'success')

                # إعادة التوجيه حسب المصدر
                if dossier_id:
                    return redirect(url_for('dossiers.view', id=dossier_id, tab='documents'))
                elif client_id:
                    return redirect(url_for('clients.view', id=client_id, tab='documents'))
                else:
                    return redirect(url_for('documents.index'))

            else:
                flash('نوع الملف غير مدعوم', 'error')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء رفع الوثيقة: {str(e)}', 'error')

    return render_template(
        'documents/upload.html',
        form=form,
        dossier=dossier,
        client=client,
        audience=audience
    )

@bp.route('/view/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل وثيقة"""

    document = Document.query.get_or_404(id)

    # التحقق من الصلاحيات
    if document.is_confidential and not current_user.can('view_confidential_documents'):
        abort(403)

    return render_template('documents/view.html', document=document)

@bp.route('/download/<int:id>')
@login_required
def download(id):
    """تحميل وثيقة"""

    document = Document.query.get_or_404(id)

    # التحقق من الصلاحيات
    if document.is_confidential and not current_user.can('view_confidential_documents'):
        abort(403)

    # التحقق من وجود الملف
    if not os.path.exists(document.file_path):
        flash('الملف غير موجود', 'error')
        return redirect(url_for('documents.index'))

    return send_file(
        document.file_path,
        as_attachment=True,
        download_name=document.original_filename
    )

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@permission_required('edit_document')
def edit(id):
    """تعديل وثيقة"""

    document = Document.query.get_or_404(id)
    form = DocumentEditForm(obj=document)

    if form.validate_on_submit():
        try:
            document.title = form.title.data
            document.description = form.description.data
            document.category_id = form.category_id.data
            document.document_date = form.document_date.data
            document.is_confidential = form.is_confidential.data
            document.is_original = form.is_original.data
            document.updated_at = datetime.utcnow()

            db.session.commit()
            flash('تم تحديث الوثيقة بنجاح', 'success')
            return redirect(url_for('documents.view', id=document.id))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الوثيقة: {str(e)}', 'error')

    return render_template('documents/edit.html', form=form, document=document)

@bp.route('/delete/<int:id>', methods=['POST'])
@login_required
@permission_required('delete_document')
def delete(id):
    """حذف وثيقة"""

    document = Document.query.get_or_404(id)

    try:
        # حذف الملف من النظام
        if os.path.exists(document.file_path):
            os.remove(document.file_path)

        # حذف السجل من قاعدة البيانات
        db.session.delete(document)
        db.session.commit()

        flash('تم حذف الوثيقة بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الوثيقة: {str(e)}', 'error')

    return redirect(url_for('documents.index'))

@bp.route('/categories')
@login_required
@permission_required('manage_document_categories')
def categories():
    """إدارة تصنيفات الوثائق"""

    categories = DocumentCategory.query.order_by(DocumentCategory.sort_order).all()
    return render_template('documents/categories.html', categories=categories)

@bp.route('/categories/add', methods=['GET', 'POST'])
@login_required
@permission_required('manage_document_categories')
def add_category():
    """إضافة تصنيف جديد"""

    form = DocumentCategoryForm()

    if form.validate_on_submit():
        try:
            category = DocumentCategory(
                name=form.name.data,
                name_ar=form.name_ar.data,
                description=form.description.data,
                icon=form.icon.data,
                color=form.color.data,
                sort_order=int(form.sort_order.data) if form.sort_order.data else 0,
                is_active=form.is_active.data
            )

            db.session.add(category)
            db.session.commit()

            flash('تم إضافة التصنيف بنجاح', 'success')
            return redirect(url_for('documents.categories'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة التصنيف: {str(e)}', 'error')

    return render_template('documents/add_category.html', form=form)

@bp.route('/bulk_action', methods=['POST'])
@login_required
@permission_required('manage_documents')
def bulk_action():
    """إجراءات مجمعة على الوثائق"""

    form = BulkDocumentActionForm()

    if form.validate_on_submit():
        try:
            document_ids = [int(id) for id in form.selected_documents.data.split(',') if id.strip()]
            documents = Document.query.filter(Document.id.in_(document_ids)).all()

            if not documents:
                flash('لم يتم تحديد أي وثائق', 'warning')
                return redirect(url_for('documents.index'))

            action = form.action.data

            if action == 'delete':
                for document in documents:
                    if os.path.exists(document.file_path):
                        os.remove(document.file_path)
                    db.session.delete(document)

                db.session.commit()
                flash(f'تم حذف {len(documents)} وثيقة بنجاح', 'success')

            elif action == 'change_category':
                new_category_id = form.new_category_id.data
                for document in documents:
                    document.category_id = new_category_id

                db.session.commit()
                flash(f'تم تغيير تصنيف {len(documents)} وثيقة بنجاح', 'success')

            elif action == 'mark_confidential':
                for document in documents:
                    document.is_confidential = True

                db.session.commit()
                flash(f'تم تحديد {len(documents)} وثيقة كسرية', 'success')

            elif action == 'unmark_confidential':
                for document in documents:
                    document.is_confidential = False

                db.session.commit()
                flash(f'تم إلغاء السرية عن {len(documents)} وثيقة', 'success')

            elif action == 'download_zip':
                return download_documents_zip(documents)

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تنفيذ الإجراء: {str(e)}', 'error')

    return redirect(url_for('documents.index'))

def download_documents_zip(documents):
    """تحميل وثائق متعددة كملف مضغوط"""

    import tempfile
    import shutil

    # إنشاء مجلد مؤقت
    temp_dir = tempfile.mkdtemp()
    zip_path = os.path.join(temp_dir, f'documents_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip')

    try:
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for document in documents:
                if os.path.exists(document.file_path):
                    # إضافة الملف للأرشيف مع اسم منظم
                    archive_name = f"{document.category.name_ar}/{document.original_filename}"
                    zipf.write(document.file_path, archive_name)

        return send_file(
            zip_path,
            as_attachment=True,
            download_name=f'documents_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip'
        )

    finally:
        # تنظيف الملفات المؤقتة
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

def format_file_size(size_bytes):
    """تنسيق حجم الملف"""
    if not size_bytes:
        return "0 B"

    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"
