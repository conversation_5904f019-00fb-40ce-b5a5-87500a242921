# -*- coding: utf-8 -*-
"""
نموذج سجل الإجراءات من موقع mahakim
"""

from datetime import datetime
from app import db

class JournalActions(db.Model):
    """نموذج سجل الإجراءات من موقع mahakim.ma"""
    
    __tablename__ = 'journal_actions'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # ربط بالملف
    dossier_id = db.Column(db.In<PERSON><PERSON>, db.<PERSON>Key('dossiers.id'), nullable=False)
    
    # معلومات من موقع mahakim
    tribunal = db.Column(db.String(50), nullable=False)
    numero_affaire = db.Column(db.String(50), nullable=False)
    
    # تاريخ استخراج البيانات
    date_extraction = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # الإجراءات والقرارات
    type_procedure = db.Column(db.String(100))  # نوع الإجراء
    decision = db.Column(db.Text)  # القرار أو الإجراء
    date_decision = db.Column(db.Date)  # تاريخ القرار
    
    # الجلسة القادمة
    prochaine_audience = db.Column(db.Date)  # تاريخ الجلسة القادمة
    heure_audience = db.Column(db.Time)  # وقت الجلسة
    
    # معلومات إضافية
    juge = db.Column(db.String(200))  # اسم القاضي
    greffier = db.Column(db.String(200))  # اسم كاتب الضبط
    
    # البيانات الخام من الموقع
    raw_data = db.Column(db.Text)  # البيانات الأصلية كما جاءت من الموقع
    
    # حالة الاستخراج
    extraction_status = db.Column(db.String(20), default='success')
    # Options: success, failed, partial
    
    error_message = db.Column(db.Text)  # رسالة الخطأ إن وجدت
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<JournalActions {self.numero_affaire} - {self.date_extraction}>'
    
    def is_recent(self, hours=24):
        """هل الاستخراج حديث"""
        from datetime import timedelta
        return (datetime.utcnow() - self.date_extraction) <= timedelta(hours=hours)
    
    def has_next_audience(self):
        """هل يوجد جلسة قادمة"""
        return self.prochaine_audience is not None
    
    def is_next_audience_soon(self, days=7):
        """هل الجلسة القادمة قريبة"""
        if not self.has_next_audience():
            return False
        
        from datetime import date, timedelta
        return self.prochaine_audience <= date.today() + timedelta(days=days)
    
    def get_status_color(self):
        """لون حالة الاستخراج"""
        colors = {
            'success': 'success',
            'failed': 'danger',
            'partial': 'warning'
        }
        return colors.get(self.extraction_status, 'secondary')
    
    def format_decision(self):
        """تنسيق القرار للعرض"""
        if not self.decision:
            return "لا يوجد قرار"
        
        # تنظيف النص وتنسيقه
        decision = self.decision.strip()
        if len(decision) > 200:
            return decision[:200] + "..."
        return decision
    
    @staticmethod
    def get_latest_for_dossier(dossier_id):
        """آخر إجراء للملف"""
        return JournalActions.query.filter_by(
            dossier_id=dossier_id
        ).order_by(JournalActions.date_extraction.desc()).first()
    
    @staticmethod
    def get_recent_extractions(hours=24):
        """الاستخراجات الحديثة"""
        from datetime import timedelta
        cutoff = datetime.utcnow() - timedelta(hours=hours)
        return JournalActions.query.filter(
            JournalActions.date_extraction >= cutoff
        ).order_by(JournalActions.date_extraction.desc()).all()
    
    def create_audience_if_needed(self):
        """إنشاء جلسة تلقائياً إذا لزم الأمر"""
        if not self.has_next_audience():
            return None
        
        from app.models.audience import Audience
        
        # التحقق من وجود جلسة بنفس التاريخ
        existing = Audience.query.filter_by(
            dossier_id=self.dossier_id,
            date_audience=self.prochaine_audience
        ).first()
        
        if existing:
            return existing
        
        # إنشاء جلسة جديدة
        audience = Audience(
            dossier_id=self.dossier_id,
            date_audience=self.prochaine_audience,
            heure_audience=self.heure_audience or datetime.strptime('09:00', '%H:%M').time(),
            client_nom=self.dossier.client.nom_complet,
            numero_dossier=self.numero_affaire,
            tribunal=self.tribunal,
            type_dossier=self.dossier.type_dossier.nom,
            demande=f"جلسة مستخرجة من موقع المحاكم - {self.type_procedure}",
            notes=f"تم إنشاؤها تلقائياً من استخراج {self.date_extraction.strftime('%Y-%m-%d %H:%M')}"
        )
        
        db.session.add(audience)
        db.session.commit()
        return audience
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'dossier_id': self.dossier_id,
            'tribunal': self.tribunal,
            'numero_affaire': self.numero_affaire,
            'date_extraction': self.date_extraction.isoformat() if self.date_extraction else None,
            'type_procedure': self.type_procedure,
            'decision': self.format_decision(),
            'date_decision': self.date_decision.isoformat() if self.date_decision else None,
            'prochaine_audience': self.prochaine_audience.isoformat() if self.prochaine_audience else None,
            'extraction_status': self.extraction_status,
            'is_recent': self.is_recent(),
            'has_next_audience': self.has_next_audience(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
