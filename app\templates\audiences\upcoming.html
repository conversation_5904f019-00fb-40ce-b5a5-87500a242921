{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-week me-2"></i>
        الجلسات القادمة
        <span class="badge bg-info ms-2">خلال {{ days }} أيام</span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('audiences.today') }}" class="btn btn-danger">
                <i class="fas fa-calendar-day me-1"></i>
                جلسات اليوم
            </a>
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('audiences.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة جلسة
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            جميع الجلسات
        </a>
    </div>
</div>

<!-- فلتر الأيام -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="days" class="form-label">عدد الأيام القادمة</label>
                <select class="form-select" id="days" name="days" onchange="this.form.submit()">
                    <option value="7" {% if days == 7 %}selected{% endif %}>أسبوع واحد</option>
                    <option value="14" {% if days == 14 %}selected{% endif %}>أسبوعين</option>
                    <option value="30" {% if days == 30 %}selected{% endif %}>شهر واحد</option>
                    <option value="60" {% if days == 60 %}selected{% endif %}>شهرين</option>
                    <option value="90" {% if days == 90 %}selected{% endif %}>3 أشهر</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="tribunal" class="form-label">المحكمة</label>
                <select class="form-select" id="tribunal" name="tribunal" onchange="this.form.submit()">
                    <option value="">جميع المحاكم</option>
                    {% for tribunal in tribunaux %}
                    <option value="{{ tribunal }}" {% if tribunal_filter == tribunal %}selected{% endif %}>
                        {{ tribunal }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="important" class="form-label">الأولوية</label>
                <select class="form-select" id="important" name="important" onchange="this.form.submit()">
                    <option value="">جميع الجلسات</option>
                    <option value="1" {% if important_filter %}selected{% endif %}>الجلسات المهمة فقط</option>
                </select>
            </div>
            <div class="col-md-3">
                <a href="{{ url_for('audiences.upcoming') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>
                    إلغاء الفلتر
                </a>
            </div>
        </form>
    </div>
</div>

{% if audiences %}
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        {% set total_audiences = audiences|length %}
        {% set important_audiences = audiences|selectattr('is_important', 'equalto', true)|list|length %}
        {% set tribunaux_count = audiences|map(attribute='tribunal')|unique|list|length %}
        {% set tomorrow_audiences = audiences|selectattr('is_tomorrow')|list|length %}

        <div class="col-md-3">
            <div class="stats-card bg-primary">
                <div class="stats-number">{{ total_audiences }}</div>
                <div class="stats-label">إجمالي الجلسات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-warning">
                <div class="stats-number">{{ important_audiences }}</div>
                <div class="stats-label">جلسات مهمة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-info">
                <div class="stats-number">{{ tribunaux_count }}</div>
                <div class="stats-label">محاكم مختلفة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-success">
                <div class="stats-number">{{ tomorrow_audiences }}</div>
                <div class="stats-label">جلسات الغد</div>
            </div>
        </div>
    </div>

    <!-- جدول الجلسات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                الجلسات القادمة خلال {{ days }} أيام
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>الملف</th>
                            <th>الموكل</th>
                            <th>المحكمة</th>
                            <th>النوع</th>
                            <th>الموضوع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for audience in audiences|sort(attribute='date_audience') %}
                        <tr class="{{ 'table-warning' if audience.is_important else '' }} {{ audience.get_urgency_class() }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong>{{ audience.date_audience.strftime('%d/%m/%Y') }}</strong>
                                        {% if audience.is_important %}
                                        <i class="fas fa-star text-warning ms-1" title="جلسة مهمة"></i>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">
                                            {{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}
                                        </small>
                                    </div>
                                </div>
                                <div class="mt-1">
                                    {% if audience.is_tomorrow() %}
                                    <span class="badge bg-warning">غداً</span>
                                    {% elif audience.days_until() <= 3 %}
                                    <span class="badge bg-info">{{ audience.days_until() }} أيام</span>
                                    {% elif audience.days_until() <= 7 %}
                                    <span class="badge bg-secondary">{{ audience.days_until() }} أيام</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                                    {{ audience.numero_dossier }}
                                </a>
                                {% if audience.reference_interne %}
                                <br><small class="text-muted">مرجع: {{ audience.reference_interne }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('clients.view', id=audience.dossier.client.id) }}" class="text-decoration-none">
                                    {{ audience.client_nom }}
                                </a>
                                {% if audience.dossier.client.telephone %}
                                <br><small class="text-muted">{{ audience.dossier.client.telephone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ audience.tribunal }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ audience.type_dossier }}</span>
                            </td>
                            <td>
                                {% if audience.demande %}
                                <div class="text-truncate" style="max-width: 200px;" title="{{ audience.demande }}">
                                    {{ audience.demande }}
                                </div>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ audience.get_status_color() }}">
                                    {{ audience.statut }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('audiences.view', id=audience.id) }}"
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="{{ url_for('audiences.edit', id=audience.id) }}"
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- ملخص حسب المحكمة -->
    <div class="row mt-4">
        {% set tribunaux_upcoming = audiences|map(attribute='tribunal')|unique|list %}
        {% for tribunal in tribunaux_upcoming %}
        {% set tribunal_audiences = audiences|selectattr('tribunal', 'equalto', tribunal)|list %}
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-university me-1"></i>
                        {{ tribunal }} ({{ tribunal_audiences|length }} جلسة)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for audience in tribunal_audiences|sort(attribute='date_audience')|list %}
                        {% if loop.index <= 5 %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ audience.date_audience.strftime('%d/%m') }}</strong>
                                - {{ audience.numero_dossier }}
                                {% if audience.is_important %}
                                <i class="fas fa-star text-warning ms-1"></i>
                                {% endif %}
                            </div>
                            <span class="badge bg-{{ audience.get_status_color() }}">
                                {{ audience.statut }}
                            </span>
                        </div>
                        {% endif %}
                        {% endfor %}
                        {% if tribunal_audiences|length > 5 %}
                        <div class="list-group-item text-center">
                            <small class="text-muted">و {{ tribunal_audiences|length - 5 }} جلسة أخرى...</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

{% else %}
    <!-- لا توجد جلسات -->
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-calendar-check fa-4x text-success mb-4"></i>
                <h3 class="text-success">لا توجد جلسات قادمة خلال {{ days }} أيام!</h3>
                <p class="lead text-muted">يمكنك الاستراحة أو مراجعة الملفات</p>

                <div class="mt-4">
                    <a href="{{ url_for('audiences.index') }}" class="btn btn-primary me-2">
                        <i class="fas fa-calendar-alt me-1"></i>
                        عرض جميع الجلسات
                    </a>
                    {% if current_user.has_permission('create') %}
                    <a href="{{ url_for('audiences.create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة جلسة جديدة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.table-warning {
    --bs-table-bg: rgba(255, 193, 7, 0.1);
}

@media print {
    .btn-toolbar,
    .card-header,
    .btn-group {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تحديث تلقائي للصفحة كل 5 دقائق
setTimeout(function() {
    location.reload();
}, 300000);

// إضافة تأثيرات بصرية للجلسات القريبة
document.addEventListener('DOMContentLoaded', function() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const dateCell = row.querySelector('td:first-child');
        if (dateCell) {
            const badges = dateCell.querySelectorAll('.badge');
            badges.forEach(badge => {
                if (badge.textContent.includes('غداً')) {
                    row.style.borderLeft = '4px solid #ffc107';
                } else if (badge.textContent.includes('أيام') && parseInt(badge.textContent) <= 3) {
                    row.style.borderLeft = '4px solid #17a2b8';
                }
            });
        }
    });
});
</script>
{% endblock %}
