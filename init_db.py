#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تهيئة قاعدة البيانات
"""

import os
from app import create_app, db
from app.models.user import User
from app.models.type_dossier import TypeDossier
from werkzeug.security import generate_password_hash

def init_database():
    """تهيئة قاعدة البيانات"""
    app = create_app()
    
    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء جداول قاعدة البيانات")
        
        # إنشاء المستخدم المدير
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                nom_complet='المدير العام',
                role='admin',
                is_active=True
            )
            admin.password_hash = generate_password_hash('admin123')
            db.session.add(admin)
            print("✅ تم إنشاء المستخدم المدير")
        
        # إنشاء أنواع الملفات
        types_dossier = [
            'طلاق شقاق',
            'طلاق خلع', 
            'جنحي',
            'مدني',
            'تجاري',
            'عقاري',
            'أحوال شخصية',
            'إداري',
            'جنائي',
            'عمالي'
        ]
        
        for type_name in types_dossier:
            if not TypeDossier.query.filter_by(nom=type_name).first():
                type_dossier = TypeDossier(nom=type_name)
                db.session.add(type_dossier)
        
        print("✅ تم إنشاء أنواع الملفات")
        
        # حفظ التغييرات
        db.session.commit()
        print("✅ تم حفظ جميع البيانات")
        
        print("\n🎉 تم تهيئة قاعدة البيانات بنجاح!")
        print("📝 بيانات تسجيل الدخول:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")

if __name__ == '__main__':
    init_database()
