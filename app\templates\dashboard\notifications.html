{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-bell me-2"></i>
        التنبيهات والإشعارات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-primary" onclick="refreshNotifications()">
                <i class="fas fa-sync-alt me-1"></i>
                تحديث
            </button>
            <button type="button" class="btn btn-outline-secondary" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-1"></i>
                تسجيل الكل كمقروء
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-danger">
            <div class="stats-number">{{ notifications.today_audiences|length if notifications.today_audiences else 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-day me-1"></i>
                جلسات اليوم
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-warning">
            <div class="stats-number">{{ notifications.tomorrow_audiences|length if notifications.tomorrow_audiences else 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-calendar-plus me-1"></i>
                جلسات الغد
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ notifications.urgent_dossiers|length if notifications.urgent_dossiers else 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-exclamation-triangle me-1"></i>
                ملفات مستعجلة
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-success">
            <div class="stats-number">{{ notifications.recent_actions|length if notifications.recent_actions else 0 }}</div>
            <div class="stats-label">
                <i class="fas fa-gavel me-1"></i>
                إجراءات حديثة
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- جلسات اليوم -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day me-2"></i>
                    جلسات اليوم
                    {% if notifications.today_audiences %}
                    <span class="badge bg-light text-dark ms-2">{{ notifications.today_audiences|length }}</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if notifications.today_audiences %}
                    <div class="list-group list-group-flush">
                        {% for audience in notifications.today_audiences %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                                            {{ audience.numero_dossier }}
                                        </a>
                                        {% if audience.is_important %}
                                        <i class="fas fa-star text-warning ms-1"></i>
                                        {% endif %}
                                    </h6>
                                    <small class="text-primary">
                                        {{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}
                                    </small>
                                </div>
                                <p class="mb-1">{{ audience.client_nom }}</p>
                                <small class="text-muted">{{ audience.tribunal }} - {{ audience.type_dossier }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('audiences.today') }}" class="btn btn-danger">
                            <i class="fas fa-eye me-1"></i>
                            عرض جميع جلسات اليوم
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                        <p class="text-muted mb-0">لا توجد جلسات مبرمجة لليوم</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- جلسات الغد -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-plus me-2"></i>
                    جلسات الغد
                    {% if notifications.tomorrow_audiences %}
                    <span class="badge bg-dark ms-2">{{ notifications.tomorrow_audiences|length }}</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if notifications.tomorrow_audiences %}
                    <div class="list-group list-group-flush">
                        {% for audience in notifications.tomorrow_audiences %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                                            {{ audience.numero_dossier }}
                                        </a>
                                        {% if audience.is_important %}
                                        <i class="fas fa-star text-warning ms-1"></i>
                                        {% endif %}
                                    </h6>
                                    <small class="text-primary">
                                        {{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}
                                    </small>
                                </div>
                                <p class="mb-1">{{ audience.client_nom }}</p>
                                <small class="text-muted">{{ audience.tribunal }} - {{ audience.type_dossier }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('audiences.upcoming') }}" class="btn btn-warning">
                            <i class="fas fa-eye me-1"></i>
                            عرض الجلسات القادمة
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد جلسات مبرمجة للغد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- الملفات المستعجلة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    الملفات المستعجلة
                    {% if notifications.urgent_dossiers %}
                    <span class="badge bg-light text-dark ms-2">{{ notifications.urgent_dossiers|length }}</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if notifications.urgent_dossiers %}
                    <div class="list-group list-group-flush">
                        {% for dossier in notifications.urgent_dossiers %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('dossiers.view', id=dossier.id) }}" class="text-decoration-none">
                                        {{ dossier.numero_affaire }}
                                    </a>
                                    <span class="badge bg-danger ms-1">مستعجل</span>
                                </h6>
                                <small class="text-muted">{{ dossier.created_at.strftime('%d/%m') if dossier.created_at else '' }}</small>
                            </div>
                            <p class="mb-1">{{ dossier.client.nom_complet }}</p>
                            <small class="text-muted">
                                {{ dossier.tribunal }} - 
                                <span class="badge bg-{{ dossier.get_status_color() }}">{{ dossier.situation }}</span>
                            </small>
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('dossiers.index') }}?urgent=true" class="btn btn-info">
                            <i class="fas fa-eye me-1"></i>
                            عرض جميع الملفات المستعجلة
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted mb-0">لا توجد ملفات مستعجلة حالياً</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- آخر الإجراءات -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-gavel me-2"></i>
                    آخر الإجراءات المستخرجة
                    {% if notifications.recent_actions %}
                    <span class="badge bg-light text-dark ms-2">{{ notifications.recent_actions|length }}</span>
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if notifications.recent_actions %}
                    <div class="list-group list-group-flush">
                        {% for action in notifications.recent_actions %}
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">
                                    <a href="{{ url_for('dossiers.view', id=action.dossier_id) }}" class="text-decoration-none">
                                        {{ action.numero_affaire }}
                                    </a>
                                    <span class="badge bg-{{ action.get_status_color() }} ms-1">{{ action.extraction_status }}</span>
                                </h6>
                                <small class="text-muted">{{ action.date_extraction.strftime('%d/%m %H:%M') if action.date_extraction else '' }}</small>
                            </div>
                            <p class="mb-1">{{ action.type_procedure or 'إجراء عام' }}</p>
                            {% if action.prochaine_audience %}
                            <small class="text-success">
                                <i class="fas fa-calendar-plus me-1"></i>
                                جلسة قادمة: {{ action.prochaine_audience.strftime('%d/%m/%Y') }}
                            </small>
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('dossiers.index') }}?recent_actions=true" class="btn btn-success">
                            <i class="fas fa-eye me-1"></i>
                            عرض جميع الإجراءات الحديثة
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-clipboard-list fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد إجراءات حديثة</p>
                        <button type="button" class="btn btn-outline-success mt-2" onclick="extractAllActions()">
                            <i class="fas fa-download me-1"></i>
                            استخراج الإجراءات الآن
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح وإرشادات -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-lightbulb me-2"></i>
            نصائح لإدارة أفضل
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="alert alert-info">
                    <h6><i class="fas fa-clock me-1"></i> تنظيم الوقت</h6>
                    <p class="mb-0">راجع جلسات اليوم كل صباح وحضر الملفات المطلوبة مسبقاً</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-sync-alt me-1"></i> التحديث المستمر</h6>
                    <p class="mb-0">استخرج الإجراءات من موقع المحاكم بانتظام للحصول على آخر التطورات</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-success">
                    <h6><i class="fas fa-star me-1"></i> الأولويات</h6>
                    <p class="mb-0">ركز على الملفات المستعجلة والجلسات المهمة أولاً</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.list-group-item {
    border-right: none;
    border-left: none;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshNotifications() {
    // إعادة تحميل الصفحة لتحديث التنبيهات
    location.reload();
}

function markAllAsRead() {
    // تسجيل جميع التنبيهات كمقروءة (سيتم تنفيذه لاحقاً)
    alert('تم تسجيل جميع التنبيهات كمقروءة');
}

function extractAllActions() {
    if (confirm('هل تريد استخراج الإجراءات لجميع الملفات النشطة؟ قد يستغرق هذا بعض الوقت.')) {
        // سيتم تنفيذ استخراج شامل لاحقاً
        alert('سيتم بدء عملية الاستخراج الشامل قريباً');
    }
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    // يمكن إضافة تحديث AJAX هنا بدلاً من إعادة تحميل الصفحة
    console.log('تحديث تلقائي للتنبيهات...');
}, 300000); // 5 دقائق
</script>
{% endblock %}
