{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة موكل جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('clients.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات الموكل
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="nom_complet" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.nom_complet.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- نوع الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="type_dossier_id" class="form-label">
                                <i class="fas fa-folder me-1"></i>
                                نوع الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.type_dossier_id(class="form-select") }}
                            {% if form.type_dossier_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.type_dossier_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- رقم الهاتف -->
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف
                            </label>
                            {{ form.telephone(class="form-control") }}
                            {% if form.telephone.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.telephone.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- رقم الهاتف الثاني -->
                        <div class="col-md-6 mb-3">
                            <label for="telephone_2" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف الثاني
                            </label>
                            {{ form.telephone_2(class="form-control") }}
                            {% if form.telephone_2.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.telephone_2.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- رقم البطاقة الوطنية -->
                        <div class="col-md-6 mb-3">
                            <label for="cin" class="form-label">
                                <i class="fas fa-id-card me-1"></i>
                                رقم البطاقة الوطنية
                            </label>
                            {{ form.cin(class="form-control") }}
                            {% if form.cin.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.cin.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- تاريخ الميلاد -->
                        <div class="col-md-6 mb-3">
                            <label for="date_naissance" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ الميلاد
                            </label>
                            {{ form.date_naissance(class="form-control") }}
                            {% if form.date_naissance.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_naissance.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- مكان الميلاد -->
                        <div class="col-md-6 mb-3">
                            <label for="lieu_naissance" class="form-label">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                مكان الميلاد
                            </label>
                            {{ form.lieu_naissance(class="form-control") }}
                            {% if form.lieu_naissance.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.lieu_naissance.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- المهنة -->
                        <div class="col-md-6 mb-3">
                            <label for="profession" class="form-label">
                                <i class="fas fa-briefcase me-1"></i>
                                المهنة
                            </label>
                            {{ form.profession(class="form-control") }}
                            {% if form.profession.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.profession.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الحالة العائلية -->
                        <div class="col-md-6 mb-3">
                            <label for="situation_familiale" class="form-label">
                                <i class="fas fa-heart me-1"></i>
                                الحالة العائلية
                            </label>
                            {{ form.situation_familiale(class="form-select") }}
                            {% if form.situation_familiale.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.situation_familiale.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- العنوان -->
                    <div class="mb-3">
                        <label for="adresse" class="form-label">
                            <i class="fas fa-home me-1"></i>
                            العنوان
                        </label>
                        {{ form.adresse(class="form-control") }}
                        {% if form.adresse.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.adresse.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الموكل
                        </button>
                        <a href="{{ url_for('clients.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة</li>
                        <li>تأكد من صحة رقم البطاقة الوطنية</li>
                        <li>يمكن إضافة رقمين للهاتف للتواصل</li>
                        <li>اختر نوع الملف المناسب للموكل</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تنبيه:</h6>
                    <p class="mb-0">تأكد من دقة البيانات المدخلة حيث سيتم استخدامها في جميع الملفات والمراسلات.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-format phone numbers
document.addEventListener('DOMContentLoaded', function() {
    const phoneInputs = document.querySelectorAll('input[name="telephone"], input[name="telephone_2"]');
    
    phoneInputs.forEach(input => {
        input.addEventListener('input', function() {
            // Remove all non-digits
            let value = this.value.replace(/\D/g, '');
            
            // Format as Moroccan phone number
            if (value.length > 0) {
                if (value.startsWith('212')) {
                    // International format
                    value = '+' + value;
                } else if (value.startsWith('0')) {
                    // National format
                    if (value.length === 10) {
                        value = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})/, '$1-$2-$3-$4');
                    }
                }
            }
            
            this.value = value;
        });
    });

    // Auto-format CIN
    const cinInput = document.querySelector('input[name="cin"]');
    if (cinInput) {
        cinInput.addEventListener('input', function() {
            let value = this.value.toUpperCase();
            // Remove spaces and special characters except letters and numbers
            value = value.replace(/[^A-Z0-9]/g, '');
            this.value = value;
        });
    }
});
</script>
{% endblock %}
