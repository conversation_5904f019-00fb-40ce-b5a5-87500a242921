# -*- coding: utf-8 -*-
"""
نماذج المستخدمين
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, Optional
from wtforms.widgets import TextArea

class UserForm(FlaskForm):
    """نموذج إضافة/تعديل مستخدم"""
    
    username = StringField(
        'اسم المستخدم',
        validators=[
            DataRequired(message='اسم المستخدم مطلوب'),
            Length(min=3, max=50, message='اسم المستخدم يجب أن يكون بين 3 و 50 حرف')
        ],
        render_kw={'placeholder': 'أدخل اسم المستخدم'}
    )
    
    email = StringField(
        'البريد الإلكتروني',
        validators=[
            Optional(),
            Em<PERSON>(message='البريد الإلكتروني غير صحيح')
        ],
        render_kw={'placeholder': '<EMAIL>'}
    )
    
    nom_complet = StringField(
        'الاسم الكامل',
        validators=[
            DataRequired(message='الاسم الكامل مطلوب'),
            Length(min=2, max=100, message='الاسم الكامل يجب أن يكون بين 2 و 100 حرف')
        ],
        render_kw={'placeholder': 'أدخل الاسم الكامل'}
    )
    
    password = PasswordField(
        'كلمة المرور',
        validators=[
            DataRequired(message='كلمة المرور مطلوبة'),
            Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        ],
        render_kw={'placeholder': 'أدخل كلمة المرور'}
    )
    
    confirm_password = PasswordField(
        'تأكيد كلمة المرور',
        validators=[
            DataRequired(message='تأكيد كلمة المرور مطلوب'),
            EqualTo('password', message='كلمات المرور غير متطابقة')
        ],
        render_kw={'placeholder': 'أعد إدخال كلمة المرور'}
    )
    
    role = SelectField(
        'الدور',
        choices=[
            ('admin', 'مدير'),
            ('lawyer', 'محامي'),
            ('assistant', 'مساعد'),
            ('viewer', 'مشاهد')
        ],
        validators=[DataRequired(message='الدور مطلوب')],
        default='viewer'
    )
    
    is_active = BooleanField(
        'نشط',
        default=True
    )
    
    submit = SubmitField('حفظ')

class UserSearchForm(FlaskForm):
    """نموذج البحث في المستخدمين"""
    
    search = StringField(
        'البحث',
        render_kw={'placeholder': 'ابحث بالاسم أو اسم المستخدم أو البريد الإلكتروني'}
    )
    
    role = SelectField(
        'الدور',
        choices=[
            ('', 'جميع الأدوار'),
            ('admin', 'مدير'),
            ('lawyer', 'محامي'),
            ('assistant', 'مساعد'),
            ('viewer', 'مشاهد')
        ],
        default=''
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('active', 'نشط'),
            ('inactive', 'غير نشط')
        ],
        default=''
    )
    
    submit = SubmitField('بحث')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    
    current_password = PasswordField(
        'كلمة المرور الحالية',
        validators=[
            DataRequired(message='كلمة المرور الحالية مطلوبة')
        ],
        render_kw={'placeholder': 'أدخل كلمة المرور الحالية'}
    )
    
    new_password = PasswordField(
        'كلمة المرور الجديدة',
        validators=[
            DataRequired(message='كلمة المرور الجديدة مطلوبة'),
            Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        ],
        render_kw={'placeholder': 'أدخل كلمة المرور الجديدة'}
    )
    
    confirm_new_password = PasswordField(
        'تأكيد كلمة المرور الجديدة',
        validators=[
            DataRequired(message='تأكيد كلمة المرور الجديدة مطلوب'),
            EqualTo('new_password', message='كلمات المرور غير متطابقة')
        ],
        render_kw={'placeholder': 'أعد إدخال كلمة المرور الجديدة'}
    )
    
    submit = SubmitField('تغيير كلمة المرور')

class ProfileForm(FlaskForm):
    """نموذج تعديل الملف الشخصي"""
    
    nom_complet = StringField(
        'الاسم الكامل',
        validators=[
            DataRequired(message='الاسم الكامل مطلوب'),
            Length(min=2, max=100, message='الاسم الكامل يجب أن يكون بين 2 و 100 حرف')
        ],
        render_kw={'placeholder': 'أدخل الاسم الكامل'}
    )
    
    email = StringField(
        'البريد الإلكتروني',
        validators=[
            Optional(),
            Email(message='البريد الإلكتروني غير صحيح')
        ],
        render_kw={'placeholder': '<EMAIL>'}
    )
    
    submit = SubmitField('حفظ التغييرات')

class ResetPasswordForm(FlaskForm):
    """نموذج إعادة تعيين كلمة المرور (للمدير)"""
    
    new_password = PasswordField(
        'كلمة المرور الجديدة',
        validators=[
            DataRequired(message='كلمة المرور الجديدة مطلوبة'),
            Length(min=6, message='كلمة المرور يجب أن تكون 6 أحرف على الأقل')
        ],
        render_kw={'placeholder': 'أدخل كلمة المرور الجديدة'}
    )
    
    confirm_new_password = PasswordField(
        'تأكيد كلمة المرور الجديدة',
        validators=[
            DataRequired(message='تأكيد كلمة المرور الجديدة مطلوب'),
            EqualTo('new_password', message='كلمات المرور غير متطابقة')
        ],
        render_kw={'placeholder': 'أعد إدخال كلمة المرور الجديدة'}
    )
    
    submit = SubmitField('إعادة تعيين كلمة المرور')
