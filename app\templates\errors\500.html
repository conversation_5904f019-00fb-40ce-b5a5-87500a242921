{% extends "base.html" %}

{% block content %}
<div class="container-fluid h-100 d-flex align-items-center justify-content-center">
    <div class="text-center">
        <div class="error-illustration mb-4">
            <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
        </div>
        
        <h1 class="display-1 fw-bold text-danger">500</h1>
        <h2 class="mb-3">خطأ في الخادم</h2>
        <p class="lead text-muted mb-4">
            عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.
        </p>
        
        <div class="d-flex gap-3 justify-content-center flex-wrap">
            <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary btn-lg">
                <i class="fas fa-home me-2"></i>
                العودة للرئيسية
            </a>
            <button onclick="location.reload()" class="btn btn-outline-secondary btn-lg">
                <i class="fas fa-redo me-2"></i>
                إعادة المحاولة
            </button>
        </div>
        
        <div class="mt-5">
            <div class="alert alert-info d-inline-block">
                <i class="fas fa-info-circle me-2"></i>
                إذا استمر هذا الخطأ، يرجى الاتصال بالدعم الفني
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.error-illustration {
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.container-fluid {
    min-height: calc(100vh - 200px);
}
</style>
{% endblock %}
