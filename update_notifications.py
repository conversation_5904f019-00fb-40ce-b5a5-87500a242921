#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإضافة جداول التنبيهات
"""

from app import create_app, db
from app.models.notification import Notification, NotificationSettings, NotificationTemplate
from app.models.user import User
from app.utils.notification_service import NotificationService
from datetime import datetime, timedelta

def update_database():
    """تحديث قاعدة البيانات"""
    
    app = create_app()
    
    with app.app_context():
        print("🔄 تحديث قاعدة البيانات...")
        
        # إنشاء الجداول الجديدة
        db.create_all()
        print("✅ تم إنشاء جداول التنبيهات")
        
        # إنشاء إعدادات التنبيهات للمستخدمين الموجودين
        users = User.query.all()
        for user in users:
            existing_settings = NotificationSettings.query.filter_by(user_id=user.id).first()
            if not existing_settings:
                settings = NotificationSettings(
                    user_id=user.id,
                    audience_notifications=True,
                    deadline_notifications=True,
                    response_notifications=True,
                    general_notifications=True,
                    email_notifications=False,
                    audience_hours_before=24,
                    deadline_hours_before=48,
                    response_hours_before=24
                )
                db.session.add(settings)
                print(f"✅ تم إنشاء إعدادات التنبيهات للمستخدم: {user.nom_complet}")
        
        # إنشاء قوالب التنبيهات الافتراضية
        create_default_templates()
        
        # إنشاء تنبيهات تجريبية
        create_sample_notifications()
        
        db.session.commit()
        print("✅ تم حفظ جميع التغييرات")
        
        # تشغيل فحص التنبيهات
        print("\n🔍 فحص التنبيهات التلقائية...")
        results = NotificationService.run_daily_check()
        
        print(f"📊 نتائج الفحص:")
        print(f"   - تنبيهات الجلسات: {results['audience_notifications']}")
        print(f"   - تنبيهات المواعيد: {results['deadline_notifications']}")
        print(f"   - تنبيهات الردود: {results['response_notifications']}")
        print(f"   - التنبيهات المرسلة: {results['sent_notifications']}")
        
        print("\n🎉 تم تحديث النظام بنجاح!")
        print("💡 يمكنك الآن الوصول للتنبيهات من: /notifications")

def create_default_templates():
    """إنشاء قوالب التنبيهات الافتراضية"""
    
    templates = [
        {
            'name': 'تنبيه الجلسة - 24 ساعة',
            'type': 'audience',
            'title_template': 'تذكير: جلسة الملف {numero_dossier}',
            'message_template': 'لديك جلسة غداً في {date_audience} الساعة {heure_audience}\nالملف: {numero_dossier}\nالموكل: {client_nom}\nالمحكمة: {tribunal}',
            'hours_before': 24
        },
        {
            'name': 'تنبيه الجلسة - ساعتين',
            'type': 'audience',
            'title_template': 'تنبيه عاجل: جلسة خلال ساعتين',
            'message_template': 'تذكير عاجل: لديك جلسة خلال ساعتين\nالملف: {numero_dossier}\nالموكل: {client_nom}\nالمحكمة: {tribunal}',
            'hours_before': 2
        },
        {
            'name': 'تنبيه موعد الاستئناف',
            'type': 'deadline',
            'title_template': 'موعد نهائي: انتهاء أجل الاستئناف',
            'message_template': 'ينتهي أجل الاستئناف للملف {numero_dossier} في {deadline_date}\nالموكل: {client_nom}\nيرجى اتخاذ الإجراء المناسب',
            'hours_before': 48
        },
        {
            'name': 'تنبيه الرد على المذكرة',
            'type': 'response',
            'title_template': 'رد مطلوب: {response_type}',
            'message_template': 'مطلوب {response_type} للملف {numero_dossier} قبل {response_deadline}\nالموكل: {client_nom}\nيرجى إعداد الرد في الوقت المناسب',
            'hours_before': 24
        }
    ]
    
    for template_data in templates:
        existing_template = NotificationTemplate.query.filter_by(
            name=template_data['name']
        ).first()
        
        if not existing_template:
            template = NotificationTemplate(**template_data)
            db.session.add(template)
            print(f"✅ تم إنشاء قالب: {template_data['name']}")

def create_sample_notifications():
    """إنشاء تنبيهات تجريبية"""
    
    # الحصول على أول مستخدم (عادة المدير)
    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        admin_user = User.query.first()
    
    if admin_user:
        # تنبيه ترحيبي
        welcome_notification = Notification(
            type='general',
            title='مرحباً بك في نظام التنبيهات!',
            message='تم تفعيل نظام التنبيهات التلقائية بنجاح.\n\n'
                   'المميزات الجديدة:\n'
                   '• تنبيهات الجلسات قبل 24 ساعة\n'
                   '• تنبيهات المواعيد النهائية للاستئناف\n'
                   '• تذكير بالردود على المذكرات\n'
                   '• إشعارات البريد الإلكتروني (اختيارية)\n\n'
                   'يمكنك تخصيص الإعدادات من قائمة التنبيهات.',
            priority='medium',
            scheduled_time=datetime.utcnow(),
            user_id=admin_user.id,
            is_sent=True,
            sent_at=datetime.utcnow()
        )
        db.session.add(welcome_notification)
        
        # تنبيه تجريبي للجلسات
        sample_audience_notification = Notification(
            type='audience',
            title='تنبيه تجريبي: جلسة قادمة',
            message='هذا تنبيه تجريبي لإظهار كيفية عمل تنبيهات الجلسات.\n\n'
                   'سيتم إرسال تنبيهات مماثلة تلقائياً قبل مواعيد جلساتك الحقيقية.',
            priority='high',
            scheduled_time=datetime.utcnow(),
            user_id=admin_user.id,
            is_sent=True,
            sent_at=datetime.utcnow()
        )
        db.session.add(sample_audience_notification)
        
        # تنبيه تجريبي للمواعيد النهائية
        sample_deadline_notification = Notification(
            type='deadline',
            title='تنبيه تجريبي: موعد نهائي',
            message='هذا تنبيه تجريبي لإظهار كيفية عمل تنبيهات المواعيد النهائية.\n\n'
                   'سيتم تنبيهك تلقائياً قبل انتهاء آجال الاستئناف والطعن.',
            priority='urgent',
            scheduled_time=datetime.utcnow(),
            user_id=admin_user.id,
            is_sent=True,
            sent_at=datetime.utcnow()
        )
        db.session.add(sample_deadline_notification)
        
        print(f"✅ تم إنشاء تنبيهات تجريبية للمستخدم: {admin_user.nom_complet}")

if __name__ == '__main__':
    update_database()
