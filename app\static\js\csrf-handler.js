/**
 * معالج رمز CSRF للنماذج
 * يقوم بتجديد رمز CSRF تلقائياً ومنع انتهاء الصلاحية
 */

class CSRFHandler {
    constructor() {
        this.refreshInterval = 30 * 60 * 1000; // 30 دقيقة
        this.warningTime = 10 * 60 * 1000; // 10 دقائق قبل انتهاء الصلاحية
        this.tokenLifetime = 2 * 60 * 60 * 1000; // ساعتين
        
        this.init();
    }
    
    init() {
        // بدء تجديد الرمز التلقائي
        this.startAutoRefresh();
        
        // إضافة تحذير قبل انتهاء الصلاحية
        this.scheduleWarning();
        
        // معالجة أخطاء CSRF في النماذج
        this.handleFormSubmissions();
        
        console.log('تم تفعيل معالج CSRF');
    }
    
    /**
     * الحصول على رمز CSRF الحالي
     */
    getCurrentToken() {
        const metaTag = document.querySelector('meta[name=csrf-token]');
        return metaTag ? metaTag.getAttribute('content') : null;
    }
    
    /**
     * تحديث رمز CSRF في جميع النماذج والعناصر
     */
    updateToken(newToken) {
        // تحديث meta tag
        const metaTag = document.querySelector('meta[name=csrf-token]');
        if (metaTag) {
            metaTag.setAttribute('content', newToken);
        }
        
        // تحديث جميع حقول CSRF في النماذج
        const csrfInputs = document.querySelectorAll('input[name="csrf_token"]');
        csrfInputs.forEach(input => {
            input.value = newToken;
        });
        
        console.log('تم تحديث رمز CSRF في جميع النماذج');
    }
    
    /**
     * تجديد رمز CSRF من الخادم
     */
    async refreshToken() {
        try {
            const response = await fetch('/auth/csrf-token', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': this.getCurrentToken()
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.csrf_token) {
                    this.updateToken(data.csrf_token);
                    this.showNotification('تم تجديد رمز الأمان بنجاح', 'success');
                    return true;
                }
            }
            
            throw new Error('فشل في الحصول على رمز جديد');
            
        } catch (error) {
            console.error('خطأ في تجديد رمز CSRF:', error);
            this.showNotification('تعذر تجديد رمز الأمان. يرجى تحديث الصفحة.', 'warning');
            return false;
        }
    }
    
    /**
     * بدء التجديد التلقائي
     */
    startAutoRefresh() {
        setInterval(() => {
            this.refreshToken();
        }, this.refreshInterval);
    }
    
    /**
     * جدولة التحذير قبل انتهاء الصلاحية
     */
    scheduleWarning() {
        setTimeout(() => {
            this.showExpirationWarning();
        }, this.tokenLifetime - this.warningTime);
    }
    
    /**
     * عرض تحذير انتهاء الصلاحية
     */
    showExpirationWarning() {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
        alertDiv.style.cssText = 'top: 20px; left: 20px; right: 20px; z-index: 9999;';
        alertDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>تنبيه:</strong> ستنتهي صلاحية النموذج خلال 10 دقائق. 
            <button type="button" class="btn btn-sm btn-outline-warning ms-2" onclick="csrfHandler.refreshToken()">
                تجديد الآن
            </button>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // إزالة التحذير تلقائياً بعد 30 ثانية
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 30000);
    }
    
    /**
     * معالجة إرسال النماذج
     */
    handleFormSubmissions() {
        document.addEventListener('submit', async (event) => {
            const form = event.target;
            const csrfInput = form.querySelector('input[name="csrf_token"]');
            
            if (csrfInput) {
                // التأكد من أن الرمز محدث
                const currentToken = this.getCurrentToken();
                if (currentToken && csrfInput.value !== currentToken) {
                    csrfInput.value = currentToken;
                }
                
                // إضافة مؤشر تحميل
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الحفظ...';
                    submitBtn.disabled = true;
                    
                    // استعادة النص الأصلي في حالة الخطأ
                    setTimeout(() => {
                        if (submitBtn.disabled) {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }
                    }, 10000);
                }
            }
        });
    }
    
    /**
     * عرض إشعار
     */
    showNotification(message, type = 'info') {
        // إنشاء إشعار مؤقت
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; right: 20px; z-index: 9999;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(notification);
        
        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }
    
    /**
     * حفظ بيانات النموذج في localStorage
     */
    saveFormData(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (key !== 'csrf_token') {
                data[key] = value;
            }
        }
        
        const formId = form.id || form.action.split('/').pop();
        localStorage.setItem(`form_backup_${formId}`, JSON.stringify(data));
    }
    
    /**
     * استرجاع بيانات النموذج من localStorage
     */
    restoreFormData(form) {
        const formId = form.id || form.action.split('/').pop();
        const savedData = localStorage.getItem(`form_backup_${formId}`);
        
        if (savedData) {
            try {
                const data = JSON.parse(savedData);
                
                for (let [key, value] of Object.entries(data)) {
                    const input = form.querySelector(`[name="${key}"]`);
                    if (input && !input.value) {
                        input.value = value;
                    }
                }
                
                console.log('تم استرجاع بيانات النموذج المحفوظة');
            } catch (error) {
                console.error('خطأ في استرجاع بيانات النموذج:', error);
            }
        }
    }
    
    /**
     * مسح بيانات النموذج المحفوظة
     */
    clearFormData(form) {
        const formId = form.id || form.action.split('/').pop();
        localStorage.removeItem(`form_backup_${formId}`);
    }
}

// تفعيل معالج CSRF عند تحميل الصفحة
let csrfHandler;

document.addEventListener('DOMContentLoaded', function() {
    csrfHandler = new CSRFHandler();
    
    // حفظ بيانات النماذج تلقائياً
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        // استرجاع البيانات المحفوظة
        csrfHandler.restoreFormData(form);
        
        // حفظ البيانات عند التغيير
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                csrfHandler.saveFormData(form);
            });
            
            input.addEventListener('change', () => {
                csrfHandler.saveFormData(form);
            });
        });
        
        // مسح البيانات عند الإرسال الناجح
        form.addEventListener('submit', () => {
            setTimeout(() => {
                csrfHandler.clearFormData(form);
            }, 2000);
        });
    });
});

// تصدير للاستخدام العام
window.csrfHandler = csrfHandler;
