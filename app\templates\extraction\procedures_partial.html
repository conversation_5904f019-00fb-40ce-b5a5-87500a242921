{% if procedures %}
<div class="timeline">
    {% for procedure in procedures %}
    <div class="timeline-item">
        <div class="timeline-marker bg-{{ procedure.status_color }}">
            <i class="{{ procedure.type_icon }}"></i>
        </div>
        <div class="timeline-content">
            <div class="d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                    <h6 class="timeline-title">
                        {{ procedure.procedure_type }}
                        {% if procedure.is_future %}
                        <span class="badge bg-warning ms-2">قادم</span>
                        {% endif %}
                        {% if procedure.is_audience %}
                        <span class="badge bg-info ms-2">جلسة</span>
                        {% endif %}
                        {% if procedure.is_decision %}
                        <span class="badge bg-success ms-2">قرار</span>
                        {% endif %}
                    </h6>
                    {% if procedure.procedure_details %}
                    <p class="timeline-text">{{ procedure.procedure_details }}</p>
                    {% endif %}
                    <div class="timeline-meta">
                        <span class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ procedure.formatted_date }}
                        </span>
                        {% if procedure.procedure_status %}
                        <span class="badge bg-{{ procedure.status_color }} ms-2">
                            {{ procedure.procedure_status }}
                        </span>
                        {% endif %}
                    </div>
                </div>
                <small class="text-muted">
                    #{{ procedure.sequence_number }}
                </small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- معلومات إضافية -->
<div class="mt-4">
    <div class="row">
        <div class="col-md-6">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle me-1"></i> ملخص الإجراءات</h6>
                <ul class="mb-0">
                    <li><strong>إجمالي الإجراءات:</strong> {{ procedures|length }}</li>
                    <li><strong>الجلسات:</strong> {{ procedures|selectattr('is_audience')|list|length }}</li>
                    <li><strong>القرارات:</strong> {{ procedures|selectattr('is_decision')|list|length }}</li>
                    <li><strong>الإجراءات القادمة:</strong> {{ procedures|selectattr('is_future')|list|length }}</li>
                </ul>
            </div>
        </div>
        
        <div class="col-md-6">
            {% set latest_extraction = dossier.extraction_logs.order_by(dossier.extraction_logs.created_at.desc()).first() %}
            {% if latest_extraction %}
            <div class="alert alert-{{ 'success' if latest_extraction.success else 'danger' }}">
                <h6><i class="fas fa-download me-1"></i> آخر استخراج</h6>
                <ul class="mb-0">
                    <li><strong>التاريخ:</strong> {{ latest_extraction.created_at.strftime('%d/%m/%Y %H:%M') }}</li>
                    <li><strong>الطريقة:</strong> {{ 'محاكاة' if latest_extraction.extraction_method == 'simulation' else 'حقيقي' }}</li>
                    <li><strong>المدة:</strong> {{ "%.1f"|format(latest_extraction.extraction_duration or 0) }} ثانية</li>
                    <li><strong>الحالة:</strong> 
                        <span class="badge bg-{{ 'success' if latest_extraction.success else 'danger' }}">
                            {{ 'نجح' if latest_extraction.success else 'فشل' }}
                        </span>
                    </li>
                </ul>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 3rem;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -3rem;
    top: 0;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.timeline-content {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border-left: 4px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.timeline-title {
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

.timeline-text {
    margin-bottom: 0.75rem;
    color: #6c757d;
    line-height: 1.5;
}

.timeline-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 1.25rem;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #dee2e6, transparent);
}
</style>

{% else %}
<div class="text-center py-5">
    <i class="fas fa-clipboard-list fa-4x text-muted mb-3"></i>
    <h5 class="text-muted">لا توجد إجراءات مستخرجة</h5>
    <p class="text-muted">لم يتم استخراج أي إجراءات لهذا الملف بعد</p>
    <button type="button" class="btn btn-success" onclick="extractActions()">
        <i class="fas fa-download me-1"></i>
        استخراج الإجراءات الآن
    </button>
</div>
{% endif %}
