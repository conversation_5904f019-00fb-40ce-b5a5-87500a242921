{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-day me-2"></i>
        جلسات اليوم
        <span class="badge bg-primary ms-2">{{ today_date.strftime('%d/%m/%Y') if today_date else '' }}</span>
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('audiences.print_today') }}" class="btn btn-success">
                <i class="fas fa-print me-1"></i>
                طباعة PDF
            </a>
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('audiences.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة جلسة
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            جميع الجلسات
        </a>
    </div>
</div>

{% if audiences %}
    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card bg-primary">
                <div class="stats-number">{{ audiences|length }}</div>
                <div class="stats-label">إجمالي الجلسات</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-success">
                <div class="stats-number">{{ audiences|selectattr('statut', 'equalto', 'مبرمجة')|list|length }}</div>
                <div class="stats-label">مبرمجة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-warning">
                <div class="stats-number">{{ audiences|selectattr('is_important', 'equalto', true)|list|length }}</div>
                <div class="stats-label">مهمة</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card bg-info">
                <div class="stats-number">{{ audiences|map(attribute='tribunal')|unique|list|length }}</div>
                <div class="stats-label">محاكم مختلفة</div>
            </div>
        </div>
    </div>

    <!-- جدول الجلسات -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                برنامج جلسات اليوم
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>الوقت</th>
                            <th>الملف</th>
                            <th>الموكل</th>
                            <th>المحكمة</th>
                            <th>النوع</th>
                            <th>الموضوع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for audience in audiences %}
                        <tr class="{{ 'table-warning' if audience.is_important else '' }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="time-badge">
                                        {{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}
                                    </div>
                                    {% if audience.is_important %}
                                    <i class="fas fa-star text-warning ms-2" title="جلسة مهمة"></i>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>
                                        <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                                            {{ audience.numero_dossier }}
                                        </a>
                                    </strong>
                                    {% if audience.reference_interne %}
                                    <br><small class="text-muted">مرجع: {{ audience.reference_interne }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <a href="{{ url_for('clients.view', id=audience.dossier.client.id) }}" class="text-decoration-none">
                                    {{ audience.client_nom }}
                                </a>
                                {% if audience.dossier.client.telephone %}
                                <br><small class="text-muted">{{ audience.dossier.client.telephone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ audience.tribunal }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ audience.type_dossier }}</span>
                            </td>
                            <td>
                                {% if audience.demande %}
                                <div class="text-truncate" style="max-width: 200px;" title="{{ audience.demande }}">
                                    {{ audience.demande }}
                                </div>
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ audience.get_status_color() }}">
                                    {{ audience.statut }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('audiences.view', id=audience.id) }}"
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="{{ url_for('audiences.edit', id=audience.id) }}"
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-success"
                                            onclick="markAsCompleted({{ audience.id }})"
                                            title="تم الانعقاد"
                                            {% if audience.statut == 'منعقدة' %}disabled{% endif %}>
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- ملخص حسب المحكمة -->
    <div class="row mt-4">
        {% set tribunaux_today = audiences|map(attribute='tribunal')|unique|list %}
        {% for tribunal in tribunaux_today %}
        {% set tribunal_audiences = audiences|selectattr('tribunal', 'equalto', tribunal)|list %}
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-university me-1"></i>
                        {{ tribunal }} ({{ tribunal_audiences|length }} جلسة)
                    </h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for audience in tribunal_audiences|sort(attribute='heure_audience') %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}</strong>
                                - {{ audience.numero_dossier }}
                                {% if audience.is_important %}
                                <i class="fas fa-star text-warning ms-1"></i>
                                {% endif %}
                            </div>
                            <span class="badge bg-{{ audience.get_status_color() }}">
                                {{ audience.statut }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

{% else %}
    <!-- لا توجد جلسات -->
    <div class="card">
        <div class="card-body">
            <div class="text-center py-5">
                <i class="fas fa-calendar-check fa-4x text-success mb-4"></i>
                <h3 class="text-success">لا توجد جلسات مبرمجة لليوم!</h3>
                <p class="lead text-muted">يمكنك الاستراحة أو مراجعة الملفات</p>

                <div class="mt-4">
                    <a href="{{ url_for('audiences.upcoming') }}" class="btn btn-primary me-2">
                        <i class="fas fa-calendar-week me-1"></i>
                        عرض الجلسات القادمة
                    </a>
                    {% if current_user.has_permission('create') %}
                    <a href="{{ url_for('audiences.create') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة جلسة جديدة
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endif %}

<!-- Mark as Completed Modal -->
<div class="modal fade" id="completedModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد انعقاد الجلسة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد تسجيل هذه الجلسة كـ "منعقدة"؟</p>
                <div class="mb-3">
                    <label for="resultat" class="form-label">نتيجة الجلسة (اختياري)</label>
                    <textarea class="form-control" id="resultat" rows="3" placeholder="أدخل نتيجة الجلسة..."></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="confirmCompleted()">
                    <i class="fas fa-check me-1"></i>
                    تأكيد الانعقاد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.time-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-weight: bold;
    font-size: 0.9rem;
}

.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

@media print {
    .btn-toolbar,
    .card-header,
    .btn-group {
        display: none !important;
    }

    .card {
        border: none !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
let selectedAudienceId = null;

function markAsCompleted(audienceId) {
    selectedAudienceId = audienceId;
    const modal = new bootstrap.Modal(document.getElementById('completedModal'));
    modal.show();
}

function confirmCompleted() {
    if (!selectedAudienceId) return;

    const resultat = document.getElementById('resultat').value;

    // إرسال طلب AJAX لتحديث حالة الجلسة
    fetch(`/audiences/${selectedAudienceId}/edit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
            'statut': 'منعقدة',
            'resultat': resultat,
            'csrf_token': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
        })
    })
    .then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('حدث خطأ في تحديث الجلسة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحديث الجلسة');
    });

    // إغلاق النافذة المنبثقة
    bootstrap.Modal.getInstance(document.getElementById('completedModal')).hide();
}

// طباعة الصفحة
function printPage() {
    window.print();
}
</script>
{% endblock %}
