# -*- coding: utf-8 -*-
"""
نموذج المستخدمين
"""

from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""

    __tablename__ = 'users'

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    nom_complet = db.Column(db.String(200), nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)

    # الأدوار والصلاحيات
    role = db.Column(db.String(20), nullable=False, default='assistant')
    # Roles: admin, lawyer, assistant, viewer

    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_verified = db.Column(db.<PERSON>, default=False, nullable=False)

    # معلومات إضافية
    telephone = db.Column(db.String(20))
    adresse = db.Column(db.Text)

    # تواريخ مهمة
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime)

    def __repr__(self):
        return f'<User {self.username}>'

    # العلاقات مع نظام الاستخراج
    @property
    def extraction_logs(self):
        """الحصول على سجلات الاستخراج للمستخدم"""
        from app.models.extraction import ExtractionLog
        return ExtractionLog.query.filter_by(extracted_by=self.id)

    @property
    def extraction_settings(self):
        """الحصول على إعدادات الاستخراج للمستخدم"""
        from app.models.extraction import ExtractionSettings
        return ExtractionSettings.query.filter_by(user_id=self.id).first()

    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)

    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': ['create', 'read', 'update', 'delete', 'manage_users'],
            'lawyer': ['create', 'read', 'update', 'delete'],
            'assistant': ['create', 'read', 'update'],
            'viewer': ['read']
        }
        return permission in permissions.get(self.role, [])

    def can_access_dossier(self, dossier):
        """التحقق من إمكانية الوصول للملف"""
        if self.role in ['admin', 'lawyer']:
            return True
        # يمكن إضافة منطق أكثر تعقيداً هنا
        return False

    def update_last_login(self):
        """تحديث آخر تسجيل دخول"""
        self.last_login = datetime.utcnow()
        db.session.commit()

    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'nom_complet': self.nom_complet,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
