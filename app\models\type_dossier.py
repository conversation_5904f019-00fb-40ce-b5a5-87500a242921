# -*- coding: utf-8 -*-
"""
نموذج أنواع الملفات
"""

from datetime import datetime
from app import db

class TypeDossier(db.Model):
    """نموذج أنواع الملفات"""
    
    __tablename__ = 'type_dossiers'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    
    # إعدادات خاصة بكل نوع
    couleur = db.Column(db.String(7), default='#007bff')  # لون للتمييز
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    clients = db.relationship('Client', backref='type_dossier_rel', lazy='dynamic')
    dossiers = db.relationship('Dossier', backref='type_dossier_rel', lazy='dynamic')
    
    def __repr__(self):
        return f'<TypeDossier {self.nom}>'
    
    def get_clients_count(self):
        """عدد الموكلين لهذا النوع"""
        return self.clients.count()
    
    def get_dossiers_count(self):
        """عدد الملفات لهذا النوع"""
        return self.dossiers.count()
    
    def get_active_dossiers_count(self):
        """عدد الملفات النشطة"""
        return self.dossiers.filter_by(is_archived=False).count()
    
    @staticmethod
    def get_active_types():
        """الحصول على الأنواع النشطة"""
        return TypeDossier.query.filter_by(is_active=True).order_by(TypeDossier.nom).all()
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'nom': self.nom,
            'description': self.description,
            'couleur': self.couleur,
            'is_active': self.is_active,
            'clients_count': self.get_clients_count(),
            'dossiers_count': self.get_dossiers_count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
