# -*- coding: utf-8 -*-
"""
نماذج المصادقة
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SelectField, TextAreaField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError
from app.models.user import User

class LoginForm(FlaskForm):
    """نموذج تسجيل الدخول"""
    username = StringField('اسم المستخدم', validators=[DataRequired()], 
                          render_kw={'class': 'form-control', 'placeholder': 'اسم المستخدم'})
    password = PasswordField('كلمة المرور', validators=[DataRequired()],
                           render_kw={'class': 'form-control', 'placeholder': 'كلمة المرور'})
    remember_me = BooleanField('تذكرني', render_kw={'class': 'form-check-input'})

class RegistrationForm(FlaskForm):
    """نموذج تسجيل مستخدم جديد"""
    username = StringField('اسم المستخدم', validators=[DataRequired(), Length(min=4, max=20)],
                          render_kw={'class': 'form-control', 'placeholder': 'اسم المستخدم'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'})
    nom_complet = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=200)],
                             render_kw={'class': 'form-control', 'placeholder': 'الاسم الكامل'})
    telephone = StringField('رقم الهاتف', validators=[Length(max=20)],
                           render_kw={'class': 'form-control', 'placeholder': 'رقم الهاتف'})
    role = SelectField('الدور', validators=[DataRequired()],
                      choices=[
                          ('admin', 'مدير'),
                          ('lawyer', 'محامي'),
                          ('assistant', 'مساعد'),
                          ('viewer', 'مشاهد')
                      ],
                      render_kw={'class': 'form-select'})
    password = PasswordField('كلمة المرور', validators=[DataRequired(), Length(min=6)],
                           render_kw={'class': 'form-control', 'placeholder': 'كلمة المرور'})
    password2 = PasswordField('تأكيد كلمة المرور', 
                             validators=[DataRequired(), EqualTo('password')],
                             render_kw={'class': 'form-control', 'placeholder': 'تأكيد كلمة المرور'})
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('اسم المستخدم مستخدم بالفعل')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل')

class ChangePasswordForm(FlaskForm):
    """نموذج تغيير كلمة المرور"""
    old_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()],
                                render_kw={'class': 'form-control', 'placeholder': 'كلمة المرور الحالية'})
    new_password = PasswordField('كلمة المرور الجديدة', validators=[DataRequired(), Length(min=6)],
                                render_kw={'class': 'form-control', 'placeholder': 'كلمة المرور الجديدة'})
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة',
                                 validators=[DataRequired(), EqualTo('new_password')],
                                 render_kw={'class': 'form-control', 'placeholder': 'تأكيد كلمة المرور الجديدة'})

class EditProfileForm(FlaskForm):
    """نموذج تعديل الملف الشخصي"""
    nom_complet = StringField('الاسم الكامل', validators=[DataRequired(), Length(max=200)],
                             render_kw={'class': 'form-control', 'placeholder': 'الاسم الكامل'})
    email = StringField('البريد الإلكتروني', validators=[DataRequired(), Email()],
                       render_kw={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'})
    telephone = StringField('رقم الهاتف', validators=[Length(max=20)],
                           render_kw={'class': 'form-control', 'placeholder': 'رقم الهاتف'})
    adresse = TextAreaField('العنوان', validators=[Length(max=500)],
                           render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'العنوان'})
