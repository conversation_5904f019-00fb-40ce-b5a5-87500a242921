# -*- coding: utf-8 -*-
"""
نموذج الملفات
"""

from datetime import datetime
from app import db

class Dossier(db.Model):
    """نموذج الملفات"""

    __tablename__ = 'dossiers'

    id = db.Column(db.Integer, primary_key=True)
    numero_affaire = db.Column(db.String(50), nullable=False, unique=True, index=True)

    # المحكمة
    tribunal = db.Column(db.String(20), nullable=False)
    # Options: ابتدائية، استئناف، نقض، تجارية، إدارية

    # نوع الملف
    type_dossier_id = db.Column(db.Integer, db.ForeignKey('type_dossiers.id'), nullable=False)

    # الحالة
    situation = db.Column(db.String(30), nullable=False, default='في انتظار التعيين')
    # Options: في الجلسات، في انتظار التعيين، مداولة، تأمل، منتهي، مؤرشف

    # التواريخ
    date_ouverture = db.Column(db.Date, nullable=False, default=datetime.utcnow().date())
    date_cloture = db.Column(db.Date)

    # الأطراف
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=False)
    adversaire = db.Column(db.String(200))

    # معلومات إضافية
    objet = db.Column(db.Text)  # موضوع الملف
    notes = db.Column(db.Text)
    montant_litige = db.Column(db.Numeric(10, 2))  # مبلغ النزاع
    honoraires = db.Column(db.Numeric(10, 2))  # الأتعاب

    # حالة الملف
    is_archived = db.Column(db.Boolean, default=False, nullable=False)
    is_urgent = db.Column(db.Boolean, default=False, nullable=False)

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    audiences = db.relationship('Audience', backref='dossier', lazy='dynamic', cascade='all, delete-orphan')
    journal_actions = db.relationship('JournalActions', backref='dossier', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Dossier {self.numero_affaire}>'

    # العلاقات مع نظام الاستخراج
    @property
    def extraction_logs(self):
        """الحصول على سجلات الاستخراج"""
        from app.models.extraction import ExtractionLog
        return ExtractionLog.query.filter_by(dossier_id=self.id)

    @property
    def case_procedures(self):
        """الحصول على الإجراءات المستخرجة"""
        from app.models.extraction import CaseProcedure
        return CaseProcedure.query.filter_by(dossier_id=self.id)

    def get_latest_extraction(self):
        """الحصول على آخر عملية استخراج"""
        from app.models.extraction import ExtractionLog
        return ExtractionLog.query.filter_by(dossier_id=self.id)\
                                  .order_by(ExtractionLog.created_at.desc())\
                                  .first()

    def get_latest_procedures(self):
        """الحصول على آخر الإجراءات المستخرجة"""
        latest_extraction = self.get_latest_extraction()
        if latest_extraction:
            from app.models.extraction import CaseProcedure
            return CaseProcedure.query.filter_by(extraction_log_id=latest_extraction.id)\
                                     .order_by(CaseProcedure.sequence_number)\
                                     .all()
        return []

    @property
    def type_dossier(self):
        """نوع الملف"""
        from app.models.type_dossier import TypeDossier
        return TypeDossier.query.get(self.type_dossier_id)

    def get_audiences_count(self):
        """عدد الجلسات"""
        return self.audiences.count()

    def get_next_audience(self):
        """الجلسة القادمة"""
        from app.models.audience import Audience
        return self.audiences.filter(
            Audience.date_audience >= datetime.utcnow().date()
        ).order_by(Audience.date_audience.asc()).first()

    def get_last_audience(self):
        """آخر جلسة"""
        return self.audiences.order_by(self.audiences.desc()).first()

    def get_latest_action(self):
        """آخر إجراء"""
        return self.journal_actions.order_by(self.journal_actions.desc()).first()

    def is_active(self):
        """هل الملف نشط"""
        return not self.is_archived and self.situation not in ['منتهي', 'مؤرشف']

    def get_status_color(self):
        """لون الحالة"""
        colors = {
            'في الجلسات': 'success',
            'في انتظار التعيين': 'warning',
            'مداولة': 'info',
            'تأمل': 'secondary',
            'منتهي': 'dark',
            'مؤرشف': 'muted'
        }
        return colors.get(self.situation, 'primary')

    @staticmethod
    def search(query):
        """البحث في الملفات"""
        return Dossier.query.filter(
            db.or_(
                Dossier.numero_affaire.contains(query),
                Dossier.adversaire.contains(query),
                Dossier.objet.contains(query)
            )
        )

    @staticmethod
    def get_by_tribunal_and_type(tribunal, type_dossier_id):
        """الحصول على الملفات حسب المحكمة والنوع"""
        return Dossier.query.filter_by(
            tribunal=tribunal,
            type_dossier_id=type_dossier_id,
            is_archived=False
        ).order_by(Dossier.date_ouverture.desc())

    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'numero_affaire': self.numero_affaire,
            'tribunal': self.tribunal,
            'type_dossier': self.type_dossier.nom if self.type_dossier else None,
            'situation': self.situation,
            'client_nom': self.client.nom_complet if self.client else None,
            'adversaire': self.adversaire,
            'date_ouverture': self.date_ouverture.isoformat() if self.date_ouverture else None,
            'audiences_count': self.get_audiences_count(),
            'is_urgent': self.is_urgent,
            'is_archived': self.is_archived,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
