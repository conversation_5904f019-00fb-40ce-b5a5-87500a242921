{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-cogs me-2"></i>
        لوحة تحكم المدير
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.users') }}" class="btn btn-primary">
                <i class="fas fa-users me-1"></i>
                إدارة المستخدمين
            </a>
            <a href="{{ url_for('admin.system') }}" class="btn btn-info">
                <i class="fas fa-server me-1"></i>
                إعدادات النظام
            </a>
            <a href="{{ url_for('admin.logs') }}" class="btn btn-warning">
                <i class="fas fa-file-alt me-1"></i>
                السجلات
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات عامة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-primary">
            <div class="stats-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number">{{ stats.total_users }}</div>
                <div class="stats-label">إجمالي المستخدمين</div>
                <div class="stats-sublabel">{{ stats.active_users }} نشط</div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-success">
            <div class="stats-icon">
                <i class="fas fa-user-tie"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number">{{ stats.total_clients }}</div>
                <div class="stats-label">إجمالي الموكلين</div>
                <div class="stats-sublabel">{{ monthly_stats.new_clients }} جديد هذا الشهر</div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-warning">
            <div class="stats-icon">
                <i class="fas fa-folder"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number">{{ stats.total_dossiers }}</div>
                <div class="stats-label">إجمالي الملفات</div>
                <div class="stats-sublabel">{{ stats.active_dossiers }} نشط</div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stats-card bg-info">
            <div class="stats-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-content">
                <div class="stats-number">{{ stats.total_audiences }}</div>
                <div class="stats-label">إجمالي الجلسات</div>
                <div class="stats-sublabel">{{ stats.today_audiences }} اليوم</div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- توزيع المستخدمين حسب الدور -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع المستخدمين حسب الدور
                </h5>
            </div>
            <div class="card-body">
                <canvas id="userRolesChart" width="400" height="200"></canvas>
                
                <div class="mt-3">
                    {% for role, count in user_roles %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="badge bg-{{ 'primary' if role == 'admin' else 'success' if role == 'lawyer' else 'info' if role == 'assistant' else 'secondary' }}">
                                {% if role == 'admin' %}مدير
                                {% elif role == 'lawyer' %}محامي
                                {% elif role == 'assistant' %}مساعد
                                {% else %}مشاهد{% endif %}
                            </span>
                        </div>
                        <strong>{{ count }}</strong>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- النشاط الشهري -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    النشاط الشهري
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-4">
                        <div class="monthly-stat">
                            <div class="monthly-number text-primary">{{ monthly_stats.new_clients }}</div>
                            <div class="monthly-label">موكلين جدد</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="monthly-stat">
                            <div class="monthly-number text-success">{{ monthly_stats.new_dossiers }}</div>
                            <div class="monthly-label">ملفات جديدة</div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="monthly-stat">
                            <div class="monthly-number text-warning">{{ monthly_stats.monthly_audiences }}</div>
                            <div class="monthly-label">جلسات الشهر</div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <canvas id="monthlyActivityChart" width="400" height="150"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- آخر المستخدمين المسجلين -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>
                    آخر المستخدمين المسجلين
                </h5>
            </div>
            <div class="card-body">
                {% if recent_users %}
                    <div class="list-group list-group-flush">
                        {% for user in recent_users %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="user-avatar me-3">
                                    {{ user.nom_complet[0] }}
                                </div>
                                <div>
                                    <h6 class="mb-0">{{ user.nom_complet }}</h6>
                                    <small class="text-muted">{{ user.username }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{{ 'primary' if user.role == 'admin' else 'success' if user.role == 'lawyer' else 'info' if user.role == 'assistant' else 'secondary' }}">
                                    {% if user.role == 'admin' %}مدير
                                    {% elif user.role == 'lawyer' %}محامي
                                    {% elif user.role == 'assistant' %}مساعد
                                    {% else %}مشاهد{% endif %}
                                </span>
                                <br>
                                <small class="text-muted">{{ user.created_at.strftime('%d/%m/%Y') if user.created_at else '' }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                            <i class="fas fa-users me-1"></i>
                            عرض جميع المستخدمين
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-users fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">لا توجد مستخدمين جدد</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- إجراءات سريعة -->
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('admin.create_user') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة مستخدم جديد
                    </a>
                    
                    <button type="button" class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-download me-2"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    
                    <a href="{{ url_for('admin.system') }}" class="btn btn-info">
                        <i class="fas fa-cogs me-2"></i>
                        إعدادات النظام
                    </a>
                    
                    <a href="{{ url_for('admin.logs') }}" class="btn btn-warning">
                        <i class="fas fa-file-alt me-2"></i>
                        عرض السجلات
                    </a>
                    
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        العودة للوحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات النظام -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="system-info">
                            <i class="fas fa-database text-primary"></i>
                            <div>
                                <strong>قاعدة البيانات</strong>
                                <p>SQLite</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="system-info">
                            <i class="fas fa-code text-success"></i>
                            <div>
                                <strong>إصدار النظام</strong>
                                <p>1.0.0</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="system-info">
                            <i class="fas fa-server text-warning"></i>
                            <div>
                                <strong>الخادم</strong>
                                <p>Flask Development</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="system-info">
                            <i class="fas fa-clock text-info"></i>
                            <div>
                                <strong>وقت التشغيل</strong>
                                <p id="uptime">جاري الحساب...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.stats-card.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stats-card.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stats-card.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.stats-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.stats-content {
    flex: 1;
}

.stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stats-label {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.stats-sublabel {
    font-size: 0.875rem;
    opacity: 0.8;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.monthly-stat {
    padding: 1rem;
}

.monthly-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.monthly-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.system-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}

.system-info i {
    font-size: 1.5rem;
}

.system-info strong {
    display: block;
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.system-info p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// رسم بياني لتوزيع المستخدمين
const userRolesCtx = document.getElementById('userRolesChart').getContext('2d');
const userRolesChart = new Chart(userRolesCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for role, count in user_roles %}
            '{% if role == "admin" %}مدير{% elif role == "lawyer" %}محامي{% elif role == "assistant" %}مساعد{% else %}مشاهد{% endif %}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [{% for role, count in user_roles %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#17a2b8',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

// رسم بياني للنشاط الشهري
const monthlyActivityCtx = document.getElementById('monthlyActivityChart').getContext('2d');
const monthlyActivityChart = new Chart(monthlyActivityCtx, {
    type: 'line',
    data: {
        labels: ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
        datasets: [{
            label: 'النشاط',
            data: [12, 19, 15, 25],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// إنشاء نسخة احتياطية
function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        fetch('{{ url_for("admin.backup") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                'csrf_token': document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || ''
            })
        })
        .then(response => {
            if (response.ok) {
                alert('تم إنشاء النسخة الاحتياطية بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ في إنشاء النسخة الاحتياطية');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ في إنشاء النسخة الاحتياطية');
        });
    }
}

// حساب وقت التشغيل
function updateUptime() {
    const startTime = new Date('{{ moment().format("YYYY-MM-DD HH:mm:ss") if moment else "2024-01-01 00:00:00" }}');
    const now = new Date();
    const diff = now - startTime;
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    document.getElementById('uptime').textContent = `${hours} ساعة و ${minutes} دقيقة`;
}

// تحديث وقت التشغيل كل دقيقة
updateUptime();
setInterval(updateUptime, 60000);
</script>
{% endblock %}
