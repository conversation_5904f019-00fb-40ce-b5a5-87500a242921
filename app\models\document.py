# -*- coding: utf-8 -*-
"""
نموذج الوثائق والملفات المرفقة
"""

import os
from datetime import datetime
from werkzeug.utils import secure_filename
from app import db

class DocumentCategory(db.Model):
    """تصنيفات الوثائق"""
    
    __tablename__ = 'document_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    name_ar = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    icon = db.Column(db.String(50), default='fas fa-file')
    color = db.Column(db.String(20), default='primary')
    is_active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    sort_order = db.Column(db.Integer, default=0)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    documents = db.relationship('Document', backref='category', lazy='dynamic')
    
    def __repr__(self):
        return f'<DocumentCategory {self.name_ar}>'
    
    @staticmethod
    def get_default_categories():
        """الحصول على التصنيفات الافتراضية"""
        return [
            {
                'name': 'judgment',
                'name_ar': 'الأحكام',
                'description': 'نسخ من الأحكام والقرارات القضائية',
                'icon': 'fas fa-gavel',
                'color': 'danger',
                'sort_order': 1
            },
            {
                'name': 'petition',
                'name_ar': 'المقالات الافتتاحية',
                'description': 'المقالات الافتتاحية والدعاوى',
                'icon': 'fas fa-file-signature',
                'color': 'primary',
                'sort_order': 2
            },
            {
                'name': 'notification',
                'name_ar': 'محاضر التبليغ',
                'description': 'محاضر التبليغ والإعلان',
                'icon': 'fas fa-bell',
                'color': 'warning',
                'sort_order': 3
            },
            {
                'name': 'identity',
                'name_ar': 'الوثائق التعريفية',
                'description': 'البطائق الوطنية وجوازات السفر',
                'icon': 'fas fa-id-card',
                'color': 'info',
                'sort_order': 4
            },
            {
                'name': 'official',
                'name_ar': 'الوثائق الرسمية',
                'description': 'الوثائق الرسمية والشهادات',
                'icon': 'fas fa-certificate',
                'color': 'success',
                'sort_order': 5
            },
            {
                'name': 'contract',
                'name_ar': 'العقود والاتفاقيات',
                'description': 'العقود والاتفاقيات القانونية',
                'icon': 'fas fa-handshake',
                'color': 'secondary',
                'sort_order': 6
            },
            {
                'name': 'correspondence',
                'name_ar': 'المراسلات',
                'description': 'المراسلات والخطابات',
                'icon': 'fas fa-envelope',
                'color': 'dark',
                'sort_order': 7
            },
            {
                'name': 'evidence',
                'name_ar': 'الأدلة والمستندات',
                'description': 'الأدلة والمستندات المؤيدة',
                'icon': 'fas fa-search',
                'color': 'light',
                'sort_order': 8
            },
            {
                'name': 'other',
                'name_ar': 'أخرى',
                'description': 'وثائق أخرى متنوعة',
                'icon': 'fas fa-file-alt',
                'color': 'muted',
                'sort_order': 9
            }
        ]

class Document(db.Model):
    """نموذج الوثائق"""
    
    __tablename__ = 'documents'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات أساسية
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    
    # معلومات الملف
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # بالبايت
    file_type = db.Column(db.String(50))  # MIME type
    file_extension = db.Column(db.String(10))
    
    # التصنيف والربط
    category_id = db.Column(db.Integer, db.ForeignKey('document_categories.id'), nullable=False)
    dossier_id = db.Column(db.Integer, db.ForeignKey('dossiers.id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'))
    audience_id = db.Column(db.Integer, db.ForeignKey('audiences.id'))
    
    # معلومات إضافية
    document_date = db.Column(db.Date)  # تاريخ الوثيقة
    is_confidential = db.Column(db.Boolean, default=False, nullable=False)
    is_original = db.Column(db.Boolean, default=False, nullable=False)  # هل هي النسخة الأصلية
    version = db.Column(db.Integer, default=1)
    
    # معلومات المستخدم
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Document {self.title}>'
    
    @property
    def file_size_formatted(self):
        """حجم الملف مُنسق"""
        if not self.file_size:
            return "غير محدد"
        
        size = self.file_size
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"
    
    @property
    def is_image(self):
        """هل الملف صورة"""
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        return self.file_extension.lower() in image_extensions
    
    @property
    def is_pdf(self):
        """هل الملف PDF"""
        return self.file_extension.lower() == '.pdf'
    
    @property
    def is_document(self):
        """هل الملف مستند نصي"""
        doc_extensions = ['.doc', '.docx', '.txt', '.rtf', '.odt']
        return self.file_extension.lower() in doc_extensions
    
    def get_icon_class(self):
        """الحصول على أيقونة الملف"""
        if self.is_pdf:
            return 'fas fa-file-pdf text-danger'
        elif self.is_image:
            return 'fas fa-file-image text-success'
        elif self.is_document:
            return 'fas fa-file-word text-primary'
        elif self.file_extension.lower() in ['.xls', '.xlsx']:
            return 'fas fa-file-excel text-success'
        elif self.file_extension.lower() in ['.zip', '.rar', '.7z']:
            return 'fas fa-file-archive text-warning'
        else:
            return 'fas fa-file text-secondary'
    
    def get_download_url(self):
        """رابط تحميل الملف"""
        from flask import url_for
        return url_for('documents.download', id=self.id)
    
    def get_view_url(self):
        """رابط عرض الملف"""
        from flask import url_for
        return url_for('documents.view', id=self.id)
    
    @staticmethod
    def allowed_file(filename):
        """التحقق من أن الملف مسموح"""
        ALLOWED_EXTENSIONS = {
            'pdf', 'doc', 'docx', 'txt', 'rtf', 'odt',
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
            'xls', 'xlsx', 'zip', 'rar', '7z'
        }
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
    
    @staticmethod
    def get_file_extension(filename):
        """الحصول على امتداد الملف"""
        return '.' + filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'filename': self.filename,
            'original_filename': self.original_filename,
            'file_size': self.file_size,
            'file_size_formatted': self.file_size_formatted,
            'file_type': self.file_type,
            'file_extension': self.file_extension,
            'category': self.category.name_ar if self.category else None,
            'document_date': self.document_date.isoformat() if self.document_date else None,
            'is_confidential': self.is_confidential,
            'is_original': self.is_original,
            'version': self.version,
            'uploaded_by': self.uploader.nom_complet if self.uploader else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'download_url': self.get_download_url(),
            'view_url': self.get_view_url(),
            'icon_class': self.get_icon_class()
        }

# إضافة العلاقات للنماذج الموجودة
def add_document_relationships():
    """إضافة العلاقات للنماذج الموجودة"""
    from app.models.dossier import Dossier
    from app.models.client import Client
    from app.models.audience import Audience
    from app.models.user import User
    
    # إضافة العلاقات إذا لم تكن موجودة
    if not hasattr(Dossier, 'documents'):
        Dossier.documents = db.relationship('Document', backref='dossier', lazy='dynamic', cascade='all, delete-orphan')
    
    if not hasattr(Client, 'documents'):
        Client.documents = db.relationship('Document', backref='client', lazy='dynamic')
    
    if not hasattr(Audience, 'documents'):
        Audience.documents = db.relationship('Document', backref='audience', lazy='dynamic')
    
    if not hasattr(User, 'uploaded_documents'):
        User.uploaded_documents = db.relationship('Document', backref='uploader', lazy='dynamic')
