{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-alt me-2"></i>
        الجلسات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('audiences.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة جلسة جديدة
            </a>
            <a href="{{ url_for('audiences.bulk_create') }}" class="btn btn-success">
                <i class="fas fa-layer-group me-1"></i>
                إنشاء جلسات متعددة
            </a>
            {% endif %}
            <a href="{{ url_for('audiences.print_today') }}" class="btn btn-outline-secondary">
                <i class="fas fa-print me-1"></i>
                طباعة جلسات اليوم
            </a>
        </div>
    </div>
</div>

<!-- Quick Links -->
<div class="row mb-4">
    <div class="col-md-3">
        <a href="{{ url_for('audiences.today') }}" class="card text-decoration-none">
            <div class="card-body text-center">
                <i class="fas fa-calendar-day fa-2x text-danger mb-2"></i>
                <h5 class="card-title">جلسات اليوم</h5>
                <p class="card-text text-muted">عرض جلسات اليوم فقط</p>
            </div>
        </a>
    </div>
    <div class="col-md-3">
        <a href="{{ url_for('audiences.upcoming') }}" class="card text-decoration-none">
            <div class="card-body text-center">
                <i class="fas fa-calendar-week fa-2x text-warning mb-2"></i>
                <h5 class="card-title">الجلسات القادمة</h5>
                <p class="card-text text-muted">الجلسات خلال الأسبوع</p>
            </div>
        </a>
    </div>
    <div class="col-md-3">
        <a href="{{ url_for('audiences.index') }}?statut=منعقدة" class="card text-decoration-none">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h5 class="card-title">الجلسات المنعقدة</h5>
                <p class="card-text text-muted">الجلسات المكتملة</p>
            </div>
        </a>
    </div>
    <div class="col-md-3">
        <a href="{{ url_for('audiences.index') }}?statut=مؤجلة" class="card text-decoration-none">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                <h5 class="card-title">الجلسات المؤجلة</h5>
                <p class="card-text text-muted">الجلسات المؤجلة</p>
            </div>
        </a>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="date_filter" class="form-label">تاريخ الجلسة</label>
                <input type="date" class="form-control" id="date_filter" name="date" 
                       value="{{ date_filter }}">
            </div>
            <div class="col-md-2">
                <label for="tribunal" class="form-label">المحكمة</label>
                <select class="form-select" id="tribunal" name="tribunal">
                    <option value="">جميع المحاكم</option>
                    {% for tribunal in tribunaux %}
                    <option value="{{ tribunal }}" 
                            {% if tribunal_filter == tribunal %}selected{% endif %}>
                        {{ tribunal }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">نوع الملف</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for type_dossier in types_dossier %}
                    <option value="{{ type_dossier }}" 
                            {% if type_filter == type_dossier %}selected{% endif %}>
                        {{ type_dossier }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="statut" class="form-label">الحالة</label>
                <select class="form-select" id="statut" name="statut">
                    <option value="">جميع الحالات</option>
                    {% for statut in statuts %}
                    <option value="{{ statut }}" 
                            {% if statut_filter == statut %}selected{% endif %}>
                        {{ statut }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الجلسات ({{ audiences.total }} جلسة)
        </h5>
    </div>
    <div class="card-body">
        {% if audiences.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>التاريخ والوقت</th>
                            <th>الملف</th>
                            <th>الموكل</th>
                            <th>المحكمة</th>
                            <th>النوع</th>
                            <th>الموضوع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for audience in audiences.items %}
                        <tr class="{{ audience.get_urgency_class() }}">
                            <td>
                                <div>
                                    <strong>{{ audience.date_audience.strftime('%d/%m/%Y') }}</strong>
                                    {% if audience.is_important %}
                                    <i class="fas fa-star text-warning ms-1" title="جلسة مهمة"></i>
                                    {% endif %}
                                </div>
                                <small class="text-muted">
                                    {{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '09:00' }}
                                </small>
                                {% if audience.is_today() %}
                                <br><span class="badge bg-danger">اليوم</span>
                                {% elif audience.is_tomorrow() %}
                                <br><span class="badge bg-warning">غداً</span>
                                {% elif audience.days_until() <= 3 and audience.is_upcoming() %}
                                <br><span class="badge bg-info">{{ audience.days_until() }} أيام</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                                    {{ audience.numero_dossier }}
                                </a>
                            </td>
                            <td>
                                <a href="{{ url_for('clients.view', id=audience.dossier.client.id) }}" class="text-decoration-none">
                                    {{ audience.client_nom }}
                                </a>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ audience.tribunal }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ audience.type_dossier }}</span>
                            </td>
                            <td>
                                {% if audience.demande %}
                                {{ audience.demande[:40] }}{% if audience.demande|length > 40 %}...{% endif %}
                                {% else %}
                                <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ audience.get_status_color() }}">
                                    {{ audience.statut }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('audiences.view', id=audience.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="{{ url_for('audiences.edit', id=audience.id) }}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if current_user.has_permission('delete') %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete({{ audience.id }}, '{{ audience.numero_dossier }}')" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if audiences.pages > 1 %}
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if audiences.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('audiences.index', page=audiences.prev_num, date=date_filter, tribunal=tribunal_filter, type=type_filter, statut=statut_filter) }}">
                            السابق
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in audiences.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != audiences.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('audiences.index', page=page_num, date=date_filter, tribunal=tribunal_filter, type=type_filter, statut=statut_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if audiences.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('audiences.index', page=audiences.next_num, date=date_filter, tribunal=tribunal_filter, type=type_filter, statut=statut_filter) }}">
                            التالي
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد جلسات</h5>
                {% if date_filter or tribunal_filter or type_filter or statut_filter %}
                <p class="text-muted">لم يتم العثور على نتائج تطابق معايير البحث</p>
                <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-primary">
                    عرض جميع الجلسات
                </a>
                {% else %}
                <p class="text-muted">ابدأ بإضافة جلسة جديدة</p>
                {% if current_user.has_permission('create') %}
                <a href="{{ url_for('audiences.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة جلسة جديدة
                </a>
                {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف جلسة الملف <strong id="audienceNumber"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(audienceId, audienceNumber) {
    document.getElementById('audienceNumber').textContent = audienceNumber;
    document.getElementById('deleteForm').action = `/audiences/${audienceId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('date_filter');
    if (!dateInput.value && window.location.search.indexOf('date=') === -1) {
        // لا تعيين تاريخ افتراضي للسماح بعرض جميع الجلسات
    }
});
</script>
{% endblock %}
