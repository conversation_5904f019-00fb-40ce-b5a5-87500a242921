{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-open me-2"></i>
        الملفات
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('dossiers.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة ملف جديد
            </a>
            {% endif %}
            <a href="{{ url_for('dossiers.export') }}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="ابحث برقم الملف، الموكل أو الطرف المقابل">
            </div>
            <div class="col-md-2">
                <label for="tribunal" class="form-label">المحكمة</label>
                <select class="form-select" id="tribunal" name="tribunal">
                    <option value="">جميع المحاكم</option>
                    {% for tribunal in tribunaux %}
                    <option value="{{ tribunal }}" 
                            {% if tribunal_filter == tribunal %}selected{% endif %}>
                        {{ tribunal }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="type" class="form-label">نوع الملف</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for type_dossier in types_dossier %}
                    <option value="{{ type_dossier.id }}" 
                            {% if type_filter == type_dossier.id %}selected{% endif %}>
                        {{ type_dossier.nom }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="situation" class="form-label">الحالة</label>
                <select class="form-select" id="situation" name="situation">
                    <option value="">جميع الحالات</option>
                    {% for situation in situations %}
                    <option value="{{ situation }}" 
                            {% if situation_filter == situation %}selected{% endif %}>
                        {{ situation }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الملفات ({{ dossiers.total }} ملف)
        </h5>
    </div>
    <div class="card-body">
        {% if dossiers.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الملف</th>
                            <th>الموكل</th>
                            <th>المحكمة</th>
                            <th>النوع</th>
                            <th>الحالة</th>
                            <th>تاريخ الفتح</th>
                            <th>الجلسات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dossier in dossiers.items %}
                        <tr class="{{ 'table-warning' if dossier.is_urgent else '' }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong>{{ dossier.numero_affaire }}</strong>
                                        {% if dossier.is_urgent %}
                                        <span class="badge bg-danger ms-1">مستعجل</span>
                                        {% endif %}
                                        {% if dossier.adversaire %}
                                        <br><small class="text-muted">ضد: {{ dossier.adversaire }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <a href="{{ url_for('clients.view', id=dossier.client.id) }}" class="text-decoration-none">
                                    {{ dossier.client.nom_complet }}
                                </a>
                                {% if dossier.client.telephone %}
                                <br><small class="text-muted">{{ dossier.client.telephone }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">{{ dossier.tribunal }}</span>
                            </td>
                            <td>
                                {% if dossier.type_dossier_rel %}
                                <span class="badge" style="background-color: {{ dossier.type_dossier_rel.couleur }}">
                                    {{ dossier.type_dossier_rel.nom }}
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ dossier.get_status_color() }}">
                                    {{ dossier.situation }}
                                </span>
                            </td>
                            <td>
                                <small>{{ dossier.date_ouverture.strftime('%d/%m/%Y') if dossier.date_ouverture else '' }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ dossier.get_audiences_count() }}</span>
                                {% set next_audience = dossier.get_next_audience() %}
                                {% if next_audience %}
                                <br><small class="text-success">{{ next_audience.date_audience.strftime('%d/%m') }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('dossiers.view', id=dossier.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="{{ url_for('dossiers.edit', id=dossier.id) }}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-success" 
                                            onclick="extractActions({{ dossier.id }})" title="جلب الإجراء">
                                        <i class="fas fa-download"></i>
                                    </button>
                                    {% endif %}
                                    {% if current_user.has_permission('delete') %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete({{ dossier.id }}, '{{ dossier.numero_affaire }}')" 
                                            title="أرشفة">
                                        <i class="fas fa-archive"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if dossiers.pages > 1 %}
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if dossiers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('dossiers.index', page=dossiers.prev_num, search=search, tribunal=tribunal_filter, type=type_filter, situation=situation_filter) }}">
                            السابق
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in dossiers.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != dossiers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('dossiers.index', page=page_num, search=search, tribunal=tribunal_filter, type=type_filter, situation=situation_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if dossiers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('dossiers.index', page=dossiers.next_num, search=search, tribunal=tribunal_filter, type=type_filter, situation=situation_filter) }}">
                            التالي
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد ملفات</h5>
                {% if search or tribunal_filter or type_filter or situation_filter %}
                <p class="text-muted">لم يتم العثور على نتائج تطابق معايير البحث</p>
                <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-primary">
                    عرض جميع الملفات
                </a>
                {% else %}
                <p class="text-muted">ابدأ بإضافة ملف جديد</p>
                {% if current_user.has_permission('create') %}
                <a href="{{ url_for('dossiers.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة ملف جديد
                </a>
                {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الأرشفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أرشفة الملف <strong id="dossierNumber"></strong>؟</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم نقل الملف إلى الأرشيف ولن يظهر في القائمة الرئيسية
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-warning">أرشفة</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Extract Actions Modal -->
<div class="modal fade" id="extractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استخراج الإجراءات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>سيتم استخراج آخر الإجراءات والقرارات من موقع المحاكم المغربية</p>
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status" id="extractSpinner" style="display: none;">
                        <span class="visually-hidden">جاري الاستخراج...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="extractForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-success" id="extractBtn">
                        <i class="fas fa-download me-1"></i>
                        استخراج الآن
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(dossierId, dossierNumber) {
    document.getElementById('dossierNumber').textContent = dossierNumber;
    document.getElementById('deleteForm').action = `/dossiers/${dossierId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

function extractActions(dossierId) {
    document.getElementById('extractForm').action = `/dossiers/${dossierId}/extract`;
    
    const modal = new bootstrap.Modal(document.getElementById('extractModal'));
    modal.show();
    
    // إظهار spinner عند الإرسال
    document.getElementById('extractForm').addEventListener('submit', function() {
        document.getElementById('extractSpinner').style.display = 'block';
        document.getElementById('extractBtn').disabled = true;
        document.getElementById('extractBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستخراج...';
    });
}
</script>
{% endblock %}
