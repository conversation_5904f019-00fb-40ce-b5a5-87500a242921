# -*- coding: utf-8 -*-
"""
نماذج الملفات
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, SelectField, DateField, DecimalField, BooleanField
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from app.models.dossier import Dossier
from app.models.client import Client
from app.models.type_dossier import TypeDossier

class DossierForm(FlaskForm):
    """نموذج إضافة/تعديل الملف"""
    
    numero_affaire = StringField('رقم الملف', validators=[DataRequired(), Length(max=50)],
                                render_kw={'class': 'form-control', 'placeholder': 'مثال: 2025/1201/89'})
    
    tribunal = SelectField('المحكمة', validators=[DataRequired()],
                          choices=[
                              ('', 'اختر المحكمة'),
                              ('ابتدائية', 'ابتدائية'),
                              ('استئناف', 'استئناف'),
                              ('نقض', 'نقض'),
                              ('تجارية', 'تجارية'),
                              ('إدارية', 'إدارية')
                          ],
                          render_kw={'class': 'form-select'})
    
    type_dossier_id = SelectField('نوع الملف', validators=[DataRequired()],
                                 coerce=int,
                                 render_kw={'class': 'form-select'})
    
    client_id = SelectField('الموكل', validators=[DataRequired()],
                           coerce=int,
                           render_kw={'class': 'form-select'})
    
    situation = SelectField('حالة الملف', validators=[DataRequired()],
                           choices=[
                               ('في انتظار التعيين', 'في انتظار التعيين'),
                               ('في الجلسات', 'في الجلسات'),
                               ('مداولة', 'مداولة'),
                               ('تأمل', 'تأمل'),
                               ('منتهي', 'منتهي')
                           ],
                           default='في انتظار التعيين',
                           render_kw={'class': 'form-select'})
    
    date_ouverture = DateField('تاريخ فتح الملف', validators=[DataRequired()],
                              render_kw={'class': 'form-control'})
    
    date_cloture = DateField('تاريخ إغلاق الملف', validators=[Optional()],
                            render_kw={'class': 'form-control'})
    
    adversaire = StringField('الطرف المقابل', validators=[Optional(), Length(max=200)],
                            render_kw={'class': 'form-control', 'placeholder': 'اسم الطرف المقابل'})
    
    objet = TextAreaField('موضوع الملف', validators=[Optional()],
                         render_kw={'class': 'form-control', 'rows': 3, 'placeholder': 'وصف موضوع الملف'})
    
    montant_litige = DecimalField('مبلغ النزاع (درهم)', validators=[Optional(), NumberRange(min=0)],
                                 render_kw={'class': 'form-control', 'placeholder': '0.00'})
    
    honoraires = DecimalField('الأتعاب (درهم)', validators=[Optional(), NumberRange(min=0)],
                             render_kw={'class': 'form-control', 'placeholder': '0.00'})
    
    is_urgent = BooleanField('ملف مستعجل', render_kw={'class': 'form-check-input'})
    
    notes = TextAreaField('ملاحظات', validators=[Optional()],
                         render_kw={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية'})
    
    def __init__(self, *args, **kwargs):
        super(DossierForm, self).__init__(*args, **kwargs)
        
        # تحديث خيارات نوع الملف
        self.type_dossier_id.choices = [(0, 'اختر نوع الملف')] + [
            (t.id, t.nom) for t in TypeDossier.get_active_types()
        ]
        
        # تحديث خيارات الموكلين
        self.client_id.choices = [(0, 'اختر الموكل')] + [
            (c.id, c.nom_complet) for c in Client.query.filter_by(is_active=True).order_by(Client.nom_complet).all()
        ]
    
    def validate_numero_affaire(self, numero_affaire):
        """التحقق من عدم تكرار رقم الملف"""
        # في حالة التعديل، استثناء الملف الحالي
        query = Dossier.query.filter_by(numero_affaire=numero_affaire.data)
        if hasattr(self, '_obj') and self._obj:
            query = query.filter(Dossier.id != self._obj.id)
        
        if query.first():
            raise ValidationError('رقم الملف مستخدم بالفعل')
    
    def validate_date_cloture(self, date_cloture):
        """التحقق من أن تاريخ الإغلاق بعد تاريخ الفتح"""
        if date_cloture.data and self.date_ouverture.data:
            if date_cloture.data < self.date_ouverture.data:
                raise ValidationError('تاريخ الإغلاق يجب أن يكون بعد تاريخ الفتح')

class DossierSearchForm(FlaskForm):
    """نموذج البحث في الملفات"""
    
    search = StringField('البحث', validators=[Optional()],
                        render_kw={'class': 'form-control', 'placeholder': 'ابحث برقم الملف، الموكل أو الطرف المقابل'})
    
    tribunal = SelectField('المحكمة', validators=[Optional()],
                          choices=[
                              ('', 'جميع المحاكم'),
                              ('ابتدائية', 'ابتدائية'),
                              ('استئناف', 'استئناف'),
                              ('نقض', 'نقض'),
                              ('تجارية', 'تجارية'),
                              ('إدارية', 'إدارية')
                          ],
                          render_kw={'class': 'form-select'})
    
    type_dossier_id = SelectField('نوع الملف', validators=[Optional()],
                                 coerce=int,
                                 render_kw={'class': 'form-select'})
    
    situation = SelectField('حالة الملف', validators=[Optional()],
                           choices=[
                               ('', 'جميع الحالات'),
                               ('في انتظار التعيين', 'في انتظار التعيين'),
                               ('في الجلسات', 'في الجلسات'),
                               ('مداولة', 'مداولة'),
                               ('تأمل', 'تأمل'),
                               ('منتهي', 'منتهي')
                           ],
                           render_kw={'class': 'form-select'})
    
    def __init__(self, *args, **kwargs):
        super(DossierSearchForm, self).__init__(*args, **kwargs)
        # تحديث خيارات نوع الملف
        self.type_dossier_id.choices = [(0, 'جميع الأنواع')] + [
            (t.id, t.nom) for t in TypeDossier.get_active_types()
        ]
