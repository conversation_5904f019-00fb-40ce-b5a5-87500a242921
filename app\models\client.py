# -*- coding: utf-8 -*-
"""
نموذج الموكلين
"""

from datetime import datetime
from app import db

class Client(db.Model):
    """نموذج الموكلين"""
    
    __tablename__ = 'clients'
    
    id = db.Column(db.Integer, primary_key=True)
    nom_complet = db.Column(db.String(200), nullable=False, index=True)
    email = db.Column(db.String(120), index=True)
    telephone = db.Column(db.String(20))
    telephone_2 = db.Column(db.String(20))  # رقم إضافي
    adresse = db.Column(db.Text)
    
    # معلومات شخصية إضافية
    cin = db.Column(db.String(20), unique=True)  # رقم البطاقة الوطنية
    date_naissance = db.Column(db.Date)
    lieu_naissance = db.Column(db.String(100))
    profession = db.Column(db.String(100))
    situation_familiale = db.Column(db.String(20))  # أعزب، متزوج، مطلق، أرمل
    
    # نوع الملف الأساسي
    type_dossier_id = db.Column(db.Integer, db.ForeignKey('type_dossiers.id'), nullable=False)
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    
    # تواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    dossiers = db.relationship('Dossier', backref='client', lazy='dynamic', cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Client {self.nom_complet}>'
    
    @property
    def type_dossier(self):
        """نوع الملف"""
        from app.models.type_dossier import TypeDossier
        return TypeDossier.query.get(self.type_dossier_id)
    
    def get_dossiers_count(self):
        """عدد الملفات"""
        return self.dossiers.count()
    
    def get_active_dossiers_count(self):
        """عدد الملفات النشطة"""
        return self.dossiers.filter_by(is_archived=False).count()
    
    def get_latest_dossier(self):
        """آخر ملف"""
        return self.dossiers.order_by(self.dossiers.desc()).first()
    
    def get_full_contact(self):
        """معلومات الاتصال الكاملة"""
        contact = []
        if self.telephone:
            contact.append(f"هاتف: {self.telephone}")
        if self.telephone_2:
            contact.append(f"هاتف 2: {self.telephone_2}")
        if self.email:
            contact.append(f"بريد: {self.email}")
        return " | ".join(contact)
    
    @staticmethod
    def search(query):
        """البحث في الموكلين"""
        return Client.query.filter(
            db.or_(
                Client.nom_complet.contains(query),
                Client.telephone.contains(query),
                Client.email.contains(query),
                Client.cin.contains(query)
            )
        ).filter_by(is_active=True)
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'nom_complet': self.nom_complet,
            'email': self.email,
            'telephone': self.telephone,
            'telephone_2': self.telephone_2,
            'adresse': self.adresse,
            'cin': self.cin,
            'profession': self.profession,
            'type_dossier': self.type_dossier.nom if self.type_dossier else None,
            'dossiers_count': self.get_dossiers_count(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
