# -*- coding: utf-8 -*-
"""
مسارات الملفات
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.dossier import Dossier
from app.models.client import Client
from app.models.type_dossier import TypeDossier
from app.forms.dossier import DossierForm, DossierSearchForm
from app.blueprints.dossiers import bp

@bp.route('/')
@login_required
def index():
    """قائمة الملفات"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    tribunal_filter = request.args.get('tribunal', '')
    type_filter = request.args.get('type', '', type=int)
    situation_filter = request.args.get('situation', '')

    query = Dossier.query.filter_by(is_archived=False)

    # تطبيق البحث
    if search:
        query = Dossier.search(search)

    # تطبيق الفلاتر
    if tribunal_filter:
        query = query.filter_by(tribunal=tribunal_filter)

    if type_filter:
        query = query.filter_by(type_dossier_id=type_filter)

    if situation_filter:
        query = query.filter_by(situation=situation_filter)

    dossiers = query.order_by(Dossier.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )

    # خيارات الفلاتر
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    types_dossier = TypeDossier.get_active_types()
    situations = ['في الجلسات', 'في انتظار التعيين', 'مداولة', 'تأمل', 'منتهي']

    return render_template('dossiers/index.html',
                         title='الملفات',
                         dossiers=dossiers,
                         tribunaux=tribunaux,
                         types_dossier=types_dossier,
                         situations=situations,
                         search=search,
                         tribunal_filter=tribunal_filter,
                         type_filter=type_filter,
                         situation_filter=situation_filter)

@bp.route('/create', methods=['GET', 'POST'])
@login_required
def create():
    """إضافة ملف جديد"""
    if not current_user.has_permission('create'):
        flash('ليس لديك صلاحية لإضافة ملفات جديدة', 'error')
        return redirect(url_for('dossiers.index'))

    form = DossierForm()

    # إذا تم تمرير client_id في الرابط
    client_id = request.args.get('client_id', type=int)
    if client_id:
        form.client_id.data = client_id

    if form.validate_on_submit():
        dossier = Dossier(
            numero_affaire=form.numero_affaire.data,
            tribunal=form.tribunal.data,
            type_dossier_id=form.type_dossier_id.data,
            client_id=form.client_id.data,
            situation=form.situation.data,
            date_ouverture=form.date_ouverture.data,
            date_cloture=form.date_cloture.data,
            adversaire=form.adversaire.data,
            objet=form.objet.data,
            montant_litige=form.montant_litige.data,
            honoraires=form.honoraires.data,
            is_urgent=form.is_urgent.data,
            notes=form.notes.data
        )

        db.session.add(dossier)
        db.session.commit()

        flash(f'تم إضافة الملف {dossier.numero_affaire} بنجاح', 'success')
        return redirect(url_for('dossiers.view', id=dossier.id))

    return render_template('dossiers/create.html', title='إضافة ملف جديد', form=form)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الملف"""
    dossier = Dossier.query.get_or_404(id)

    # الجلسات المرتبطة
    audiences = dossier.audiences.order_by(dossier.audiences.desc()).all()

    # آخر الإجراءات
    journal_actions = dossier.journal_actions.order_by(dossier.journal_actions.desc()).limit(5).all()

    return render_template('dossiers/view.html',
                         title=f'الملف: {dossier.numero_affaire}',
                         dossier=dossier,
                         audiences=audiences,
                         journal_actions=journal_actions)

@bp.route('/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit(id):
    """تعديل الملف"""
    if not current_user.has_permission('update'):
        flash('ليس لديك صلاحية لتعديل الملفات', 'error')
        return redirect(url_for('dossiers.view', id=id))

    dossier = Dossier.query.get_or_404(id)
    form = DossierForm(obj=dossier)

    if form.validate_on_submit():
        form.populate_obj(dossier)
        db.session.commit()

        flash(f'تم تحديث الملف {dossier.numero_affaire} بنجاح', 'success')
        return redirect(url_for('dossiers.view', id=dossier.id))

    return render_template('dossiers/edit.html',
                         title=f'تعديل الملف: {dossier.numero_affaire}',
                         form=form,
                         dossier=dossier)

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف الملف (أرشفة)"""
    if not current_user.has_permission('delete'):
        flash('ليس لديك صلاحية لحذف الملفات', 'error')
        return redirect(url_for('dossiers.view', id=id))

    dossier = Dossier.query.get_or_404(id)

    # أرشفة الملف بدلاً من حذفه
    dossier.is_archived = True
    db.session.commit()

    flash(f'تم أرشفة الملف {dossier.numero_affaire} بنجاح', 'success')
    return redirect(url_for('dossiers.index'))

@bp.route('/<int:id>/extract', methods=['POST'])
@login_required
def extract_actions(id):
    """استخراج الإجراءات من موقع المحاكم"""
    if not current_user.has_permission('update'):
        flash('ليس لديك صلاحية لاستخراج الإجراءات', 'error')
        return redirect(url_for('dossiers.view', id=id))

    dossier = Dossier.query.get_or_404(id)

    try:
        from app.utils.mahakim_scraper import extract_single_dossier

        # استخراج البيانات
        result = extract_single_dossier(dossier.numero_affaire, dossier.tribunal)

        if result['success']:
            # حفظ الإجراء في قاعدة البيانات
            from app.models.journal_actions import JournalActions

            action = JournalActions(
                dossier_id=dossier.id,
                tribunal=dossier.tribunal,
                numero_affaire=dossier.numero_affaire,
                type_procedure=result['data'].get('type_procedure'),
                decision=result['data'].get('decision'),
                date_decision=result['data'].get('date_decision'),
                prochaine_audience=result['data'].get('prochaine_audience'),
                juge=result['data'].get('juge'),
                greffier=result['data'].get('greffier'),
                extraction_status='success',
                raw_data=str(result['data'])
            )

            db.session.add(action)
            db.session.commit()

            flash('تم استخراج الإجراءات بنجاح من موقع المحاكم', 'success')
        else:
            flash(f'فشل في استخراج الإجراءات: {result.get("error", "خطأ غير معروف")}', 'error')

    except Exception as e:
        flash(f'خطأ في استخراج الإجراءات: {str(e)}', 'error')

    return redirect(url_for('dossiers.view', id=id))

@bp.route('/export')
@login_required
def export():
    """تصدير بيانات الملفات"""
    if not current_user.has_permission('read'):
        flash('ليس لديك صلاحية لتصدير البيانات', 'error')
        return redirect(url_for('dossiers.index'))

    try:
        from flask import make_response
        from app.utils.export_data import export_dossiers_to_excel
        from datetime import date

        # الحصول على جميع الملفات النشطة
        dossiers = Dossier.query.filter_by(is_archived=False).all()

        # تصدير البيانات
        excel_buffer = export_dossiers_to_excel(dossiers)

        # إنشاء الاستجابة
        response = make_response(excel_buffer.read())
        response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        response.headers['Content-Disposition'] = f'attachment; filename=dossiers_{date.today().strftime("%Y%m%d")}.xlsx'

        return response

    except Exception as e:
        flash(f'خطأ في تصدير البيانات: {str(e)}', 'error')
        return redirect(url_for('dossiers.index'))