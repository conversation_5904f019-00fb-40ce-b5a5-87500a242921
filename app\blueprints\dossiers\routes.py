# -*- coding: utf-8 -*-
"""
مسارات الملفات
"""

from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app import db
from app.models.dossier import Dossier
from app.models.client import Client
from app.models.type_dossier import TypeDossier
from app.blueprints.dossiers import bp

@bp.route('/')
@login_required
def index():
    """قائمة الملفات"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    tribunal_filter = request.args.get('tribunal', '')
    type_filter = request.args.get('type', '', type=int)
    situation_filter = request.args.get('situation', '')
    
    query = Dossier.query.filter_by(is_archived=False)
    
    # تطبيق البحث
    if search:
        query = Dossier.search(search)
    
    # تطبيق الفلاتر
    if tribunal_filter:
        query = query.filter_by(tribunal=tribunal_filter)
    
    if type_filter:
        query = query.filter_by(type_dossier_id=type_filter)
    
    if situation_filter:
        query = query.filter_by(situation=situation_filter)
    
    dossiers = query.order_by(Dossier.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # خيارات الفلاتر
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    types_dossier = TypeDossier.get_active_types()
    situations = ['في الجلسات', 'في انتظار التعيين', 'مداولة', 'تأمل', 'منتهي']
    
    return render_template('dossiers/index.html',
                         title='الملفات',
                         dossiers=dossiers,
                         tribunaux=tribunaux,
                         types_dossier=types_dossier,
                         situations=situations,
                         search=search,
                         tribunal_filter=tribunal_filter,
                         type_filter=type_filter,
                         situation_filter=situation_filter)

@bp.route('/create')
@login_required
def create():
    """إضافة ملف جديد"""
    if not current_user.has_permission('create'):
        flash('ليس لديك صلاحية لإضافة ملفات جديدة', 'error')
        return redirect(url_for('dossiers.index'))
    
    return render_template('dossiers/create.html', title='إضافة ملف جديد')

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل الملف"""
    dossier = Dossier.query.get_or_404(id)
    
    # الجلسات المرتبطة
    audiences = dossier.audiences.order_by(dossier.audiences.desc()).all()
    
    # آخر الإجراءات
    journal_actions = dossier.journal_actions.order_by(dossier.journal_actions.desc()).limit(5).all()
    
    return render_template('dossiers/view.html',
                         title=f'الملف: {dossier.numero_affaire}',
                         dossier=dossier,
                         audiences=audiences,
                         journal_actions=journal_actions)
