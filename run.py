#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نقطة بداية تطبيق إدارة مكتب المحامي
"""

import os
from flask.cli import FlaskGroup
from app import create_app, db
from app.models import User, Client, TypeDossier, Dossier, Audience, JournalActions

# إنشاء التطبيق
app = create_app(os.getenv('FLASK_CONFIG') or 'default')
cli = FlaskGroup(app)

@app.shell_context_processor
def make_shell_context():
    """إضافة المتغيرات إلى shell context"""
    return {
        'db': db,
        'User': User,
        'Client': Client,
        'TypeDossier': TypeDossier,
        'Dossier': Dossier,
        'Audience': Audience,
        'JournalActions': JournalActions
    }

@app.cli.command()
def init_db():
    """تهيئة قاعدة البيانات"""
    db.create_all()
    print("تم إنشاء قاعدة البيانات بنجاح!")

@app.cli.command()
def create_admin():
    """إنشاء مستخدم مدير"""
    from werkzeug.security import generate_password_hash
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        nom_complet='المدير العام',
        role='admin',
        is_active=True
    )
    admin.password_hash = generate_password_hash('admin123')
    
    db.session.add(admin)
    db.session.commit()
    print("تم إنشاء المستخدم المدير بنجاح!")
    print("اسم المستخدم: admin")
    print("كلمة المرور: admin123")

@app.cli.command()
def seed_data():
    """إدخال بيانات تجريبية"""
    # إنشاء أنواع الملفات
    types_dossier = [
        'طلاق شقاق',
        'طلاق خلع',
        'جنحي',
        'مدني',
        'تجاري',
        'عقاري',
        'أحوال شخصية',
        'إداري',
        'جنائي',
        'عمالي'
    ]
    
    for type_name in types_dossier:
        if not TypeDossier.query.filter_by(nom=type_name).first():
            type_dossier = TypeDossier(nom=type_name)
            db.session.add(type_dossier)
    
    db.session.commit()
    print("تم إدخال البيانات التجريبية بنجاح!")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
