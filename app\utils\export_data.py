# -*- coding: utf-8 -*-
"""
وحدة تصدير البيانات إلى Excel و CSV
"""

import io
import pandas as pd
from datetime import datetime, date
from typing import List, Dict, Optional
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class DataExporter:
    """مصدر البيانات"""
    
    def __init__(self):
        """تهيئة مصدر البيانات"""
        self.supported_formats = ['excel', 'csv']
    
    def export_clients(self, clients: List, format_type: str = 'excel') -> io.BytesIO:
        """تصدير بيانات الموكلين"""
        try:
            # تحويل البيانات إلى قاموس
            data = []
            for client in clients:
                data.append({
                    'الرقم': client.id,
                    'الاسم الكامل': client.nom_complet,
                    'رقم الهوية': client.cin or '',
                    'الهاتف': client.telephone or '',
                    'البريد الإلكتروني': client.email or '',
                    'العنوان': client.adresse or '',
                    'المهنة': client.profession or '',
                    'تاريخ الميلاد': client.date_naissance.strftime('%d/%m/%Y') if client.date_naissance else '',
                    'الجنسية': client.nationalite or '',
                    'عدد الملفات': len(client.dossiers) if hasattr(client, 'dossiers') else 0,
                    'تاريخ الإنشاء': client.created_at.strftime('%d/%m/%Y %H:%M') if client.created_at else '',
                    'ملاحظات': client.notes or ''
                })
            
            return self._create_file(data, format_type, 'الموكلين')
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الموكلين: {str(e)}")
            raise
    
    def export_dossiers(self, dossiers: List, format_type: str = 'excel') -> io.BytesIO:
        """تصدير بيانات الملفات"""
        try:
            data = []
            for dossier in dossiers:
                data.append({
                    'الرقم': dossier.id,
                    'رقم الملف': dossier.numero_affaire,
                    'المحكمة': dossier.tribunal,
                    'نوع الملف': dossier.type_dossier_rel.nom if dossier.type_dossier_rel else '',
                    'الموكل': dossier.client.nom_complet if dossier.client else '',
                    'هاتف الموكل': dossier.client.telephone if dossier.client else '',
                    'الحالة': dossier.situation,
                    'تاريخ الفتح': dossier.date_ouverture.strftime('%d/%m/%Y') if dossier.date_ouverture else '',
                    'تاريخ الإغلاق': dossier.date_cloture.strftime('%d/%m/%Y') if dossier.date_cloture else '',
                    'الطرف المقابل': dossier.adversaire or '',
                    'موضوع الملف': dossier.objet or '',
                    'مبلغ النزاع': dossier.montant_litige or 0,
                    'الأتعاب': dossier.honoraires or 0,
                    'مستعجل': 'نعم' if dossier.is_urgent else 'لا',
                    'عدد الجلسات': dossier.get_audiences_count() if hasattr(dossier, 'get_audiences_count') else 0,
                    'الجلسة القادمة': dossier.get_next_audience().date_audience.strftime('%d/%m/%Y') if hasattr(dossier, 'get_next_audience') and dossier.get_next_audience() else '',
                    'ملاحظات': dossier.notes or ''
                })
            
            return self._create_file(data, format_type, 'الملفات')
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الملفات: {str(e)}")
            raise
    
    def export_audiences(self, audiences: List, format_type: str = 'excel') -> io.BytesIO:
        """تصدير بيانات الجلسات"""
        try:
            data = []
            for audience in audiences:
                data.append({
                    'الرقم': audience.id,
                    'تاريخ الجلسة': audience.date_audience.strftime('%d/%m/%Y') if audience.date_audience else '',
                    'وقت الجلسة': audience.heure_audience.strftime('%H:%M') if audience.heure_audience else '',
                    'رقم الملف': audience.numero_dossier,
                    'الموكل': audience.client_nom,
                    'المحكمة': audience.tribunal,
                    'نوع الملف': audience.type_dossier,
                    'الموضوع': audience.demande or '',
                    'النتيجة': audience.resultat or '',
                    'الحالة': audience.statut,
                    'مهمة': 'نعم' if audience.is_important else 'لا',
                    'المرجع الداخلي': audience.reference_interne or '',
                    'ملاحظات': audience.notes or ''
                })
            
            return self._create_file(data, format_type, 'الجلسات')
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الجلسات: {str(e)}")
            raise
    
    def export_journal_actions(self, actions: List, format_type: str = 'excel') -> io.BytesIO:
        """تصدير بيانات الإجراءات"""
        try:
            data = []
            for action in actions:
                data.append({
                    'الرقم': action.id,
                    'رقم الملف': action.numero_affaire,
                    'المحكمة': action.tribunal,
                    'نوع الإجراء': action.type_procedure or '',
                    'القرار': action.decision or '',
                    'تاريخ القرار': action.date_decision.strftime('%d/%m/%Y') if action.date_decision else '',
                    'الجلسة القادمة': action.prochaine_audience.strftime('%d/%m/%Y') if action.prochaine_audience else '',
                    'القاضي': action.juge or '',
                    'كاتب الضبط': action.greffier or '',
                    'حالة الاستخراج': action.extraction_status,
                    'تاريخ الاستخراج': action.date_extraction.strftime('%d/%m/%Y %H:%M') if action.date_extraction else ''
                })
            
            return self._create_file(data, format_type, 'الإجراءات')
            
        except Exception as e:
            logger.error(f"خطأ في تصدير الإجراءات: {str(e)}")
            raise
    
    def export_comprehensive_report(self, data_dict: Dict, format_type: str = 'excel') -> io.BytesIO:
        """تصدير تقرير شامل"""
        try:
            if format_type == 'excel':
                output = io.BytesIO()
                
                with pd.ExcelWriter(output, engine='openpyxl') as writer:
                    # إضافة ورقة الملخص
                    summary_data = [{
                        'البيان': 'إجمالي الموكلين',
                        'العدد': data_dict.get('total_clients', 0)
                    }, {
                        'البيان': 'إجمالي الملفات',
                        'العدد': data_dict.get('total_dossiers', 0)
                    }, {
                        'البيان': 'الملفات النشطة',
                        'العدد': data_dict.get('active_dossiers', 0)
                    }, {
                        'البيان': 'الجلسات هذا الشهر',
                        'العدد': data_dict.get('monthly_audiences', 0)
                    }, {
                        'البيان': 'تاريخ التقرير',
                        'العدد': datetime.now().strftime('%d/%m/%Y %H:%M')
                    }]
                    
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='الملخص', index=False)
                    
                    # إضافة البيانات الأخرى إذا كانت متوفرة
                    if 'clients' in data_dict:
                        clients_data = self._prepare_clients_data(data_dict['clients'])
                        clients_df = pd.DataFrame(clients_data)
                        clients_df.to_excel(writer, sheet_name='الموكلين', index=False)
                    
                    if 'dossiers' in data_dict:
                        dossiers_data = self._prepare_dossiers_data(data_dict['dossiers'])
                        dossiers_df = pd.DataFrame(dossiers_data)
                        dossiers_df.to_excel(writer, sheet_name='الملفات', index=False)
                    
                    if 'audiences' in data_dict:
                        audiences_data = self._prepare_audiences_data(data_dict['audiences'])
                        audiences_df = pd.DataFrame(audiences_data)
                        audiences_df.to_excel(writer, sheet_name='الجلسات', index=False)
                
                output.seek(0)
                return output
            
            else:
                # للـ CSV، نصدر ملف واحد مع الملخص
                summary_data = [{
                    'البيان': 'إجمالي الموكلين',
                    'العدد': data_dict.get('total_clients', 0)
                }, {
                    'البيان': 'إجمالي الملفات',
                    'العدد': data_dict.get('total_dossiers', 0)
                }, {
                    'البيان': 'الملفات النشطة',
                    'العدد': data_dict.get('active_dossiers', 0)
                }, {
                    'البيان': 'الجلسات هذا الشهر',
                    'العدد': data_dict.get('monthly_audiences', 0)
                }]
                
                return self._create_file(summary_data, format_type, 'التقرير_الشامل')
            
        except Exception as e:
            logger.error(f"خطأ في تصدير التقرير الشامل: {str(e)}")
            raise
    
    def _create_file(self, data: List[Dict], format_type: str, sheet_name: str) -> io.BytesIO:
        """إنشاء ملف Excel أو CSV"""
        output = io.BytesIO()
        
        if not data:
            # إنشاء ملف فارغ مع رسالة
            data = [{'رسالة': 'لا توجد بيانات للتصدير'}]
        
        df = pd.DataFrame(data)
        
        if format_type == 'excel':
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets[sheet_name]
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
        
        else:  # CSV
            df.to_csv(output, index=False, encoding='utf-8-sig')
        
        output.seek(0)
        return output
    
    def _prepare_clients_data(self, clients: List) -> List[Dict]:
        """تحضير بيانات الموكلين للتصدير"""
        data = []
        for client in clients:
            data.append({
                'الاسم الكامل': client.nom_complet,
                'رقم الهوية': client.cin or '',
                'الهاتف': client.telephone or '',
                'البريد الإلكتروني': client.email or '',
                'المهنة': client.profession or '',
                'عدد الملفات': len(client.dossiers) if hasattr(client, 'dossiers') else 0
            })
        return data
    
    def _prepare_dossiers_data(self, dossiers: List) -> List[Dict]:
        """تحضير بيانات الملفات للتصدير"""
        data = []
        for dossier in dossiers:
            data.append({
                'رقم الملف': dossier.numero_affaire,
                'المحكمة': dossier.tribunal,
                'الموكل': dossier.client.nom_complet if dossier.client else '',
                'الحالة': dossier.situation,
                'تاريخ الفتح': dossier.date_ouverture.strftime('%d/%m/%Y') if dossier.date_ouverture else '',
                'مبلغ النزاع': dossier.montant_litige or 0,
                'الأتعاب': dossier.honoraires or 0
            })
        return data
    
    def _prepare_audiences_data(self, audiences: List) -> List[Dict]:
        """تحضير بيانات الجلسات للتصدير"""
        data = []
        for audience in audiences:
            data.append({
                'تاريخ الجلسة': audience.date_audience.strftime('%d/%m/%Y') if audience.date_audience else '',
                'رقم الملف': audience.numero_dossier,
                'الموكل': audience.client_nom,
                'المحكمة': audience.tribunal,
                'الحالة': audience.statut
            })
        return data

# إنشاء مثيل عام
data_exporter = DataExporter()

def export_clients_to_excel(clients: List) -> io.BytesIO:
    """دالة مساعدة لتصدير الموكلين إلى Excel"""
    return data_exporter.export_clients(clients, 'excel')

def export_dossiers_to_excel(dossiers: List) -> io.BytesIO:
    """دالة مساعدة لتصدير الملفات إلى Excel"""
    return data_exporter.export_dossiers(dossiers, 'excel')

def export_audiences_to_excel(audiences: List) -> io.BytesIO:
    """دالة مساعدة لتصدير الجلسات إلى Excel"""
    return data_exporter.export_audiences(audiences, 'excel')
