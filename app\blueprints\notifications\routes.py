# -*- coding: utf-8 -*-
"""
مسارات التنبيهات
"""

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import desc

from app import db
from app.models.notification import Notification, NotificationSettings
from app.models.user import User
from app.blueprints.notifications import bp

@bp.route('/')
@login_required
def index():
    """صفحة التنبيهات الرئيسية"""
    
    # فلاتر
    filter_type = request.args.get('type', '')
    filter_priority = request.args.get('priority', '')
    filter_status = request.args.get('status', '')
    
    # استعلام التنبيهات
    query = Notification.query.filter_by(user_id=current_user.id)
    
    if filter_type:
        query = query.filter_by(type=filter_type)
    
    if filter_priority:
        query = query.filter_by(priority=filter_priority)
    
    if filter_status == 'read':
        query = query.filter_by(is_read=True)
    elif filter_status == 'unread':
        query = query.filter_by(is_read=False)
    elif filter_status == 'overdue':
        query = query.filter(
            Notification.scheduled_time < datetime.utcnow(),
            Notification.is_sent == False
        )
    
    # ترتيب حسب الأولوية والتاريخ
    notifications = query.order_by(
        desc(Notification.priority == 'urgent'),
        desc(Notification.priority == 'high'),
        desc(Notification.scheduled_time)
    ).all()
    
    # إحصائيات
    total_notifications = len(notifications)
    unread_count = len([n for n in notifications if not n.is_read])
    overdue_count = len([n for n in notifications if n.is_overdue()])
    urgent_count = len([n for n in notifications if n.priority == 'urgent'])
    
    return render_template('notifications/index.html',
                         notifications=notifications,
                         total_notifications=total_notifications,
                         unread_count=unread_count,
                         overdue_count=overdue_count,
                         urgent_count=urgent_count,
                         filter_type=filter_type,
                         filter_priority=filter_priority,
                         filter_status=filter_status)

@bp.route('/<int:id>')
@login_required
def view(id):
    """عرض تفاصيل التنبيه"""
    
    notification = Notification.query.filter_by(
        id=id, 
        user_id=current_user.id
    ).first_or_404()
    
    # تحديد التنبيه كمقروء
    if not notification.is_read:
        notification.mark_as_read()
    
    return render_template('notifications/view.html',
                         notification=notification)

@bp.route('/<int:id>/mark_read', methods=['POST'])
@login_required
def mark_read(id):
    """تحديد التنبيه كمقروء"""
    
    notification = Notification.query.filter_by(
        id=id, 
        user_id=current_user.id
    ).first_or_404()
    
    notification.mark_as_read()
    
    return jsonify({'success': True})

@bp.route('/mark_all_read', methods=['POST'])
@login_required
def mark_all_read():
    """تحديد جميع التنبيهات كمقروءة"""
    
    notifications = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).all()
    
    for notification in notifications:
        notification.mark_as_read()
    
    flash(f'تم تحديد {len(notifications)} تنبيه كمقروء', 'success')
    
    return redirect(url_for('notifications.index'))

@bp.route('/<int:id>/delete', methods=['POST'])
@login_required
def delete(id):
    """حذف التنبيه"""
    
    notification = Notification.query.filter_by(
        id=id, 
        user_id=current_user.id
    ).first_or_404()
    
    db.session.delete(notification)
    db.session.commit()
    
    flash('تم حذف التنبيه بنجاح', 'success')
    
    return redirect(url_for('notifications.index'))

@bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """إعدادات التنبيهات"""
    
    # الحصول على إعدادات المستخدم أو إنشاؤها
    user_settings = NotificationSettings.query.filter_by(
        user_id=current_user.id
    ).first()
    
    if not user_settings:
        user_settings = NotificationSettings(user_id=current_user.id)
        db.session.add(user_settings)
        db.session.commit()
    
    if request.method == 'POST':
        # تحديث الإعدادات
        user_settings.audience_notifications = bool(request.form.get('audience_notifications'))
        user_settings.deadline_notifications = bool(request.form.get('deadline_notifications'))
        user_settings.response_notifications = bool(request.form.get('response_notifications'))
        user_settings.general_notifications = bool(request.form.get('general_notifications'))
        
        user_settings.email_notifications = bool(request.form.get('email_notifications'))
        user_settings.email_audience = bool(request.form.get('email_audience'))
        user_settings.email_deadline = bool(request.form.get('email_deadline'))
        user_settings.email_response = bool(request.form.get('email_response'))
        
        user_settings.audience_hours_before = int(request.form.get('audience_hours_before', 24))
        user_settings.deadline_hours_before = int(request.form.get('deadline_hours_before', 48))
        user_settings.response_hours_before = int(request.form.get('response_hours_before', 24))
        
        user_settings.updated_at = datetime.utcnow()
        db.session.commit()
        
        flash('تم حفظ إعدادات التنبيهات بنجاح', 'success')
        return redirect(url_for('notifications.settings'))
    
    return render_template('notifications/settings.html',
                         settings=user_settings)

@bp.route('/api/count')
@login_required
def api_count():
    """API لعدد التنبيهات غير المقروءة"""
    
    unread_count = Notification.query.filter_by(
        user_id=current_user.id,
        is_read=False
    ).count()
    
    return jsonify({'unread_count': unread_count})

@bp.route('/api/latest')
@login_required
def api_latest():
    """API لآخر التنبيهات"""
    
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(desc(Notification.created_at)).limit(5).all()
    
    notifications_data = []
    for notification in notifications:
        notifications_data.append({
            'id': notification.id,
            'title': notification.title,
            'message': notification.message[:100] + '...' if len(notification.message) > 100 else notification.message,
            'type': notification.type,
            'priority': notification.priority,
            'is_read': notification.is_read,
            'created_at': notification.created_at.strftime('%d/%m/%Y %H:%M'),
            'url': url_for('notifications.view', id=notification.id)
        })
    
    return jsonify({'notifications': notifications_data})

# دوال مساعدة
def get_unread_notifications_count(user_id):
    """الحصول على عدد التنبيهات غير المقروءة"""
    return Notification.query.filter_by(
        user_id=user_id,
        is_read=False
    ).count()

def get_urgent_notifications(user_id):
    """الحصول على التنبيهات العاجلة"""
    return Notification.query.filter_by(
        user_id=user_id,
        priority='urgent',
        is_read=False
    ).all()

def create_general_notification(user_id, title, message, priority='medium'):
    """إنشاء تنبيه عام"""
    notification = Notification(
        type='general',
        title=title,
        message=message,
        priority=priority,
        scheduled_time=datetime.utcnow(),
        user_id=user_id
    )
    
    db.session.add(notification)
    db.session.commit()
    
    return notification
