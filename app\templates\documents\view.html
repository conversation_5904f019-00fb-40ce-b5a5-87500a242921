{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="{{ document.get_icon_class() }} me-2"></i>
        {{ document.title }}
        {% if document.is_confidential %}
        <i class="fas fa-lock text-warning ms-2" title="وثيقة سرية"></i>
        {% endif %}
        {% if document.is_original %}
        <i class="fas fa-star text-success ms-2" title="النسخة الأصلية"></i>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('documents.download', id=document.id) }}" class="btn btn-success">
                <i class="fas fa-download me-1"></i>
                تحميل
            </a>
            <a href="{{ url_for('documents.edit', id=document.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                <i class="fas fa-trash me-1"></i>
                حذف
            </button>
        </div>
        <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوثائق
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- معاينة الوثيقة -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-eye me-2"></i>
                    معاينة الوثيقة
                </h5>
            </div>
            <div class="card-body">
                {% if document.is_pdf %}
                    <!-- معاينة PDF -->
                    <div class="pdf-viewer">
                        <embed src="{{ url_for('documents.download', id=document.id) }}" 
                               type="application/pdf" 
                               width="100%" 
                               height="600px">
                        <p class="text-center mt-3">
                            <a href="{{ url_for('documents.download', id=document.id) }}" 
                               class="btn btn-primary">
                                <i class="fas fa-download me-1"></i>
                                تحميل PDF
                            </a>
                        </p>
                    </div>
                {% elif document.is_image %}
                    <!-- معاينة الصورة -->
                    <div class="text-center">
                        <img src="{{ url_for('documents.download', id=document.id) }}" 
                             class="img-fluid rounded shadow" 
                             alt="{{ document.title }}"
                             style="max-height: 600px;">
                    </div>
                {% else %}
                    <!-- أنواع ملفات أخرى -->
                    <div class="text-center py-5">
                        <i class="{{ document.get_icon_class() }} fa-5x mb-4"></i>
                        <h4>{{ document.original_filename }}</h4>
                        <p class="text-muted">لا يمكن معاينة هذا النوع من الملفات</p>
                        <a href="{{ url_for('documents.download', id=document.id) }}" 
                           class="btn btn-primary">
                            <i class="fas fa-download me-1"></i>
                            تحميل الملف
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- الوصف -->
        {% if document.description %}
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-align-left me-2"></i>
                    الوصف
                </h6>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ document.description }}</p>
            </div>
        </div>
        {% endif %}
        
        <!-- الوثائق ذات الصلة -->
        {% if related_documents %}
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-link me-2"></i>
                    وثائق ذات صلة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for related_doc in related_documents %}
                    <div class="col-md-6 mb-3">
                        <div class="d-flex align-items-center p-2 border rounded">
                            <i class="{{ related_doc.get_icon_class() }} fa-lg me-3"></i>
                            <div class="flex-grow-1">
                                <a href="{{ url_for('documents.view', id=related_doc.id) }}" 
                                   class="text-decoration-none fw-bold">
                                    {{ related_doc.title }}
                                </a>
                                <br>
                                <small class="text-muted">{{ related_doc.file_size_formatted }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-lg-4">
        <!-- معلومات الوثيقة -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الوثيقة
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td class="text-muted">التصنيف:</td>
                        <td>
                            <span class="badge bg-{{ document.category.color }}">
                                <i class="{{ document.category.icon }} me-1"></i>
                                {{ document.category.name_ar }}
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td class="text-muted">اسم الملف:</td>
                        <td><small>{{ document.original_filename }}</small></td>
                    </tr>
                    <tr>
                        <td class="text-muted">حجم الملف:</td>
                        <td>{{ document.file_size_formatted }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">نوع الملف:</td>
                        <td>{{ document.file_extension.upper() }}</td>
                    </tr>
                    {% if document.document_date %}
                    <tr>
                        <td class="text-muted">تاريخ الوثيقة:</td>
                        <td>{{ document.document_date.strftime('%d/%m/%Y') }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="text-muted">تاريخ الرفع:</td>
                        <td>{{ document.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                    </tr>
                    <tr>
                        <td class="text-muted">رفع بواسطة:</td>
                        <td>{{ document.uploader.nom_complet }}</td>
                    </tr>
                    {% if document.updated_at != document.created_at %}
                    <tr>
                        <td class="text-muted">آخر تحديث:</td>
                        <td>{{ document.updated_at.strftime('%d/%m/%Y %H:%M') }}</td>
                    </tr>
                    {% endif %}
                    <tr>
                        <td class="text-muted">الإصدار:</td>
                        <td>{{ document.version }}</td>
                    </tr>
                </table>
                
                <!-- خصائص إضافية -->
                <div class="mt-3">
                    {% if document.is_confidential %}
                    <span class="badge bg-warning text-dark me-1">
                        <i class="fas fa-lock me-1"></i>
                        سرية
                    </span>
                    {% endif %}
                    {% if document.is_original %}
                    <span class="badge bg-success me-1">
                        <i class="fas fa-star me-1"></i>
                        نسخة أصلية
                    </span>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- الربط -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-link me-2"></i>
                    الربط
                </h6>
            </div>
            <div class="card-body">
                {% if document.dossier %}
                <div class="mb-3">
                    <label class="text-muted">الملف:</label>
                    <div>
                        <a href="{{ url_for('dossiers.view', id=document.dossier.id) }}" 
                           class="text-decoration-none">
                            <i class="fas fa-folder me-1"></i>
                            {{ document.dossier.numero_affaire }}
                        </a>
                    </div>
                    {% if document.dossier.objet %}
                    <small class="text-muted">{{ document.dossier.objet }}</small>
                    {% endif %}
                </div>
                {% endif %}
                
                {% if document.client %}
                <div class="mb-3">
                    <label class="text-muted">الموكل:</label>
                    <div>
                        <a href="{{ url_for('clients.view', id=document.client.id) }}" 
                           class="text-decoration-none">
                            <i class="fas fa-user me-1"></i>
                            {{ document.client.nom_complet }}
                        </a>
                    </div>
                </div>
                {% endif %}
                
                {% if document.audience %}
                <div class="mb-3">
                    <label class="text-muted">الجلسة:</label>
                    <div>
                        <a href="{{ url_for('audiences.view', id=document.audience.id) }}" 
                           class="text-decoration-none">
                            <i class="fas fa-calendar me-1"></i>
                            {{ document.audience.date_audience.strftime('%d/%m/%Y') }}
                        </a>
                    </div>
                    <small class="text-muted">{{ document.audience.tribunal }}</small>
                </div>
                {% endif %}
                
                {% if not document.dossier and not document.client and not document.audience %}
                <p class="text-muted text-center">
                    <i class="fas fa-unlink fa-2x mb-2"></i>
                    <br>
                    غير مرتبطة
                </p>
                {% endif %}
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('documents.download', id=document.id) }}" 
                       class="btn btn-outline-success btn-sm">
                        <i class="fas fa-download me-1"></i>
                        تحميل الملف
                    </a>
                    
                    {% if document.dossier %}
                    <a href="{{ url_for('documents.upload', dossier_id=document.dossier.id) }}" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        إضافة وثيقة للملف
                    </a>
                    {% endif %}
                    
                    <button type="button" class="btn btn-outline-info btn-sm" 
                            onclick="copyToClipboard('{{ url_for('documents.view', id=document.id, _external=True) }}')">
                        <i class="fas fa-copy me-1"></i>
                        نسخ الرابط
                    </button>
                    
                    <a href="{{ url_for('documents.index', category_id=document.category.id) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-tags me-1"></i>
                        وثائق مشابهة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مودال تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذه الوثيقة؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف الملف نهائياً من النظام.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="POST" action="{{ url_for('documents.delete', id=document.id) }}" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف نهائياً
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.pdf-viewer {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    overflow: hidden;
}

.pdf-viewer embed {
    border: none;
}

.table-borderless td {
    border: none;
    padding: 0.25rem 0.5rem;
}

.table-borderless td:first-child {
    width: 40%;
    font-weight: 500;
}

.badge {
    font-size: 0.75rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // إظهار رسالة نجاح
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check me-2"></i>
                    تم نسخ الرابط بنجاح
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // إزالة العنصر بعد إخفاؤه
        toast.addEventListener('hidden.bs.toast', function() {
            document.body.removeChild(toast);
        });
    }).catch(function(err) {
        console.error('فشل في نسخ الرابط: ', err);
        alert('فشل في نسخ الرابط');
    });
}
</script>
{% endblock %}
