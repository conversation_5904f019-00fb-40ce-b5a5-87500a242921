# -*- coding: utf-8 -*-
"""
أداة إدارة الملفات والوثائق
"""

import os
import uuid
import shutil
import mimetypes
from datetime import datetime
from werkzeug.utils import secure_filename
from flask import current_app
from PIL import Image
import PyPDF2

class FileManager:
    """مدير الملفات والوثائق"""
    
    # الأنواع المدعومة
    ALLOWED_EXTENSIONS = {
        'pdf', 'doc', 'docx', 'txt', 'rtf', 'odt',
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
        'xls', 'xlsx', 'zip', 'rar', '7z'
    }
    
    # الحد الأقصى لحجم الملف (50 ميجابايت)
    MAX_FILE_SIZE = 50 * 1024 * 1024
    
    @staticmethod
    def allowed_file(filename):
        """التحقق من أن الملف مسموح"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in FileManager.ALLOWED_EXTENSIONS
    
    @staticmethod
    def get_file_extension(filename):
        """الحصول على امتداد الملف"""
        return '.' + filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
    
    @staticmethod
    def generate_unique_filename(original_filename):
        """إنشاء اسم ملف فريد"""
        secure_name = secure_filename(original_filename)
        extension = FileManager.get_file_extension(secure_name)
        unique_name = f"{uuid.uuid4().hex}{extension}"
        return unique_name, secure_name
    
    @staticmethod
    def get_upload_path(subfolder='documents'):
        """الحصول على مسار الرفع"""
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
        full_path = os.path.join(upload_folder, subfolder)
        
        # إنشاء المجلد إذا لم يكن موجوداً
        if not os.path.exists(full_path):
            os.makedirs(full_path)
        
        return full_path
    
    @staticmethod
    def save_file(file, subfolder='documents'):
        """حفظ ملف مرفوع"""
        if not file or not FileManager.allowed_file(file.filename):
            raise ValueError("نوع الملف غير مدعوم")
        
        # التحقق من حجم الملف
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)
        
        if file_size > FileManager.MAX_FILE_SIZE:
            raise ValueError(f"حجم الملف كبير جداً. الحد الأقصى {FileManager.MAX_FILE_SIZE // (1024*1024)} ميجابايت")
        
        # إنشاء اسم ملف فريد
        unique_filename, original_filename = FileManager.generate_unique_filename(file.filename)
        
        # الحصول على مسار الحفظ
        upload_path = FileManager.get_upload_path(subfolder)
        file_path = os.path.join(upload_path, unique_filename)
        
        # حفظ الملف
        file.save(file_path)
        
        # الحصول على معلومات الملف
        file_info = {
            'filename': unique_filename,
            'original_filename': original_filename,
            'file_path': file_path,
            'file_size': file_size,
            'file_type': file.content_type or mimetypes.guess_type(file.filename)[0],
            'file_extension': FileManager.get_file_extension(original_filename)
        }
        
        return file_info
    
    @staticmethod
    def delete_file(file_path):
        """حذف ملف"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
        except Exception as e:
            current_app.logger.error(f"خطأ في حذف الملف {file_path}: {str(e)}")
        return False
    
    @staticmethod
    def move_file(old_path, new_path):
        """نقل ملف"""
        try:
            # إنشاء المجلد الجديد إذا لم يكن موجوداً
            new_dir = os.path.dirname(new_path)
            if not os.path.exists(new_dir):
                os.makedirs(new_dir)
            
            shutil.move(old_path, new_path)
            return True
        except Exception as e:
            current_app.logger.error(f"خطأ في نقل الملف من {old_path} إلى {new_path}: {str(e)}")
        return False
    
    @staticmethod
    def copy_file(source_path, dest_path):
        """نسخ ملف"""
        try:
            # إنشاء المجلد الجديد إذا لم يكن موجوداً
            dest_dir = os.path.dirname(dest_path)
            if not os.path.exists(dest_dir):
                os.makedirs(dest_dir)
            
            shutil.copy2(source_path, dest_path)
            return True
        except Exception as e:
            current_app.logger.error(f"خطأ في نسخ الملف من {source_path} إلى {dest_path}: {str(e)}")
        return False
    
    @staticmethod
    def get_file_info(file_path):
        """الحصول على معلومات ملف"""
        if not os.path.exists(file_path):
            return None
        
        stat = os.stat(file_path)
        filename = os.path.basename(file_path)
        extension = FileManager.get_file_extension(filename)
        
        info = {
            'filename': filename,
            'file_path': file_path,
            'file_size': stat.st_size,
            'file_extension': extension,
            'created_at': datetime.fromtimestamp(stat.st_ctime),
            'modified_at': datetime.fromtimestamp(stat.st_mtime),
            'is_image': extension.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'],
            'is_pdf': extension.lower() == '.pdf',
            'is_document': extension.lower() in ['.doc', '.docx', '.txt', '.rtf', '.odt']
        }
        
        return info
    
    @staticmethod
    def format_file_size(size_bytes):
        """تنسيق حجم الملف"""
        if not size_bytes:
            return "0 B"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    @staticmethod
    def create_thumbnail(image_path, thumbnail_path, size=(200, 200)):
        """إنشاء صورة مصغرة"""
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size, Image.Resampling.LANCZOS)
                
                # إنشاء المجلد إذا لم يكن موجوداً
                thumbnail_dir = os.path.dirname(thumbnail_path)
                if not os.path.exists(thumbnail_dir):
                    os.makedirs(thumbnail_dir)
                
                img.save(thumbnail_path, optimize=True, quality=85)
                return True
        except Exception as e:
            current_app.logger.error(f"خطأ في إنشاء الصورة المصغرة: {str(e)}")
        return False
    
    @staticmethod
    def extract_pdf_text(pdf_path, max_pages=5):
        """استخراج نص من ملف PDF"""
        try:
            text = ""
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                pages_to_read = min(len(pdf_reader.pages), max_pages)
                
                for page_num in range(pages_to_read):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
            
            return text.strip()
        except Exception as e:
            current_app.logger.error(f"خطأ في استخراج نص PDF: {str(e)}")
        return ""
    
    @staticmethod
    def get_file_icon_class(file_extension):
        """الحصول على فئة أيقونة الملف"""
        extension = file_extension.lower()
        
        if extension == '.pdf':
            return 'fas fa-file-pdf text-danger'
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
            return 'fas fa-file-image text-success'
        elif extension in ['.doc', '.docx']:
            return 'fas fa-file-word text-primary'
        elif extension in ['.xls', '.xlsx']:
            return 'fas fa-file-excel text-success'
        elif extension in ['.zip', '.rar', '.7z']:
            return 'fas fa-file-archive text-warning'
        elif extension in ['.txt', '.rtf', '.odt']:
            return 'fas fa-file-alt text-info'
        else:
            return 'fas fa-file text-secondary'
    
    @staticmethod
    def cleanup_orphaned_files(upload_folder, referenced_files):
        """تنظيف الملفات غير المرجعية"""
        try:
            cleaned_count = 0
            
            for root, dirs, files in os.walk(upload_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    # تجاهل الملفات النظام
                    if file.startswith('.'):
                        continue
                    
                    # التحقق من أن الملف مرجع في قاعدة البيانات
                    if file not in referenced_files:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                            current_app.logger.info(f"تم حذف الملف غير المرجع: {file_path}")
                        except Exception as e:
                            current_app.logger.error(f"خطأ في حذف الملف {file_path}: {str(e)}")
            
            return cleaned_count
        except Exception as e:
            current_app.logger.error(f"خطأ في تنظيف الملفات: {str(e)}")
        return 0
    
    @staticmethod
    def get_storage_stats(upload_folder):
        """الحصول على إحصائيات التخزين"""
        try:
            total_size = 0
            file_count = 0
            
            for root, dirs, files in os.walk(upload_folder):
                for file in files:
                    if not file.startswith('.'):
                        file_path = os.path.join(root, file)
                        total_size += os.path.getsize(file_path)
                        file_count += 1
            
            return {
                'total_size': total_size,
                'total_size_formatted': FileManager.format_file_size(total_size),
                'file_count': file_count
            }
        except Exception as e:
            current_app.logger.error(f"خطأ في حساب إحصائيات التخزين: {str(e)}")
        
        return {
            'total_size': 0,
            'total_size_formatted': '0 B',
            'file_count': 0
        }
