<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% if title %}{{ title }} - {% endif %}إدارة مكتب المحامي</title>

    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .sidebar {
            min-height: calc(100vh - 56px);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-left: 10px;
        }

        .main-content {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: 600;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }

        .alert {
            border: none;
            border-radius: 10px;
            font-weight: 500;
        }

        .badge {
            font-size: 0.75em;
            padding: 0.5em 0.75em;
            border-radius: 50px;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }

        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-card .stats-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .pagination .page-link {
            border-radius: 50px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }

        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                right: -250px;
                width: 250px;
                height: calc(100vh - 56px);
                z-index: 1000;
                transition: right 0.3s ease;
            }

            .sidebar.show {
                right: 0;
            }

            .main-content {
                padding: 1rem;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-balance-scale me-2"></i>
                إدارة مكتب المحامي
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.nom_complet }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user-edit me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>

                <!-- Search -->
                <form class="d-flex" method="GET" action="{{ url_for('dashboard.search') }}">
                    <input class="form-control me-2" type="search" name="q" placeholder="البحث..."
                           value="{{ request.args.get('q', '') }}">
                    <button class="btn btn-outline-light" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'dashboard.index' }}"
                               href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'clients' in request.endpoint }}"
                               href="{{ url_for('clients.index') }}">
                                <i class="fas fa-users"></i>
                                الموكلين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'dossiers' in request.endpoint }}"
                               href="{{ url_for('dossiers.index') }}">
                                <i class="fas fa-folder-open"></i>
                                الملفات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'audiences' in request.endpoint }}"
                               href="{{ url_for('audiences.index') }}">
                                <i class="fas fa-calendar-alt"></i>
                                الجلسات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'documents' in request.endpoint }}"
                               href="{{ url_for('documents.index') }}">
                                <i class="fas fa-folder-open"></i>
                                الوثائق
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'extraction' in request.endpoint }}"
                               href="{{ url_for('extraction.index') }}">
                                <i class="fas fa-download"></i>
                                استخراج المحاكم
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'notifications' in request.endpoint }}"
                               href="{{ url_for('notifications.index') }}">
                                <i class="fas fa-bell"></i>
                                التنبيهات
                                {% if unread_notifications_count > 0 %}
                                <span class="badge bg-danger ms-1">{{ unread_notifications_count }}</span>
                                {% endif %}
                            </a>
                        </li>

                        {% if current_user.role == 'admin' %}
                        <li class="nav-item">
                            <hr class="dropdown-divider my-2">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if 'admin' in request.endpoint }}"
                               href="{{ url_for('admin.index') }}">
                                <i class="fas fa-cogs"></i>
                                لوحة تحكم المدير
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'admin.users' }}"
                               href="{{ url_for('admin.users') }}">
                                <i class="fas fa-users-cog"></i>
                                إدارة المستخدمين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ 'active' if request.endpoint == 'admin.system' }}"
                               href="{{ url_for('admin.system') }}">
                                <i class="fas fa-server"></i>
                                إعدادات النظام
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>

    <!-- CSRF Handler -->
    <script src="{{ url_for('static', filename='js/csrf-handler.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
