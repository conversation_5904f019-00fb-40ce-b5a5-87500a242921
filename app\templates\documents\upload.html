{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-upload me-2"></i>
        رفع وثيقة جديدة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('documents.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للوثائق
        </a>
    </div>
</div>

<!-- معلومات الربط -->
{% if dossier or client or audience %}
<div class="alert alert-info mb-4">
    <h6><i class="fas fa-link me-2"></i>ربط الوثيقة:</h6>
    <ul class="mb-0">
        {% if dossier %}
        <li><strong>الملف:</strong> {{ dossier.numero_affaire }} - {{ dossier.objet or 'غير محدد' }}</li>
        {% endif %}
        {% if client %}
        <li><strong>الموكل:</strong> {{ client.nom_complet }}</li>
        {% endif %}
        {% if audience %}
        <li><strong>الجلسة:</strong> {{ audience.date_audience.strftime('%d/%m/%Y') }} - {{ audience.tribunal }}</li>
        {% endif %}
    </ul>
</div>
{% endif %}

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-file-upload me-2"></i>
                    معلومات الوثيقة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    {{ form.hidden_tag() }}
                    
                    <!-- اختيار الملف -->
                    <div class="mb-4">
                        <label for="{{ form.file.id }}" class="form-label required">
                            <i class="fas fa-file me-1"></i>
                            {{ form.file.label.text }}
                        </label>
                        <div class="upload-area" id="uploadArea">
                            {{ form.file(class="form-control", id="fileInput", accept=".pdf,.doc,.docx,.txt,.rtf,.odt,.jpg,.jpeg,.png,.gif,.bmp,.webp,.xls,.xlsx,.zip,.rar,.7z") }}
                            <div class="upload-placeholder" id="uploadPlaceholder">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">اسحب الملف هنا أو انقر لاختيار ملف</p>
                                <small class="text-muted">
                                    الأنواع المدعومة: PDF, Word, صور, Excel, ملفات مضغوطة
                                    <br>
                                    الحد الأقصى: 50 ميجابايت
                                </small>
                            </div>
                            <div class="file-preview" id="filePreview" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file fa-2x text-primary me-3" id="fileIcon"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <small class="text-muted" id="fileSize"></small>
                                    </div>
                                    <button type="button" class="btn btn-outline-danger btn-sm" id="removeFile">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% if form.file.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.file.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- عنوان الوثيقة -->
                    <div class="mb-3">
                        {{ form.title.label(class="form-label required") }}
                        {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else "")) }}
                        {% if form.title.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.title.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- وصف الوثيقة -->
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control") }}
                        {% if form.description.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.description.errors %}
                                    <small>{{ error }}</small><br>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <!-- تصنيف الوثيقة -->
                        <div class="col-md-6 mb-3">
                            {{ form.category_id.label(class="form-label required") }}
                            {{ form.category_id(class="form-select" + (" is-invalid" if form.category_id.errors else "")) }}
                            {% if form.category_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.category_id.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- تاريخ الوثيقة -->
                        <div class="col-md-6 mb-3">
                            {{ form.document_date.label(class="form-label") }}
                            {{ form.document_date(class="form-control") }}
                            {% if form.document_date.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.document_date.errors %}
                                        <small>{{ error }}</small><br>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- خيارات إضافية -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_confidential(class="form-check-input") }}
                                {{ form.is_confidential.label(class="form-check-label") }}
                            </div>
                            <small class="text-muted">الوثائق السرية تتطلب صلاحيات خاصة للعرض</small>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <div class="form-check">
                                {{ form.is_original(class="form-check-input") }}
                                {{ form.is_original.label(class="form-check-label") }}
                            </div>
                            <small class="text-muted">تحديد الوثيقة كنسخة أصلية</small>
                        </div>
                    </div>
                    
                    <!-- أزرار الحفظ -->
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-upload me-1"></i>
                            رفع الوثيقة
                        </button>
                        <a href="{{ url_for('documents.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- نصائح الرفع -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    نصائح مهمة
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> نصائح للرفع:</h6>
                    <ul class="mb-0">
                        <li>استخدم أسماء ملفات واضحة ومفهومة</li>
                        <li>اختر التصنيف المناسب للوثيقة</li>
                        <li>أضف وصفاً مختصراً للوثيقة</li>
                        <li>حدد تاريخ الوثيقة إن أمكن</li>
                        <li>استخدم خيار "سرية" للوثائق الحساسة</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تنبيه:</h6>
                    <p class="mb-0">تأكد من أن الوثيقة لا تحتوي على معلومات شخصية حساسة قبل الرفع.</p>
                </div>
            </div>
        </div>
        
        <!-- أنواع الملفات المدعومة -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    أنواع الملفات المدعومة
                </h6>
            </div>
            <div class="card-body">
                <div class="file-types">
                    <div class="file-type-item">
                        <i class="fas fa-file-pdf text-danger"></i>
                        <span>PDF</span>
                    </div>
                    <div class="file-type-item">
                        <i class="fas fa-file-word text-primary"></i>
                        <span>Word</span>
                    </div>
                    <div class="file-type-item">
                        <i class="fas fa-file-image text-success"></i>
                        <span>صور</span>
                    </div>
                    <div class="file-type-item">
                        <i class="fas fa-file-excel text-success"></i>
                        <span>Excel</span>
                    </div>
                    <div class="file-type-item">
                        <i class="fas fa-file-archive text-warning"></i>
                        <span>مضغوط</span>
                    </div>
                    <div class="file-type-item">
                        <i class="fas fa-file-alt text-secondary"></i>
                        <span>نصوص</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    min-height: 200px;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.file-preview {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background-color: #f8f9fa;
}

.file-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.file-type-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    background-color: #f8f9fa;
}

.file-type-item i {
    font-size: 1.2rem;
}

.required::after {
    content: " *";
    color: #dc3545;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadPlaceholder = document.getElementById('uploadPlaceholder');
    const filePreview = document.getElementById('filePreview');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const fileIcon = document.getElementById('fileIcon');
    const removeFileBtn = document.getElementById('removeFile');
    const titleInput = document.querySelector('input[name="title"]');
    const submitBtn = document.getElementById('submitBtn');
    
    // منع السلوك الافتراضي للسحب والإفلات
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    // إضافة تأثيرات بصرية للسحب والإفلات
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });
    
    // معالجة الإفلات
    uploadArea.addEventListener('drop', handleDrop, false);
    
    // معالجة اختيار الملف
    fileInput.addEventListener('change', handleFileSelect);
    
    // إزالة الملف
    removeFileBtn.addEventListener('click', removeFile);
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    function highlight(e) {
        uploadArea.classList.add('dragover');
    }
    
    function unhighlight(e) {
        uploadArea.classList.remove('dragover');
    }
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect();
        }
    }
    
    function handleFileSelect() {
        const file = fileInput.files[0];
        if (file) {
            showFilePreview(file);
            
            // تعبئة العنوان تلقائياً إذا كان فارغاً
            if (!titleInput.value.trim()) {
                const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");
                titleInput.value = nameWithoutExt;
            }
        }
    }
    
    function showFilePreview(file) {
        // إخفاء منطقة الرفع وإظهار المعاينة
        uploadPlaceholder.style.display = 'none';
        filePreview.style.display = 'block';
        
        // تحديث معلومات الملف
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        
        // تحديث الأيقونة حسب نوع الملف
        const extension = file.name.split('.').pop().toLowerCase();
        updateFileIcon(extension);
    }
    
    function removeFile() {
        fileInput.value = '';
        uploadPlaceholder.style.display = 'block';
        filePreview.style.display = 'none';
    }
    
    function updateFileIcon(extension) {
        let iconClass = 'fas fa-file text-secondary';
        
        if (extension === 'pdf') {
            iconClass = 'fas fa-file-pdf text-danger';
        } else if (['doc', 'docx'].includes(extension)) {
            iconClass = 'fas fa-file-word text-primary';
        } else if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
            iconClass = 'fas fa-file-image text-success';
        } else if (['xls', 'xlsx'].includes(extension)) {
            iconClass = 'fas fa-file-excel text-success';
        } else if (['zip', 'rar', '7z'].includes(extension)) {
            iconClass = 'fas fa-file-archive text-warning';
        } else if (['txt', 'rtf', 'odt'].includes(extension)) {
            iconClass = 'fas fa-file-alt text-info';
        }
        
        fileIcon.className = iconClass + ' fa-2x';
    }
    
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // تعطيل زر الإرسال أثناء الرفع
    document.getElementById('uploadForm').addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الرفع...';
    });
});
</script>
{% endblock %}
