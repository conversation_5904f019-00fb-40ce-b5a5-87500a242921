{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-plus me-2"></i>
        إضافة مستخدم جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة لقائمة المستخدمين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    بيانات المستخدم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="nom_complet" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.nom_complet.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-at me-1"></i>
                                اسم المستخدم <span class="text-danger">*</span>
                            </label>
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">سيستخدم لتسجيل الدخول</small>
                        </div>
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.email.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">اختياري - للإشعارات والتواصل</small>
                    </div>

                    <div class="row">
                        <!-- كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                كلمة المرور <span class="text-danger">*</span>
                            </label>
                            {{ form.password(class="form-control") }}
                            {% if form.password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">6 أحرف على الأقل</small>
                        </div>

                        <!-- تأكيد كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>
                                تأكيد كلمة المرور <span class="text-danger">*</span>
                            </label>
                            {{ form.confirm_password(class="form-control") }}
                            {% if form.confirm_password.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.confirm_password.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الدور -->
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                <i class="fas fa-user-tag me-1"></i>
                                الدور <span class="text-danger">*</span>
                            </label>
                            {{ form.role(class="form-select") }}
                            {% if form.role.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.role.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-toggle-on me-1"></i>
                                الحالة
                            </label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                <label class="form-check-label" for="is_active">
                                    مستخدم نشط
                                </label>
                            </div>
                            <small class="form-text text-muted">يمكن للمستخدم النشط تسجيل الدخول</small>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-1"></i>
                            إنشاء المستخدم
                        </button>
                        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        <button type="button" class="btn btn-info" onclick="generatePassword()">
                            <i class="fas fa-random me-1"></i>
                            توليد كلمة مرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الأدوار -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الأدوار
                </h5>
            </div>
            <div class="card-body">
                <div class="role-info">
                    <div class="role-item">
                        <span class="badge bg-primary">مدير</span>
                        <p>صلاحيات كاملة - إدارة النظام والمستخدمين</p>
                    </div>
                    <div class="role-item">
                        <span class="badge bg-success">محامي</span>
                        <p>إدارة الملفات والجلسات والموكلين</p>
                    </div>
                    <div class="role-item">
                        <span class="badge bg-info">مساعد</span>
                        <p>إضافة وتعديل البيانات الأساسية</p>
                    </div>
                    <div class="role-item">
                        <span class="badge bg-secondary">مشاهد</span>
                        <p>عرض البيانات فقط بدون تعديل</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- إرشادات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>اختر اسم مستخدم فريد وسهل التذكر</li>
                        <li>استخدم كلمة مرور قوية</li>
                        <li>حدد الدور المناسب للمستخدم</li>
                        <li>يمكن تعديل البيانات لاحقاً</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير:</h6>
                    <p class="mb-0">تأكد من إعطاء الصلاحيات المناسبة فقط لكل مستخدم.</p>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-shield-alt me-1"></i> الأمان:</h6>
                    <p class="mb-0">جميع كلمات المرور مشفرة ومحمية بأعلى معايير الأمان.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.role-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.role-item {
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    background-color: #f8f9fa;
}

.role-item .badge {
    margin-bottom: 0.5rem;
}

.role-item p {
    margin: 0;
    font-size: 0.875rem;
    color: #6c757d;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function generatePassword() {
    const length = 8;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    document.getElementById('password').value = password;
    document.getElementById('confirm_password').value = password;
    
    // إظهار كلمة المرور المولدة
    alert(`كلمة المرور المولدة: ${password}\nتأكد من حفظها في مكان آمن`);
}

// التحقق من تطابق كلمات المرور
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// توليد اسم مستخدم من الاسم الكامل
document.getElementById('nom_complet').addEventListener('input', function() {
    const fullName = this.value;
    const usernameField = document.getElementById('username');
    
    if (fullName && !usernameField.value) {
        // إنشاء اسم مستخدم من الاسم الكامل
        let username = fullName.toLowerCase()
            .replace(/\s+/g, '')  // إزالة المسافات
            .replace(/[^a-z0-9]/g, '')  // إزالة الأحرف الخاصة
            .substring(0, 20);  // تحديد الطول
        
        usernameField.value = username;
    }
});

// التحقق من قوة كلمة المرور
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthIndicator = document.getElementById('password-strength');
    
    if (password.length < 6) {
        this.style.borderColor = '#dc3545';
    } else if (password.length < 8) {
        this.style.borderColor = '#ffc107';
    } else {
        this.style.borderColor = '#28a745';
    }
});
</script>
{% endblock %}
