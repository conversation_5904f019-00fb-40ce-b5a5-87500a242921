{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-calendar-edit me-2"></i>
        تعديل الجلسة: {{ audience.numero_dossier }}
        {% if audience.is_important %}
        <span class="badge bg-warning ms-2">مهمة</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('audiences.view', id=audience.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-eye me-1"></i>
                عرض الجلسة
            </a>
        </div>
        <a href="{{ url_for('audiences.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    تعديل بيانات الجلسة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="dossier_id" class="form-label">
                                <i class="fas fa-folder me-1"></i>
                                الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.dossier_id(class="form-select") }}
                            {% if form.dossier_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.dossier_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="date_audience" class="form-label">
                                <i class="fas fa-calendar-day me-1"></i>
                                تاريخ الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.date_audience(class="form-control") }}
                            {% if form.date_audience.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_audience.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- وقت الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="heure_audience" class="form-label">
                                <i class="fas fa-clock me-1"></i>
                                وقت الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.heure_audience(class="form-control") }}
                            {% if form.heure_audience.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.heure_audience.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- المرجع الداخلي -->
                        <div class="col-md-6 mb-3">
                            <label for="reference_interne" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                المرجع الداخلي
                            </label>
                            {{ form.reference_interne(class="form-control") }}
                            {% if form.reference_interne.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.reference_interne.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- حالة الجلسة -->
                        <div class="col-md-6 mb-3">
                            <label for="statut" class="form-label">
                                <i class="fas fa-info-circle me-1"></i>
                                حالة الجلسة <span class="text-danger">*</span>
                            </label>
                            {{ form.statut(class="form-select") }}
                            {% if form.statut.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.statut.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- جلسة مهمة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="form-check">
                                {{ form.is_important(class="form-check-input") }}
                                <label class="form-check-label" for="is_important">
                                    <i class="fas fa-star text-warning me-1"></i>
                                    جلسة مهمة
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- موضوع الجلسة -->
                    <div class="mb-3">
                        <label for="demande" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            موضوع الجلسة / الطلب
                        </label>
                        {{ form.demande(class="form-control") }}
                        {% if form.demande.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.demande.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- نتيجة الجلسة -->
                    <div class="mb-3">
                        <label for="resultat" class="form-label">
                            <i class="fas fa-clipboard-check me-1"></i>
                            نتيجة الجلسة
                        </label>
                        {{ form.resultat(class="form-control") }}
                        {% if form.resultat.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.resultat.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('audiences.view', id=audience.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {% if current_user.has_permission('delete') %}
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-1"></i>
                            حذف الجلسة
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الجلسة الحالية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الجلسة الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-calendar text-primary"></i>
                        <div>
                            <strong>التاريخ الحالي</strong>
                            <p>{{ audience.date_audience.strftime('%d/%m/%Y') if audience.date_audience else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-clock text-info"></i>
                        <div>
                            <strong>الوقت الحالي</strong>
                            <p>{{ audience.heure_audience.strftime('%H:%M') if audience.heure_audience else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-info-circle text-{{ audience.get_status_color() }}"></i>
                        <div>
                            <strong>الحالة الحالية</strong>
                            <p>
                                <span class="badge bg-{{ audience.get_status_color() }}">
                                    {{ audience.statut }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الملف -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    الملف المرتبط
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h6 class="mb-1">
                        <a href="{{ url_for('dossiers.view', id=audience.dossier_id) }}" class="text-decoration-none">
                            {{ audience.numero_dossier }}
                        </a>
                    </h6>
                    <small class="text-muted">{{ audience.type_dossier }}</small>
                </div>

                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-university text-info"></i>
                        <div>
                            <strong>المحكمة</strong>
                            <p>{{ audience.tribunal }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-user text-success"></i>
                        <div>
                            <strong>الموكل</strong>
                            <p>{{ audience.client_nom }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إرشادات التعديل -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات التعديل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة التاريخ والوقت</li>
                        <li>اختر الحالة المناسبة للجلسة</li>
                        <li>أضف النتيجة بعد انعقاد الجلسة</li>
                        <li>يمكن إلغاء التعديلات في أي وقت</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير:</h6>
                    <p class="mb-0">حذف الجلسة سيؤثر على تتبع الملف والتنبيهات المرتبطة.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف جلسة الملف <strong>{{ audience.numero_dossier }}</strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('audiences.delete', id=audience.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// التحقق من صحة الوقت
document.getElementById('heure_audience').addEventListener('change', function() {
    const time = this.value;
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    
    if (time && !timeRegex.test(time)) {
        alert('يرجى إدخال وقت صحيح بالصيغة HH:MM (مثال: 09:30)');
        this.focus();
    }
});
</script>
{% endblock %>
