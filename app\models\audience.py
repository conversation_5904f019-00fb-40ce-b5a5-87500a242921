# -*- coding: utf-8 -*-
"""
نموذج الجلسات
"""

from datetime import datetime, date
from app import db

class Audience(db.Model):
    """نموذج الجلسات"""
    
    __tablename__ = 'audiences'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # ربط بالملف
    dossier_id = db.Column(db.<PERSON>, db.<PERSON>ey('dossiers.id'), nullable=False)
    
    # معلومات الجلسة
    date_audience = db.Column(db.Date, nullable=False, index=True)
    heure_audience = db.Column(db.Time, default=datetime.strptime('09:00', '%H:%M').time())
    
    # معلومات مكررة للسرعة (denormalized)
    reference_interne = db.Column(db.String(50))  # مرجع داخلي
    client_nom = db.Column(db.String(200), nullable=False)
    numero_dossier = db.Column(db.String(50), nullable=False)
    tribunal = db.Column(db.String(20), nullable=False)
    type_dossier = db.Column(db.String(100), nullable=False)
    
    # تفاصيل الجلسة
    demande = db.Column(db.Text)  # الطلب أو الموضوع
    resultat = db.Column(db.Text)  # نتيجة الجلسة
    
    # حالة الجلسة
    statut = db.Column(db.String(20), default='مبرمجة')
    # Options: مبرمجة، منعقدة، مؤجلة، ملغية
    
    # معلومات إضافية
    notes = db.Column(db.Text)
    is_important = db.Column(db.Boolean, default=False)
    
    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<Audience {self.numero_dossier} - {self.date_audience}>'
    
    def is_today(self):
        """هل الجلسة اليوم"""
        return self.date_audience == date.today()
    
    def is_tomorrow(self):
        """هل الجلسة غداً"""
        from datetime import timedelta
        return self.date_audience == date.today() + timedelta(days=1)
    
    def is_upcoming(self):
        """هل الجلسة قادمة"""
        return self.date_audience >= date.today()
    
    def is_past(self):
        """هل الجلسة مضت"""
        return self.date_audience < date.today()
    
    def days_until(self):
        """عدد الأيام المتبقية"""
        if self.is_past():
            return 0
        return (self.date_audience - date.today()).days
    
    def get_status_color(self):
        """لون الحالة"""
        colors = {
            'مبرمجة': 'primary',
            'منعقدة': 'success',
            'مؤجلة': 'warning',
            'ملغية': 'danger'
        }
        return colors.get(self.statut, 'secondary')
    
    def get_urgency_class(self):
        """فئة الاستعجال"""
        if self.is_today():
            return 'table-danger'
        elif self.is_tomorrow():
            return 'table-warning'
        elif self.days_until() <= 3:
            return 'table-info'
        return ''
    
    @staticmethod
    def get_today_audiences():
        """جلسات اليوم"""
        return Audience.query.filter_by(
            date_audience=date.today(),
            statut='مبرمجة'
        ).order_by(Audience.heure_audience.asc()).all()
    
    @staticmethod
    def get_upcoming_audiences(days=7):
        """الجلسات القادمة"""
        from datetime import timedelta
        end_date = date.today() + timedelta(days=days)
        return Audience.query.filter(
            Audience.date_audience.between(date.today(), end_date),
            Audience.statut == 'مبرمجة'
        ).order_by(Audience.date_audience.asc(), Audience.heure_audience.asc()).all()
    
    @staticmethod
    def get_by_tribunal_and_type(tribunal, type_dossier, date_filter=None):
        """الحصول على الجلسات حسب المحكمة والنوع"""
        query = Audience.query.filter_by(
            tribunal=tribunal,
            type_dossier=type_dossier
        )
        
        if date_filter:
            query = query.filter_by(date_audience=date_filter)
        
        return query.order_by(Audience.date_audience.desc()).all()
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'dossier_id': self.dossier_id,
            'date_audience': self.date_audience.isoformat() if self.date_audience else None,
            'heure_audience': self.heure_audience.strftime('%H:%M') if self.heure_audience else None,
            'client_nom': self.client_nom,
            'numero_dossier': self.numero_dossier,
            'tribunal': self.tribunal,
            'type_dossier': self.type_dossier,
            'demande': self.demande,
            'resultat': self.resultat,
            'statut': self.statut,
            'is_important': self.is_important,
            'days_until': self.days_until(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
