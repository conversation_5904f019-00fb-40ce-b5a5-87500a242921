# -*- coding: utf-8 -*-
"""
مسارات استخراج البيانات من المحاكم
"""

from flask import render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app.blueprints.extraction import bp
from app import db
from app.models.extraction import ExtractionLog, ExtractionSettings, CaseProcedure
from app.models.dossier import Dossier
from app.services.extraction_service import ExtractionService

@bp.route('/')
@login_required
def index():
    """صفحة الاستخراج الرئيسية"""

    # إحصائيات الاستخراج
    stats = ExtractionService.get_extraction_statistics()

    # آخر عمليات الاستخراج
    recent_extractions = ExtractionLog.query.order_by(ExtractionLog.created_at.desc()).limit(10).all()

    # الملفات النشطة
    active_dossiers = Dossier.query.filter(
        Dossier.situation.in_(['في الجلسات', 'مداولة', 'في الانتظار'])
    ).limit(20).all()

    return render_template(
        'extraction/index.html',
        stats=stats,
        recent_extractions=recent_extractions,
        active_dossiers=active_dossiers
    )

@bp.route('/extract/<int:dossier_id>', methods=['POST'])
@login_required
def extract_dossier(dossier_id):
    """استخراج بيانات ملف محدد"""

    dossier = Dossier.query.get_or_404(dossier_id)

    # التحقق من وجود رقم الملف
    if not dossier.numero_affaire:
        flash('رقم الملف غير محدد', 'error')
        return redirect(url_for('dossiers.view', id=dossier_id))

    try:
        # تحديد نوع الاستخراج
        force_real = request.form.get('force_real') == 'true'

        # تشغيل الاستخراج
        result = ExtractionService.extract_dossier_data(
            dossier_id=dossier_id,
            user_id=current_user.id,
            force_real=force_real
        )

        if result['success']:
            flash(f'تم استخراج البيانات بنجاح. تم العثور على {len(result["data"]["procedures"])} إجراء', 'success')
        else:
            flash(f'فشل في استخراج البيانات: {result["error"]}', 'error')

    except Exception as e:
        flash(f'حدث خطأ أثناء الاستخراج: {str(e)}', 'error')

    return redirect(url_for('dossiers.view', id=dossier_id, tab='procedures'))

@bp.route('/api/extract/<int:dossier_id>', methods=['POST'])
@login_required
def api_extract_dossier(dossier_id):
    """API لاستخراج بيانات ملف"""

    dossier = Dossier.query.get_or_404(dossier_id)

    if not dossier.numero_affaire:
        return jsonify({
            'success': False,
            'error': 'رقم الملف غير محدد'
        })

    try:
        force_real = request.json.get('force_real', False) if request.is_json else False

        result = ExtractionService.extract_dossier_data(
            dossier_id=dossier_id,
            user_id=current_user.id,
            force_real=force_real
        )

        return jsonify(result)

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@bp.route('/history/<int:dossier_id>')
@login_required
def extraction_history(dossier_id):
    """تاريخ الاستخراج للملف"""

    dossier = Dossier.query.get_or_404(dossier_id)
    extractions = ExtractionService.get_extraction_history(dossier_id)

    return render_template(
        'extraction/history.html',
        dossier=dossier,
        extractions=extractions
    )

@bp.route('/procedures/<int:dossier_id>')
@login_required
def procedures(dossier_id):
    """عرض الإجراءات المستخرجة"""

    dossier = Dossier.query.get_or_404(dossier_id)
    procedures = ExtractionService.get_latest_procedures(dossier_id)

    # إذا كان الطلب من AJAX، إرجاع HTML جزئي
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or 'partial' in request.args:
        return render_template(
            'extraction/procedures_partial.html',
            dossier=dossier,
            procedures=procedures
        )

    # إرجاع الصفحة كاملة
    return render_template(
        'extraction/procedures.html',
        dossier=dossier,
        procedures=procedures
    )

@bp.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """إعدادات الاستخراج"""

    user_settings = ExtractionSettings.get_user_settings(current_user.id)

    if request.method == 'POST':
        try:
            # تحديث الإعدادات
            user_settings.auto_extraction = request.form.get('auto_extraction') == 'on'
            user_settings.extraction_frequency = request.form.get('extraction_frequency', 'weekly')
            user_settings.headless_browser = request.form.get('headless_browser') == 'on'
            user_settings.browser_timeout = int(request.form.get('browser_timeout', 30))
            user_settings.notify_on_success = request.form.get('notify_on_success') == 'on'
            user_settings.notify_on_error = request.form.get('notify_on_error') == 'on'
            user_settings.notify_on_new_procedure = request.form.get('notify_on_new_procedure') == 'on'
            user_settings.simulation_mode = request.form.get('simulation_mode') == 'on'
            user_settings.max_retries = int(request.form.get('max_retries', 3))
            user_settings.retry_delay = int(request.form.get('retry_delay', 60))

            db.session.commit()
            flash('تم حفظ الإعدادات بنجاح', 'success')

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ في حفظ الإعدادات: {str(e)}', 'error')

    return render_template('extraction/settings.html', settings=user_settings)

@bp.route('/logs')
@login_required
def logs():
    """عرض سجلات الاستخراج"""

    page = request.args.get('page', 1, type=int)
    per_page = 20

    # فلترة السجلات
    query = ExtractionLog.query

    # فلترة حسب النجاح/الفشل
    success_filter = request.args.get('success')
    if success_filter == 'true':
        query = query.filter_by(success=True)
    elif success_filter == 'false':
        query = query.filter_by(success=False)

    # فلترة حسب طريقة الاستخراج
    method_filter = request.args.get('method')
    if method_filter:
        query = query.filter_by(extraction_method=method_filter)

    # فلترة حسب التاريخ
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    if date_from:
        try:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(ExtractionLog.created_at >= date_from_obj)
        except ValueError:
            pass

    if date_to:
        try:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(ExtractionLog.created_at <= date_to_obj)
        except ValueError:
            pass

    # ترتيب وتصفح
    logs = query.order_by(ExtractionLog.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('extraction/logs.html', logs=logs)

@bp.route('/log/<int:log_id>')
@login_required
def view_log(log_id):
    """عرض تفاصيل سجل استخراج"""

    log = ExtractionLog.query.get_or_404(log_id)
    procedures = CaseProcedure.query.filter_by(extraction_log_id=log_id)\
                                   .order_by(CaseProcedure.sequence_number).all()

    return render_template(
        'extraction/view_log.html',
        log=log,
        procedures=procedures
    )

@bp.route('/bulk_extract', methods=['POST'])
@login_required
def bulk_extract():
    """استخراج مجمع للملفات"""

    dossier_ids = request.form.getlist('dossier_ids')
    if not dossier_ids:
        flash('لم يتم تحديد أي ملفات', 'warning')
        return redirect(url_for('extraction.index'))

    force_real = request.form.get('force_real') == 'true'
    success_count = 0
    error_count = 0

    for dossier_id in dossier_ids:
        try:
            result = ExtractionService.extract_dossier_data(
                dossier_id=int(dossier_id),
                user_id=current_user.id,
                force_real=force_real
            )

            if result['success']:
                success_count += 1
            else:
                error_count += 1

        except Exception as e:
            error_count += 1

    flash(f'تم استخراج {success_count} ملف بنجاح، فشل في {error_count} ملف',
          'success' if success_count > 0 else 'error')

    return redirect(url_for('extraction.index'))

@bp.route('/test_connection', methods=['POST'])
@login_required
def test_connection():
    """اختبار الاتصال بموقع المحاكم"""

    try:
        from app.services.mahakim_scraper import MahakimScraper

        scraper = MahakimScraper()

        # اختبار بسيط للاتصال
        if scraper.setup_driver(headless=True):
            scraper.driver.get("https://www.mahakim.ma")
            title = scraper.driver.title
            scraper.close_driver()

            return jsonify({
                'success': True,
                'message': f'تم الاتصال بنجاح. عنوان الصفحة: {title}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إعداد المتصفح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاتصال: {str(e)}'
        })
