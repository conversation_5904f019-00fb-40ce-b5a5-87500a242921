# -*- coding: utf-8 -*-
"""
خدمة التنبيهات التلقائية
"""

from datetime import datetime, timedelta
from app import db
from app.models.notification import (
    Notification, NotificationSettings,
    create_audience_notification,
    create_deadline_notification,
    create_response_notification
)
from app.models.audience import Audience
from app.models.dossier import Dossier
from app.models.user import User

class NotificationService:
    """خدمة إدارة التنبيهات"""

    @staticmethod
    def check_and_create_audience_notifications():
        """فحص وإنشاء تنبيهات الجلسات"""

        # الحصول على الجلسات القادمة خلال الأيام القليلة القادمة
        start_date = datetime.now().date()
        end_date = start_date + timedelta(days=3)  # فحص 3 أيام قادمة

        audiences = Audience.query.filter(
            Audience.date_audience >= start_date,
            Audience.date_audience <= end_date,
            Audience.statut != 'منجزة'
        ).all()

        notifications_created = 0

        for audience in audiences:
            # التحقق من عدم وجود تنبيه مسبق لهذه الجلسة
            existing_notification = Notification.query.filter_by(
                type='audience',
                audience_id=audience.id
            ).first()

            if not existing_notification:
                # حساب الوقت المناسب للتنبيه
                hours_before = 24  # افتراضي 24 ساعة

                # إنشاء التنبيه
                create_audience_notification(audience, hours_before)
                notifications_created += 1

        return notifications_created

    @staticmethod
    def check_and_create_deadline_notifications():
        """فحص وإنشاء تنبيهات المواعيد النهائية"""

        notifications_created = 0

        # فحص ملفات الاستئناف (30 يوم من تاريخ الحكم)
        dossiers = Dossier.query.filter(
            Dossier.situation.in_(['في الجلسات', 'مداولة'])
        ).all()

        for dossier in dossiers:
            # البحث عن آخر جلسة للملف
            last_audience = Audience.query.filter_by(
                dossier_id=dossier.id
            ).order_by(Audience.date_audience.desc()).first()

            if last_audience and last_audience.resultat:
                # إذا كان هناك حكم، فحص موعد الاستئناف
                if 'حكم' in last_audience.resultat.lower():
                    appeal_deadline = last_audience.date_audience + timedelta(days=30)

                    # التحقق من عدم انتهاء الموعد
                    if appeal_deadline > datetime.now().date():
                        # التحقق من عدم وجود تنبيه مسبق
                        existing_notification = Notification.query.filter_by(
                            type='deadline',
                            dossier_id=dossier.id
                        ).filter(
                            Notification.message.contains('استئناف')
                        ).first()

                        if not existing_notification:
                            create_deadline_notification(
                                dossier,
                                appeal_deadline,
                                'الاستئناف',
                                48  # 48 ساعة قبل انتهاء الموعد
                            )
                            notifications_created += 1

        return notifications_created

    @staticmethod
    def check_and_create_response_notifications():
        """فحص وإنشاء تنبيهات الردود المطلوبة"""

        notifications_created = 0

        # فحص الملفات التي تحتاج ردود
        dossiers = Dossier.query.filter(
            Dossier.situation.in_(['في الجلسات', 'مداولة'])
        ).all()

        for dossier in dossiers:
            # البحث عن الجلسات التي تحتاج ردود
            audiences = Audience.query.filter_by(
                dossier_id=dossier.id
            ).filter(
                Audience.demande.contains('مذكرة') |
                Audience.demande.contains('رد') |
                Audience.demande.contains('جواب')
            ).all()

            for audience in audiences:
                if audience.date_audience > datetime.now().date():
                    # حساب موعد الرد (عادة قبل الجلسة بأسبوع)
                    response_deadline = audience.date_audience - timedelta(days=7)

                    if response_deadline > datetime.now().date():
                        # التحقق من عدم وجود تنبيه مسبق
                        existing_notification = Notification.query.filter_by(
                            type='response',
                            audience_id=audience.id
                        ).first()

                        if not existing_notification:
                            create_response_notification(
                                dossier,
                                response_deadline,
                                'الرد على المذكرة',
                                24  # 24 ساعة قبل الموعد
                            )
                            notifications_created += 1

        return notifications_created

    @staticmethod
    def send_pending_notifications():
        """إرسال التنبيهات المجدولة"""

        # الحصول على التنبيهات التي حان وقت إرسالها
        pending_notifications = Notification.query.filter(
            Notification.scheduled_time <= datetime.utcnow(),
            Notification.is_sent == False
        ).all()

        sent_count = 0

        for notification in pending_notifications:
            try:
                # تحديد التنبيه كمرسل
                notification.mark_as_sent()

                # إرسال بريد إلكتروني إذا كان مطلوباً
                if notification.send_email and notification.user.email:
                    NotificationService.send_email_notification(notification)

                sent_count += 1

            except Exception as e:
                print(f"خطأ في إرسال التنبيه {notification.id}: {str(e)}")

        return sent_count

    @staticmethod
    def send_email_notification(notification):
        """إرسال تنبيه عبر البريد الإلكتروني"""

        try:
            # هنا يمكن إضافة منطق إرسال البريد الإلكتروني
            # باستخدام Flask-Mail أو خدمة أخرى

            # مثال بسيط (يحتاج إعداد Flask-Mail):
            """
            from flask_mail import Message
            from app import mail

            msg = Message(
                subject=notification.title,
                recipients=[notification.user.email],
                body=notification.message
            )

            mail.send(msg)
            """

            # تحديد البريد كمرسل
            notification.email_sent = True
            db.session.commit()

            return True

        except Exception as e:
            print(f"خطأ في إرسال البريد الإلكتروني: {str(e)}")
            return False

    @staticmethod
    def create_system_notification(title, message, priority='medium', user_ids=None):
        """إنشاء تنبيه نظام لمستخدمين محددين"""

        if user_ids is None:
            # إرسال لجميع المستخدمين النشطين
            users = User.query.filter_by(is_active=True).all()
            user_ids = [user.id for user in users]

        notifications_created = 0

        for user_id in user_ids:
            notification = Notification(
                type='general',
                title=title,
                message=message,
                priority=priority,
                scheduled_time=datetime.utcnow(),
                user_id=user_id
            )

            db.session.add(notification)
            notifications_created += 1

        db.session.commit()
        return notifications_created

    @staticmethod
    def cleanup_old_notifications(days_old=30):
        """تنظيف التنبيهات القديمة"""

        cutoff_date = datetime.utcnow() - timedelta(days=days_old)

        old_notifications = Notification.query.filter(
            Notification.created_at < cutoff_date,
            Notification.is_read == True
        ).all()

        deleted_count = len(old_notifications)

        for notification in old_notifications:
            db.session.delete(notification)

        db.session.commit()
        return deleted_count

    @staticmethod
    def run_daily_check():
        """تشغيل الفحص اليومي للتنبيهات"""

        results = {
            'audience_notifications': NotificationService.check_and_create_audience_notifications(),
            'deadline_notifications': NotificationService.check_and_create_deadline_notifications(),
            'response_notifications': NotificationService.check_and_create_response_notifications(),
            'sent_notifications': NotificationService.send_pending_notifications(),
            'cleaned_notifications': NotificationService.cleanup_old_notifications()
        }

        return results

# دالة لتشغيل الخدمة من سطر الأوامر
def run_notification_service():
    """تشغيل خدمة التنبيهات"""

    from app import create_app

    app = create_app()
    with app.app_context():
        results = NotificationService.run_daily_check()

        print("نتائج خدمة التنبيهات:")
        print(f"- تنبيهات الجلسات: {results['audience_notifications']}")
        print(f"- تنبيهات المواعيد: {results['deadline_notifications']}")
        print(f"- تنبيهات الردود: {results['response_notifications']}")
        print(f"- التنبيهات المرسلة: {results['sent_notifications']}")
        print(f"- التنبيهات المحذوفة: {results['cleaned_notifications']}")

        return results

if __name__ == '__main__':
    run_notification_service()
