# -*- coding: utf-8 -*-
"""
مسارات وحدة الإدارة
"""

from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import func

from app import db
from app.models.user import User
from app.models.client import Client
from app.models.dossier import Dossier
from app.models.audience import Audience
from app.models.journal_actions import JournalActions
from app.models.type_dossier import TypeDossier
from app.forms.user import UserForm, UserSearchForm
from app.blueprints.admin import bp

def admin_required(f):
    """ديكوريتر للتأكد من صلاحيات المدير"""
    from functools import wraps
    
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or current_user.role != 'admin':
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard.index'))
        return f(*args, **kwargs)
    return decorated_function

@bp.route('/')
@login_required
@admin_required
def index():
    """لوحة تحكم المدير"""
    
    # إحصائيات عامة
    stats = {
        'total_users': User.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'total_clients': Client.query.count(),
        'total_dossiers': Dossier.query.count(),
        'active_dossiers': Dossier.query.filter_by(is_archived=False).count(),
        'total_audiences': Audience.query.count(),
        'today_audiences': len(Audience.get_today_audiences()),
        'total_actions': JournalActions.query.count()
    }
    
    # إحصائيات المستخدمين حسب الدور
    user_roles = db.session.query(
        User.role,
        func.count(User.id).label('count')
    ).group_by(User.role).all()
    
    # آخر المستخدمين المسجلين
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    
    # إحصائيات النشاط الشهري
    current_month = date.today().replace(day=1)
    monthly_stats = {
        'new_clients': Client.query.filter(Client.created_at >= current_month).count(),
        'new_dossiers': Dossier.query.filter(Dossier.created_at >= current_month).count(),
        'monthly_audiences': Audience.query.filter(Audience.date_audience >= current_month).count()
    }
    
    return render_template('admin/index.html',
                         title='لوحة تحكم المدير',
                         stats=stats,
                         user_roles=user_roles,
                         recent_users=recent_users,
                         monthly_stats=monthly_stats)

@bp.route('/users')
@login_required
@admin_required
def users():
    """إدارة المستخدمين"""
    
    # معايير البحث والتصفية
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    page = request.args.get('page', 1, type=int)
    
    # بناء الاستعلام
    query = User.query
    
    if search:
        query = query.filter(
            User.nom_complet.contains(search) |
            User.username.contains(search) |
            User.email.contains(search)
        )
    
    if role_filter:
        query = query.filter(User.role == role_filter)
    
    if status_filter:
        is_active = status_filter == 'active'
        query = query.filter(User.is_active == is_active)
    
    # ترتيب وتقسيم الصفحات
    users = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    # قوائم للتصفية
    roles = ['admin', 'lawyer', 'assistant', 'viewer']
    statuses = ['active', 'inactive']
    
    return render_template('admin/users.html',
                         title='إدارة المستخدمين',
                         users=users,
                         search=search,
                         role_filter=role_filter,
                         status_filter=status_filter,
                         roles=roles,
                         statuses=statuses)

@bp.route('/users/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_user():
    """إضافة مستخدم جديد"""
    
    form = UserForm()
    
    if form.validate_on_submit():
        # التحقق من عدم وجود اسم مستخدم مكرر
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('admin/create_user.html', form=form)
        
        # التحقق من عدم وجود بريد إلكتروني مكرر
        if form.email.data:
            existing_email = User.query.filter_by(email=form.email.data).first()
            if existing_email:
                flash('البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('admin/create_user.html', form=form)
        
        # إنشاء المستخدم الجديد
        user = User(
            username=form.username.data,
            email=form.email.data,
            nom_complet=form.nom_complet.data,
            role=form.role.data,
            is_active=form.is_active.data
        )
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash(f'تم إنشاء المستخدم {user.nom_complet} بنجاح', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/create_user.html',
                         title='إضافة مستخدم جديد',
                         form=form)

@bp.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_user(id):
    """تعديل مستخدم"""
    
    user = User.query.get_or_404(id)
    form = UserForm(obj=user)
    
    # إزالة حقل كلمة المرور من التعديل
    del form.password
    del form.confirm_password
    
    if form.validate_on_submit():
        # التحقق من عدم وجود اسم مستخدم مكرر
        existing_user = User.query.filter(
            User.username == form.username.data,
            User.id != user.id
        ).first()
        if existing_user:
            flash('اسم المستخدم موجود بالفعل', 'error')
            return render_template('admin/edit_user.html', form=form, user=user)
        
        # التحقق من عدم وجود بريد إلكتروني مكرر
        if form.email.data:
            existing_email = User.query.filter(
                User.email == form.email.data,
                User.id != user.id
            ).first()
            if existing_email:
                flash('البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('admin/edit_user.html', form=form, user=user)
        
        # تحديث البيانات
        form.populate_obj(user)
        db.session.commit()
        
        flash(f'تم تحديث بيانات المستخدم {user.nom_complet} بنجاح', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/edit_user.html',
                         title=f'تعديل المستخدم: {user.nom_complet}',
                         form=form,
                         user=user)

@bp.route('/users/<int:id>/reset_password', methods=['POST'])
@login_required
@admin_required
def reset_password(id):
    """إعادة تعيين كلمة مرور المستخدم"""
    
    user = User.query.get_or_404(id)
    new_password = request.form.get('new_password')
    
    if not new_password or len(new_password) < 6:
        flash('كلمة المرور يجب أن تكون 6 أحرف على الأقل', 'error')
        return redirect(url_for('admin.edit_user', id=id))
    
    user.set_password(new_password)
    db.session.commit()
    
    flash(f'تم إعادة تعيين كلمة مرور المستخدم {user.nom_complet}', 'success')
    return redirect(url_for('admin.users'))

@bp.route('/users/<int:id>/toggle_status', methods=['POST'])
@login_required
@admin_required
def toggle_user_status(id):
    """تفعيل/إلغاء تفعيل المستخدم"""
    
    user = User.query.get_or_404(id)
    
    # منع إلغاء تفعيل المدير الحالي
    if user.id == current_user.id:
        flash('لا يمكنك إلغاء تفعيل حسابك الخاص', 'error')
        return redirect(url_for('admin.users'))
    
    user.is_active = not user.is_active
    db.session.commit()
    
    status = 'تم تفعيل' if user.is_active else 'تم إلغاء تفعيل'
    flash(f'{status} المستخدم {user.nom_complet}', 'success')
    
    return redirect(url_for('admin.users'))

@bp.route('/system')
@login_required
@admin_required
def system():
    """إعدادات النظام"""
    
    # معلومات النظام
    system_info = {
        'database_size': get_database_size(),
        'total_records': get_total_records(),
        'last_backup': get_last_backup_date(),
        'system_version': '1.0.0',
        'python_version': get_python_version()
    }
    
    # إحصائيات الاستخدام
    usage_stats = {
        'daily_logins': get_daily_logins(),
        'weekly_activity': get_weekly_activity(),
        'popular_features': get_popular_features()
    }
    
    return render_template('admin/system.html',
                         title='إعدادات النظام',
                         system_info=system_info,
                         usage_stats=usage_stats)

@bp.route('/backup', methods=['POST'])
@login_required
@admin_required
def backup():
    """إنشاء نسخة احتياطية"""
    
    try:
        # إنشاء نسخة احتياطية من قاعدة البيانات
        backup_file = create_database_backup()
        
        flash(f'تم إنشاء النسخة الاحتياطية: {backup_file}', 'success')
    except Exception as e:
        flash(f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}', 'error')
    
    return redirect(url_for('admin.system'))

@bp.route('/logs')
@login_required
@admin_required
def logs():
    """عرض سجلات النظام"""
    
    # قراءة آخر 100 سطر من سجل النظام
    log_entries = get_recent_logs(100)
    
    return render_template('admin/logs.html',
                         title='سجلات النظام',
                         log_entries=log_entries)

# دوال مساعدة
def get_database_size():
    """حساب حجم قاعدة البيانات"""
    import os
    try:
        db_path = 'instance/mohami.db'
        if os.path.exists(db_path):
            size = os.path.getsize(db_path)
            return f"{size / 1024 / 1024:.2f} MB"
        return "غير محدد"
    except:
        return "غير محدد"

def get_total_records():
    """حساب إجمالي السجلات"""
    try:
        total = (
            User.query.count() +
            Client.query.count() +
            Dossier.query.count() +
            Audience.query.count() +
            JournalActions.query.count()
        )
        return total
    except:
        return 0

def get_last_backup_date():
    """تاريخ آخر نسخة احتياطية"""
    # سيتم تنفيذه لاحقاً
    return "لم يتم إنشاء نسخة احتياطية بعد"

def get_python_version():
    """إصدار Python"""
    import sys
    return f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

def get_daily_logins():
    """عدد تسجيلات الدخول اليومية"""
    # سيتم تنفيذه مع نظام السجلات
    return 0

def get_weekly_activity():
    """النشاط الأسبوعي"""
    # سيتم تنفيذه مع نظام السجلات
    return []

def get_popular_features():
    """المميزات الأكثر استخداماً"""
    # سيتم تنفيذه مع نظام السجلات
    return []

def create_database_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    import shutil
    import os
    from datetime import datetime
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_dir = 'backups'
    os.makedirs(backup_dir, exist_ok=True)
    
    # اسم ملف النسخة الاحتياطية
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{backup_dir}/mohami_backup_{timestamp}.db"
    
    # نسخ قاعدة البيانات
    shutil.copy2('instance/mohami.db', backup_file)
    
    return backup_file

def get_recent_logs(limit=100):
    """قراءة آخر سجلات النظام"""
    # سيتم تنفيذه مع نظام السجلات المتقدم
    return [
        {
            'timestamp': datetime.now(),
            'level': 'INFO',
            'message': 'تم تشغيل النظام بنجاح',
            'user': 'النظام'
        }
    ]
