# -*- coding: utf-8 -*-
"""
نماذج استخراج البيانات من المحاكم
"""

import json
from datetime import datetime
from app import db

class ExtractionLog(db.Model):
    """سجل عمليات الاستخراج"""

    __tablename__ = 'extraction_logs'

    id = db.Column(db.Integer, primary_key=True)

    # معلومات الاستخراج
    dossier_id = db.Column(db.Integer, db.ForeignKey('dossiers.id'), nullable=False)
    numero_dossier = db.Column(db.String(100), nullable=False)
    tribunal = db.Column(db.String(200), nullable=False)

    # نتيجة الاستخراج
    success = db.Column(db.<PERSON>an, default=False, nullable=False)
    error_message = db.Column(db.Text)

    # البيانات المستخرجة
    extracted_data = db.Column(db.Text)  # JSON
    procedures_count = db.Column(db.Integer, default=0)

    # معلومات إضافية
    extraction_method = db.Column(db.String(50), default='selenium')  # selenium, simulation, manual
    extraction_duration = db.Column(db.Float)  # بالثواني

    # معلومات المستخدم
    extracted_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f'<ExtractionLog {self.numero_dossier}>'

    @property
    def data_dict(self):
        """الحصول على البيانات المستخرجة كقاموس"""
        if self.extracted_data:
            try:
                return json.loads(self.extracted_data)
            except json.JSONDecodeError:
                return {}
        return {}

    def set_data(self, data):
        """تعيين البيانات المستخرجة"""
        self.extracted_data = json.dumps(data, ensure_ascii=False, indent=2)

        # حساب عدد الإجراءات
        if isinstance(data, dict) and 'procedures' in data:
            self.procedures_count = len(data['procedures'])

    def get_next_audience(self):
        """الحصول على الجلسة القادمة"""
        data = self.data_dict
        return data.get('next_audience')

    def get_last_decision(self):
        """الحصول على آخر قرار"""
        data = self.data_dict
        return data.get('last_decision')

    def get_case_status(self):
        """الحصول على حالة القضية"""
        data = self.data_dict
        return data.get('case_status', 'غير محدد')

    def get_procedures(self):
        """الحصول على قائمة الإجراءات"""
        data = self.data_dict
        return data.get('procedures', [])

class CaseProcedure(db.Model):
    """إجراءات القضية المستخرجة"""

    __tablename__ = 'case_procedures'

    id = db.Column(db.Integer, primary_key=True)

    # ربط بالملف وسجل الاستخراج
    dossier_id = db.Column(db.Integer, db.ForeignKey('dossiers.id'), nullable=False)
    extraction_log_id = db.Column(db.Integer, db.ForeignKey('extraction_logs.id'), nullable=False)

    # معلومات الإجراء
    procedure_date = db.Column(db.Date)
    procedure_type = db.Column(db.String(200), nullable=False)
    procedure_details = db.Column(db.Text)
    procedure_status = db.Column(db.String(100))

    # معلومات إضافية
    is_audience = db.Column(db.Boolean, default=False)  # هل هو جلسة
    is_decision = db.Column(db.Boolean, default=False)  # هل هو قرار
    is_future = db.Column(db.Boolean, default=False)    # هل في المستقبل

    # ترتيب الإجراء
    sequence_number = db.Column(db.Integer, default=0)

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<CaseProcedure {self.procedure_type}>'

    @property
    def formatted_date(self):
        """التاريخ منسق"""
        if self.procedure_date:
            return self.procedure_date.strftime('%d/%m/%Y')
        return 'غير محدد'

    @property
    def status_color(self):
        """لون الحالة"""
        status_colors = {
            'مكتمل': 'success',
            'مجدولة': 'primary',
            'ملغية': 'danger',
            'مؤجلة': 'warning',
            'في الانتظار': 'info'
        }
        return status_colors.get(self.procedure_status, 'secondary')

    @property
    def type_icon(self):
        """أيقونة نوع الإجراء"""
        if self.is_audience:
            return 'fas fa-calendar-alt'
        elif self.is_decision:
            return 'fas fa-gavel'
        elif 'تبليغ' in self.procedure_type:
            return 'fas fa-bell'
        elif 'إيداع' in self.procedure_type:
            return 'fas fa-file-upload'
        else:
            return 'fas fa-clipboard-list'

class ExtractionSettings(db.Model):
    """إعدادات الاستخراج"""

    __tablename__ = 'extraction_settings'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)

    # إعدادات عامة
    auto_extraction = db.Column(db.Boolean, default=False)  # استخراج تلقائي
    extraction_frequency = db.Column(db.String(20), default='weekly')  # daily, weekly, monthly

    # إعدادات المتصفح
    headless_browser = db.Column(db.Boolean, default=True)
    browser_timeout = db.Column(db.Integer, default=30)

    # إعدادات التنبيهات
    notify_on_success = db.Column(db.Boolean, default=True)
    notify_on_error = db.Column(db.Boolean, default=True)
    notify_on_new_procedure = db.Column(db.Boolean, default=True)

    # إعدادات متقدمة
    simulation_mode = db.Column(db.Boolean, default=False)  # وضع المحاكاة للاختبار
    max_retries = db.Column(db.Integer, default=3)
    retry_delay = db.Column(db.Integer, default=60)  # بالثواني

    # تواريخ النظام
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<ExtractionSettings User:{self.user_id}>'

    @staticmethod
    def get_user_settings(user_id):
        """الحصول على إعدادات المستخدم"""
        settings = ExtractionSettings.query.filter_by(user_id=user_id).first()
        if not settings:
            # إنشاء إعدادات افتراضية
            settings = ExtractionSettings(user_id=user_id)
            db.session.add(settings)
            db.session.commit()
        return settings

# العلاقات ستتم إضافتها في ملف منفصل
