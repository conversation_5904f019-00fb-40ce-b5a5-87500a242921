{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user me-2"></i>
        {{ client.nom_complet }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('update') %}
            <a href="{{ url_for('clients.edit', id=client.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i>
                تعديل
            </a>
            {% endif %}
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('dossiers.create') }}?client_id={{ client.id }}" class="btn btn-success">
                <i class="fas fa-folder-plus me-1"></i>
                إضافة ملف جديد
            </a>
            {% endif %}
        </div>
        <a href="{{ url_for('clients.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <!-- معلومات الموكل -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات شخصية
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-4">
                    <div class="avatar-large mx-auto mb-3">
                        {{ client.nom_complet[0] }}
                    </div>
                    <h5 class="mb-1">{{ client.nom_complet }}</h5>
                    {% if client.profession %}
                    <p class="text-muted">{{ client.profession }}</p>
                    {% endif %}
                    {% if client.type_dossier %}
                    <span class="badge" style="background-color: {{ client.type_dossier.couleur }}">
                        {{ client.type_dossier.nom }}
                    </span>
                    {% endif %}
                </div>

                <div class="info-list">
                    {% if client.email %}
                    <div class="info-item">
                        <i class="fas fa-envelope text-primary"></i>
                        <div>
                            <strong>البريد الإلكتروني</strong>
                            <p>{{ client.email }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.telephone %}
                    <div class="info-item">
                        <i class="fas fa-phone text-success"></i>
                        <div>
                            <strong>رقم الهاتف</strong>
                            <p>{{ client.telephone }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.telephone_2 %}
                    <div class="info-item">
                        <i class="fas fa-phone text-success"></i>
                        <div>
                            <strong>رقم الهاتف الثاني</strong>
                            <p>{{ client.telephone_2 }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.cin %}
                    <div class="info-item">
                        <i class="fas fa-id-card text-warning"></i>
                        <div>
                            <strong>رقم البطاقة الوطنية</strong>
                            <p>{{ client.cin }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.date_naissance %}
                    <div class="info-item">
                        <i class="fas fa-calendar text-info"></i>
                        <div>
                            <strong>تاريخ الميلاد</strong>
                            <p>{{ client.date_naissance.strftime('%d/%m/%Y') }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.lieu_naissance %}
                    <div class="info-item">
                        <i class="fas fa-map-marker-alt text-danger"></i>
                        <div>
                            <strong>مكان الميلاد</strong>
                            <p>{{ client.lieu_naissance }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.situation_familiale %}
                    <div class="info-item">
                        <i class="fas fa-heart text-pink"></i>
                        <div>
                            <strong>الحالة العائلية</strong>
                            <p>{{ client.situation_familiale }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if client.adresse %}
                    <div class="info-item">
                        <i class="fas fa-home text-secondary"></i>
                        <div>
                            <strong>العنوان</strong>
                            <p>{{ client.adresse }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                {% if client.notes %}
                <div class="mt-4">
                    <h6><i class="fas fa-sticky-note me-1"></i> ملاحظات</h6>
                    <div class="alert alert-light">
                        {{ client.notes }}
                    </div>
                </div>
                {% endif %}

                <div class="mt-4 text-muted small">
                    <i class="fas fa-clock me-1"></i>
                    تم الإنشاء: {{ client.created_at.strftime('%d/%m/%Y %H:%M') if client.created_at else '' }}
                </div>
            </div>
        </div>
    </div>

    <!-- الملفات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    الملفات ({{ dossiers|length }})
                </h5>
                {% if current_user.has_permission('create') %}
                <a href="{{ url_for('dossiers.create') }}?client_id={{ client.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة ملف
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if dossiers %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الملف</th>
                                    <th>المحكمة</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الفتح</th>
                                    <th>الجلسات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dossier in dossiers %}
                                <tr>
                                    <td>
                                        <strong>{{ dossier.numero_affaire }}</strong>
                                        {% if dossier.is_urgent %}
                                        <span class="badge bg-danger ms-1">مستعجل</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ dossier.tribunal }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ dossier.get_status_color() }}">
                                            {{ dossier.situation }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ dossier.date_ouverture.strftime('%d/%m/%Y') if dossier.date_ouverture else '' }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ dossier.get_audiences_count() }}</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('dossiers.view', id=dossier.id) }}" 
                                               class="btn btn-outline-primary" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if current_user.has_permission('update') %}
                                            <a href="{{ url_for('dossiers.edit', id=dossier.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد ملفات لهذا الموكل</h6>
                        <p class="text-muted">ابدأ بإضافة ملف جديد</p>
                        {% if current_user.has_permission('create') %}
                        <a href="{{ url_for('dossiers.create') }}?client_id={{ client.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            إضافة ملف جديد
                        </a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="stats-card bg-primary">
                    <div class="stats-number">{{ client.get_dossiers_count() }}</div>
                    <div class="stats-label">إجمالي الملفات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card bg-success">
                    <div class="stats-number">{{ client.get_active_dossiers_count() }}</div>
                    <div class="stats-label">الملفات النشطة</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card bg-warning">
                    <div class="stats-number">
                        {% set latest = client.get_latest_dossier() %}
                        {% if latest %}
                            {{ latest.get_audiences_count() }}
                        {% else %}
                            0
                        {% endif %}
                    </div>
                    <div class="stats-label">جلسات آخر ملف</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card bg-info">
                    <div class="stats-number">
                        {% if client.created_at %}
                            {{ ((moment().utc() - client.created_at).days if moment else 0) }}
                        {% else %}
                            0
                        {% endif %}
                    </div>
                    <div class="stats-label">أيام منذ التسجيل</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
}

.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}

.text-pink {
    color: #ec4899 !important;
}
</style>
{% endblock %}
