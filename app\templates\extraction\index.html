{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-download me-2"></i>
        استخراج البيانات من المحاكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-success" onclick="testConnection()">
                <i class="fas fa-wifi me-1"></i>
                اختبار الاتصال
            </button>
            <a href="{{ url_for('extraction.settings') }}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-1"></i>
                الإعدادات
            </a>
            <a href="{{ url_for('extraction.logs') }}" class="btn btn-outline-secondary">
                <i class="fas fa-history me-1"></i>
                السجلات
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات الاستخراج -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card bg-primary">
            <div class="stats-number">{{ stats.total_extractions }}</div>
            <div class="stats-label">إجمالي العمليات</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-success">
            <div class="stats-number">{{ stats.successful_extractions }}</div>
            <div class="stats-label">نجحت</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-danger">
            <div class="stats-number">{{ stats.failed_extractions }}</div>
            <div class="stats-label">فشلت</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card bg-info">
            <div class="stats-number">{{ "%.1f"|format(stats.success_rate) }}%</div>
            <div class="stats-label">معدل النجاح</div>
        </div>
    </div>
</div>

<!-- تنبيه حول الاستخراج -->
<div class="alert alert-info mb-4">
    <h6><i class="fas fa-info-circle me-2"></i>حول استخراج البيانات:</h6>
    <ul class="mb-0">
        <li><strong>الاستخراج التلقائي:</strong> يتم استخراج البيانات من موقع المحاكم تلقائياً</li>
        <li><strong>وضع المحاكاة:</strong> لأغراض الاختبار والتطوير (بيانات تجريبية)</li>
        <li><strong>الاستخراج الحقيقي:</strong> يتطلب اتصال بالإنترنت ومتصفح Chrome</li>
        <li><strong>البيانات المستخرجة:</strong> الإجراءات، الجلسات القادمة، القرارات الأخيرة</li>
    </ul>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- الملفات النشطة -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-folder-open me-2"></i>
                    الملفات النشطة
                </h5>
                <button type="button" class="btn btn-primary btn-sm" onclick="showBulkExtractModal()">
                    <i class="fas fa-download me-1"></i>
                    استخراج مجمع
                </button>
            </div>
            <div class="card-body">
                {% if active_dossiers %}
                <form id="bulkExtractForm">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>رقم الملف</th>
                                    <th>الموكل</th>
                                    <th>المحكمة</th>
                                    <th>الحالة</th>
                                    <th>آخر استخراج</th>
                                    <th width="120">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dossier in active_dossiers %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input dossier-checkbox"
                                               value="{{ dossier.id }}">
                                    </td>
                                    <td>
                                        <a href="{{ url_for('dossiers.view', id=dossier.id) }}"
                                           class="text-decoration-none">
                                            {{ dossier.numero_affaire }}
                                        </a>
                                    </td>
                                    <td>{{ dossier.client.nom_complet if dossier.client else 'غير محدد' }}</td>
                                    <td>{{ dossier.tribunal or 'غير محدد' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if dossier.situation == 'في الجلسات' else 'warning' }}">
                                            {{ dossier.situation }}
                                        </span>
                                    </td>
                                    <td>
                                        {% set last_extraction = dossier.get_latest_extraction() %}
                                        {% if last_extraction %}
                                            <small class="text-{{ 'success' if last_extraction.success else 'danger' }}">
                                                {{ last_extraction.created_at.strftime('%d/%m/%Y') }}
                                                <i class="fas fa-{{ 'check' if last_extraction.success else 'times' }} ms-1"></i>
                                            </small>
                                        {% else %}
                                            <small class="text-muted">لم يتم</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary"
                                                    onclick="extractSingle({{ dossier.id }})" title="استخراج">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <a href="{{ url_for('extraction.procedures', dossier_id=dossier.id) }}"
                                               class="btn btn-outline-info" title="الإجراءات">
                                                <i class="fas fa-list"></i>
                                            </a>
                                            <a href="{{ url_for('extraction.extraction_history', dossier_id=dossier.id) }}"
                                               class="btn btn-outline-secondary" title="التاريخ">
                                                <i class="fas fa-history"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد ملفات نشطة</h5>
                    <p class="text-muted">أضف ملفات جديدة لبدء استخراج البيانات</p>
                    <a href="{{ url_for('dossiers.add') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة ملف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- آخر عمليات الاستخراج -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    آخر العمليات
                </h6>
            </div>
            <div class="card-body">
                {% if recent_extractions %}
                <div class="timeline">
                    {% for extraction in recent_extractions %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-{{ 'success' if extraction.success else 'danger' }}">
                            <i class="fas fa-{{ 'check' if extraction.success else 'times' }}"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">
                                <a href="{{ url_for('dossiers.view', id=extraction.dossier_id) }}">
                                    {{ extraction.numero_dossier }}
                                </a>
                            </h6>
                            <p class="timeline-text">
                                {% if extraction.success %}
                                    تم استخراج {{ extraction.procedures_count }} إجراء
                                {% else %}
                                    فشل: {{ extraction.error_message[:50] }}...
                                {% endif %}
                            </p>
                            <small class="timeline-time">
                                {{ extraction.created_at.strftime('%d/%m/%Y %H:%M') }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-history fa-2x text-muted mb-2"></i>
                    <p class="text-muted">لا توجد عمليات استخراج</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- مودال الاستخراج المجمع -->
<div class="modal fade" id="bulkExtractModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استخراج مجمع للملفات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد استخراج البيانات للملفات المحددة؟</p>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="forceRealExtraction">
                    <label class="form-check-label" for="forceRealExtraction">
                        استخراج حقيقي (تجاهل وضع المحاكاة)
                    </label>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <strong>ملاحظة:</strong> قد تستغرق العملية عدة دقائق حسب عدد الملفات المحددة.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkExtract()">
                    <i class="fas fa-download me-1"></i>
                    بدء الاستخراج
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.stats-card {
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    color: white;
    text-align: center;
}

.stats-card .stats-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card .stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 3px solid #dee2e6;
}

.timeline-title {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.timeline-text {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.timeline-time {
    color: #6c757d;
    font-size: 0.75rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// تحديد/إلغاء تحديد الكل
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.dossier-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// استخراج ملف واحد
function extractSingle(dossierId) {
    if (confirm('هل تريد استخراج البيانات لهذا الملف؟')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/extraction/extract/${dossierId}`;

        const csrfToken = document.querySelector('meta[name=csrf-token]');
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken.getAttribute('content');
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
}

// إظهار مودال الاستخراج المجمع
function showBulkExtractModal() {
    const selectedCheckboxes = document.querySelectorAll('.dossier-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        alert('يرجى تحديد ملف واحد على الأقل');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('bulkExtractModal'));
    modal.show();
}

// تنفيذ الاستخراج المجمع
function executeBulkExtract() {
    const selectedCheckboxes = document.querySelectorAll('.dossier-checkbox:checked');
    const forceReal = document.getElementById('forceRealExtraction').checked;

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/extraction/bulk_extract';

    // إضافة الملفات المحددة
    selectedCheckboxes.forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'dossier_ids';
        input.value = checkbox.value;
        form.appendChild(input);
    });

    // إضافة خيار الاستخراج الحقيقي
    if (forceReal) {
        const forceInput = document.createElement('input');
        forceInput.type = 'hidden';
        forceInput.name = 'force_real';
        forceInput.value = 'true';
        form.appendChild(forceInput);
    }

    // إضافة CSRF token
    const csrfToken = document.querySelector('meta[name=csrf-token]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    document.body.appendChild(form);
    form.submit();

    // إغلاق المودال
    const modal = bootstrap.Modal.getInstance(document.getElementById('bulkExtractModal'));
    modal.hide();
}

// اختبار الاتصال
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاختبار...';
    btn.disabled = true;

    fetch('/extraction/test_connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ ' + data.message);
        } else {
            alert('❌ ' + data.message);
        }
    })
    .catch(error => {
        alert('❌ خطأ في الاتصال: ' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
{% endblock %}
