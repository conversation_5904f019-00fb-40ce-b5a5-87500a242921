{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-users me-2"></i>
        الموكلين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if current_user.has_permission('create') %}
            <a href="{{ url_for('clients.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة موكل جديد
            </a>
            {% endif %}
            <a href="{{ url_for('clients.export') }}" class="btn btn-outline-secondary">
                <i class="fas fa-download me-1"></i>
                تصدير
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search }}" placeholder="ابحث بالاسم، الهاتف، البريد أو رقم البطاقة">
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">نوع الملف</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for type_dossier in types_dossier %}
                    <option value="{{ type_dossier.id }}" 
                            {% if type_filter == type_dossier.id %}selected{% endif %}>
                        {{ type_dossier.nom }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="{{ url_for('clients.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إلغاء
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Results -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الموكلين ({{ clients.total }} موكل)
        </h5>
    </div>
    <div class="card-body">
        {% if clients.items %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>الاسم الكامل</th>
                            <th>معلومات الاتصال</th>
                            <th>نوع الملف</th>
                            <th>عدد الملفات</th>
                            <th>تاريخ الإضافة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients.items %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle me-2">
                                        {{ client.nom_complet[0] }}
                                    </div>
                                    <div>
                                        <strong>{{ client.nom_complet }}</strong>
                                        {% if client.profession %}
                                        <br><small class="text-muted">{{ client.profession }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if client.telephone %}
                                <div><i class="fas fa-phone me-1"></i>{{ client.telephone }}</div>
                                {% endif %}
                                {% if client.email %}
                                <div><i class="fas fa-envelope me-1"></i>{{ client.email }}</div>
                                {% endif %}
                                {% if client.cin %}
                                <div><small class="text-muted">ب.و: {{ client.cin }}</small></div>
                                {% endif %}
                            </td>
                            <td>
                                {% if client.type_dossier %}
                                <span class="badge" style="background-color: {{ client.type_dossier.couleur }}">
                                    {{ client.type_dossier.nom }}
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ client.get_dossiers_count() }}</span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ client.created_at.strftime('%d/%m/%Y') if client.created_at else '' }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('clients.view', id=client.id) }}" 
                                       class="btn btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    {% if current_user.has_permission('update') %}
                                    <a href="{{ url_for('clients.edit', id=client.id) }}" 
                                       class="btn btn-outline-warning" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% endif %}
                                    {% if current_user.has_permission('delete') %}
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmDelete({{ client.id }}, '{{ client.nom_complet }}')" 
                                            title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if clients.pages > 1 %}
            <nav aria-label="تصفح الصفحات">
                <ul class="pagination justify-content-center">
                    {% if clients.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('clients.index', page=clients.prev_num, search=search, type=type_filter) }}">
                            السابق
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in clients.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != clients.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('clients.index', page=page_num, search=search, type=type_filter) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if clients.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('clients.index', page=clients.next_num, search=search, type=type_filter) }}">
                            التالي
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد موكلين</h5>
                {% if search or type_filter %}
                <p class="text-muted">لم يتم العثور على نتائج تطابق معايير البحث</p>
                <a href="{{ url_for('clients.index') }}" class="btn btn-outline-primary">
                    عرض جميع الموكلين
                </a>
                {% else %}
                <p class="text-muted">ابدأ بإضافة موكل جديد</p>
                {% if current_user.has_permission('create') %}
                <a href="{{ url_for('clients.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    إضافة موكل جديد
                </a>
                {% endif %}
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموكل <strong id="clientName"></strong>؟</p>
                <p class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(clientId, clientName) {
    document.getElementById('clientName').textContent = clientName;
    document.getElementById('deleteForm').action = `/clients/${clientId}/delete`;
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}
</script>
{% endblock %}
