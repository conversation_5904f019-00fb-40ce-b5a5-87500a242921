# -*- coding: utf-8 -*-
"""
مسارات لوحة التحكم
"""

from flask import render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from app.models.client import Client
from app.models.dossier import Dossier
from app.models.audience import Audience
from app.models.type_dossier import TypeDossier
from app.models.journal_actions import JournalActions
from app.blueprints.dashboard import bp

@bp.route('/')
@bp.route('/index')
@login_required
def index():
    """الصفحة الرئيسية"""

    # إحصائيات عامة
    stats = {
        'total_clients': Client.query.filter_by(is_active=True).count(),
        'total_dossiers': Dossier.query.filter_by(is_archived=False).count(),
        'active_dossiers': Dossier.query.filter(
            Dossier.is_archived == False,
            Dossier.situation.in_(['في الجلسات', 'في انتظار التعيين'])
        ).count(),
        'today_audiences': Audience.query.filter_by(
            date_audience=date.today(),
            statut='مبرمجة'
        ).count()
    }

    # جلسات اليوم
    today_audiences = Audience.get_today_audiences()

    # الجلسات القادمة (خلال أسبوع)
    upcoming_audiences = Audience.get_upcoming_audiences(days=7)

    # آخر الملفات المضافة
    recent_dossiers = Dossier.query.filter_by(is_archived=False).order_by(
        Dossier.created_at.desc()
    ).limit(5).all()

    # آخر الموكلين المضافين
    recent_clients = Client.query.filter_by(is_active=True).order_by(
        Client.created_at.desc()
    ).limit(5).all()

    # إحصائيات حسب نوع الملف
    type_stats = []
    for type_dossier in TypeDossier.get_active_types():
        type_stats.append({
            'nom': type_dossier.nom,
            'couleur': type_dossier.couleur,
            'dossiers_count': type_dossier.get_active_dossiers_count(),
            'clients_count': type_dossier.get_clients_count()
        })

    # إحصائيات حسب المحكمة
    tribunal_stats = []
    tribunaux = ['ابتدائية', 'استئناف', 'نقض', 'تجارية', 'إدارية']
    for tribunal in tribunaux:
        count = Dossier.query.filter_by(tribunal=tribunal, is_archived=False).count()
        if count > 0:
            tribunal_stats.append({
                'nom': tribunal,
                'count': count
            })

    return render_template('dashboard/index.html',
                         title='لوحة التحكم',
                         stats=stats,
                         today_audiences=today_audiences,
                         upcoming_audiences=upcoming_audiences,
                         recent_dossiers=recent_dossiers,
                         recent_clients=recent_clients,
                         type_stats=type_stats,
                         tribunal_stats=tribunal_stats)

@bp.route('/api/stats')
@login_required
def api_stats():
    """API للإحصائيات"""

    # إحصائيات شهرية للملفات
    monthly_dossiers = []
    for i in range(12):
        month_start = date.today().replace(day=1) - timedelta(days=30*i)
        month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        count = Dossier.query.filter(
            Dossier.date_ouverture.between(month_start, month_end)
        ).count()

        monthly_dossiers.append({
            'month': month_start.strftime('%Y-%m'),
            'count': count
        })

    monthly_dossiers.reverse()

    # إحصائيات الجلسات القادمة
    upcoming_stats = []
    for i in range(7):
        target_date = date.today() + timedelta(days=i)
        count = Audience.query.filter_by(
            date_audience=target_date,
            statut='مبرمجة'
        ).count()

        upcoming_stats.append({
            'date': target_date.isoformat(),
            'count': count,
            'day_name': target_date.strftime('%A')
        })

    return jsonify({
        'monthly_dossiers': monthly_dossiers,
        'upcoming_audiences': upcoming_stats
    })

@bp.route('/search')
@login_required
def search():
    """البحث العام"""
    query = request.args.get('q', '').strip()

    if not query:
        return render_template('dashboard/search.html', title='البحث', query=query)

    # البحث في الموكلين
    clients = Client.search(query).limit(10).all()

    # البحث في الملفات
    dossiers = Dossier.search(query).limit(10).all()

    # البحث في الجلسات
    from app import db
    audiences = Audience.query.filter(
        db.or_(
            Audience.client_nom.contains(query),
            Audience.numero_dossier.contains(query),
            Audience.demande.contains(query)
        )
    ).limit(10).all()

    results = {
        'clients': clients,
        'dossiers': dossiers,
        'audiences': audiences,
        'total': len(clients) + len(dossiers) + len(audiences)
    }

    return render_template('dashboard/search.html',
                         title=f'نتائج البحث: {query}',
                         query=query,
                         results=results)

@bp.route('/notifications')
@login_required
def notifications():
    """التنبيهات"""

    # جلسات اليوم
    today_audiences = Audience.get_today_audiences()

    # جلسات الغد
    tomorrow = date.today() + timedelta(days=1)
    tomorrow_audiences = Audience.query.filter_by(
        date_audience=tomorrow,
        statut='مبرمجة'
    ).order_by(Audience.heure_audience.asc()).all()

    # الملفات المستعجلة
    urgent_dossiers = Dossier.query.filter_by(
        is_urgent=True,
        is_archived=False
    ).order_by(Dossier.updated_at.desc()).all()

    # آخر الإجراءات المستخرجة
    recent_actions = JournalActions.get_recent_extractions(hours=48)

    notifications = {
        'today_audiences': today_audiences,
        'tomorrow_audiences': tomorrow_audiences,
        'urgent_dossiers': urgent_dossiers,
        'recent_actions': recent_actions
    }

    return render_template('dashboard/notifications.html',
                         title='التنبيهات',
                         notifications=notifications)

@bp.route('/api/notifications')
@login_required
def api_notifications():
    """API للحصول على التنبيهات"""
    from app.utils.notifications import get_user_notifications, get_notification_count

    notifications = get_user_notifications(current_user.id)
    count = get_notification_count(current_user.id)

    return jsonify({
        'notifications': notifications,
        'count': count,
        'unread_count': count  # جميع التنبيهات تعتبر غير مقروءة حالياً
    })
