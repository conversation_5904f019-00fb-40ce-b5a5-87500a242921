{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-edit me-2"></i>
        تعديل الملف: {{ dossier.numero_affaire }}
        {% if dossier.is_urgent %}
        <span class="badge bg-danger ms-2">مستعجل</span>
        {% endif %}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('dossiers.view', id=dossier.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-eye me-1"></i>
                عرض الملف
            </a>
        </div>
        <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    تعديل بيانات الملف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- رقم الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="numero_affaire" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                رقم الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.numero_affaire(class="form-control") }}
                            {% if form.numero_affaire.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.numero_affaire.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- المحكمة -->
                        <div class="col-md-6 mb-3">
                            <label for="tribunal" class="form-label">
                                <i class="fas fa-university me-1"></i>
                                المحكمة <span class="text-danger">*</span>
                            </label>
                            {{ form.tribunal(class="form-select") }}
                            {% if form.tribunal.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tribunal.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- نوع الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="type_dossier_id" class="form-label">
                                <i class="fas fa-tags me-1"></i>
                                نوع الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.type_dossier_id(class="form-select") }}
                            {% if form.type_dossier_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.type_dossier_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الموكل -->
                        <div class="col-md-6 mb-3">
                            <label for="client_id" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الموكل <span class="text-danger">*</span>
                            </label>
                            {{ form.client_id(class="form-select") }}
                            {% if form.client_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.client_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- حالة الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="situation" class="form-label">
                                <i class="fas fa-info-circle me-1"></i>
                                حالة الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.situation(class="form-select") }}
                            {% if form.situation.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.situation.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ فتح الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="date_ouverture" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ فتح الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.date_ouverture(class="form-control") }}
                            {% if form.date_ouverture.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_ouverture.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الطرف المقابل -->
                        <div class="col-md-6 mb-3">
                            <label for="adversaire" class="form-label">
                                <i class="fas fa-user-times me-1"></i>
                                الطرف المقابل
                            </label>
                            {{ form.adversaire(class="form-control") }}
                            {% if form.adversaire.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.adversaire.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ إغلاق الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="date_cloture" class="form-label">
                                <i class="fas fa-calendar-times me-1"></i>
                                تاريخ إغلاق الملف
                            </label>
                            {{ form.date_cloture(class="form-control") }}
                            {% if form.date_cloture.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_cloture.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- موضوع الملف -->
                    <div class="mb-3">
                        <label for="objet" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            موضوع الملف
                        </label>
                        {{ form.objet(class="form-control") }}
                        {% if form.objet.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.objet.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <!-- مبلغ النزاع -->
                        <div class="col-md-6 mb-3">
                            <label for="montant_litige" class="form-label">
                                <i class="fas fa-money-bill me-1"></i>
                                مبلغ النزاع (درهم)
                            </label>
                            {{ form.montant_litige(class="form-control") }}
                            {% if form.montant_litige.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.montant_litige.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الأتعاب -->
                        <div class="col-md-6 mb-3">
                            <label for="honoraires" class="form-label">
                                <i class="fas fa-coins me-1"></i>
                                الأتعاب (درهم)
                            </label>
                            {{ form.honoraires(class="form-control") }}
                            {% if form.honoraires.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.honoraires.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- ملف مستعجل -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_urgent(class="form-check-input") }}
                            <label class="form-check-label" for="is_urgent">
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                ملف مستعجل
                            </label>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('dossiers.view', id=dossier.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {% if current_user.has_permission('delete') %}
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-archive me-1"></i>
                            أرشفة الملف
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات الملف الحالية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الملف الحالية
                </h5>
            </div>
            <div class="card-body">
                <div class="info-list">
                    <div class="info-item">
                        <i class="fas fa-calendar text-primary"></i>
                        <div>
                            <strong>تاريخ الإنشاء</strong>
                            <p>{{ dossier.created_at.strftime('%d/%m/%Y %H:%M') if dossier.created_at else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-calendar-alt text-info"></i>
                        <div>
                            <strong>عدد الجلسات</strong>
                            <p>{{ dossier.get_audiences_count() if hasattr(dossier, 'get_audiences_count') else 0 }} جلسة</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-gavel text-success"></i>
                        <div>
                            <strong>آخر إجراء</strong>
                            <p>
                                {% set last_action = dossier.get_last_action() if hasattr(dossier, 'get_last_action') else None %}
                                {% if last_action %}
                                {{ last_action.date_extraction.strftime('%d/%m/%Y') }}
                                {% else %}
                                لا توجد إجراءات
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إرشادات التعديل -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    إرشادات التعديل
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-1"></i> تنبيهات مهمة:</h6>
                    <ul class="mb-0">
                        <li>تأكد من صحة البيانات قبل الحفظ</li>
                        <li>تغيير رقم الملف قد يؤثر على الإجراءات</li>
                        <li>تغيير الموكل سيؤثر على الجلسات</li>
                        <li>يمكن إلغاء التعديلات في أي وقت</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تحذير:</h6>
                    <p class="mb-0">أرشفة الملف ستخفيه من القائمة الرئيسية ولكن ستحتفظ بجميع البيانات.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الأرشفة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أرشفة الملف <strong>{{ dossier.numero_affaire }}</strong>؟</p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم نقل الملف إلى الأرشيف ولن يظهر في القائمة الرئيسية
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('dossiers.delete', id=dossier.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-warning">أرشفة</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.info-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div {
    flex: 1;
}

.info-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p {
    margin: 0;
    font-weight: 500;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// تنسيق أرقام المبالغ
document.addEventListener('DOMContentLoaded', function() {
    const amountInputs = document.querySelectorAll('input[name="montant_litige"], input[name="honoraires"]');
    amountInputs.forEach(input => {
        input.addEventListener('input', function() {
            let value = this.value.replace(/[^0-9.]/g, '');
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            this.value = value;
        });
    });
});
</script>
{% endblock %}
