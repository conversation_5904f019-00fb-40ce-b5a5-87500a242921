# -*- coding: utf-8 -*-
"""
نموذج التنبيهات
"""

from datetime import datetime, timedelta
from app import db

class Notification(db.Model):
    """نموذج التنبيهات"""
    
    __tablename__ = 'notifications'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # نوع التنبيه
    type = db.Column(db.String(50), nullable=False)  # audience, deadline, response, general
    
    # العنوان والرسالة
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    
    # الأولوية
    priority = db.Column(db.String(20), default='medium')  # low, medium, high, urgent
    
    # الحالة
    is_read = db.Column(db.Boolean, default=False)
    is_sent = db.Column(db.<PERSON>, default=False)
    
    # التوقيت
    scheduled_time = db.Column(db.DateTime, nullable=False)  # وقت إرسال التنبيه
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)
    sent_at = db.Column(db.DateTime)
    
    # المستخدم المستهدف
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # الربط بالكيانات الأخرى
    audience_id = db.Column(db.Integer, db.ForeignKey('audiences.id'), nullable=True)
    dossier_id = db.Column(db.Integer, db.ForeignKey('dossiers.id'), nullable=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.id'), nullable=True)
    
    # إعدادات الإرسال
    send_email = db.Column(db.Boolean, default=False)
    email_sent = db.Column(db.Boolean, default=False)
    
    # العلاقات
    user = db.relationship('User', backref='notifications')
    audience = db.relationship('Audience', backref='notifications')
    dossier = db.relationship('Dossier', backref='notifications')
    client = db.relationship('Client', backref='notifications')
    
    def __repr__(self):
        return f'<Notification {self.title}>'
    
    def mark_as_read(self):
        """تحديد التنبيه كمقروء"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()
    
    def mark_as_sent(self):
        """تحديد التنبيه كمرسل"""
        self.is_sent = True
        self.sent_at = datetime.utcnow()
        db.session.commit()
    
    def get_priority_color(self):
        """لون الأولوية"""
        colors = {
            'low': 'secondary',
            'medium': 'info',
            'high': 'warning',
            'urgent': 'danger'
        }
        return colors.get(self.priority, 'info')
    
    def get_priority_text(self):
        """نص الأولوية"""
        texts = {
            'low': 'منخفضة',
            'medium': 'متوسطة',
            'high': 'عالية',
            'urgent': 'عاجلة'
        }
        return texts.get(self.priority, 'متوسطة')
    
    def get_type_icon(self):
        """أيقونة نوع التنبيه"""
        icons = {
            'audience': 'calendar-alt',
            'deadline': 'clock',
            'response': 'reply',
            'general': 'bell'
        }
        return icons.get(self.type, 'bell')
    
    def get_type_text(self):
        """نص نوع التنبيه"""
        texts = {
            'audience': 'جلسة',
            'deadline': 'موعد نهائي',
            'response': 'رد مطلوب',
            'general': 'عام'
        }
        return texts.get(self.type, 'عام')
    
    def is_overdue(self):
        """هل التنبيه متأخر"""
        return datetime.utcnow() > self.scheduled_time and not self.is_sent
    
    def time_until_scheduled(self):
        """الوقت المتبقي حتى الإرسال"""
        if self.is_sent:
            return None
        
        now = datetime.utcnow()
        if now >= self.scheduled_time:
            return "حان الوقت"
        
        diff = self.scheduled_time - now
        
        if diff.days > 0:
            return f"{diff.days} يوم"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} ساعة"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} دقيقة"
        else:
            return "أقل من دقيقة"

class NotificationTemplate(db.Model):
    """قوالب التنبيهات"""
    
    __tablename__ = 'notification_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # معلومات القالب
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(50), nullable=False)
    
    # محتوى القالب
    title_template = db.Column(db.String(200), nullable=False)
    message_template = db.Column(db.Text, nullable=False)
    
    # إعدادات التوقيت
    hours_before = db.Column(db.Integer, default=24)  # عدد الساعات قبل الحدث
    
    # الحالة
    is_active = db.Column(db.Boolean, default=True)
    
    # التوقيت
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<NotificationTemplate {self.name}>'
    
    def render_title(self, **kwargs):
        """تطبيق القالب على العنوان"""
        return self.title_template.format(**kwargs)
    
    def render_message(self, **kwargs):
        """تطبيق القالب على الرسالة"""
        return self.message_template.format(**kwargs)

class NotificationSettings(db.Model):
    """إعدادات التنبيهات للمستخدمين"""
    
    __tablename__ = 'notification_settings'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # المستخدم
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # إعدادات التنبيهات
    audience_notifications = db.Column(db.Boolean, default=True)
    deadline_notifications = db.Column(db.Boolean, default=True)
    response_notifications = db.Column(db.Boolean, default=True)
    general_notifications = db.Column(db.Boolean, default=True)
    
    # إعدادات البريد الإلكتروني
    email_notifications = db.Column(db.Boolean, default=False)
    email_audience = db.Column(db.Boolean, default=False)
    email_deadline = db.Column(db.Boolean, default=False)
    email_response = db.Column(db.Boolean, default=False)
    
    # إعدادات التوقيت
    audience_hours_before = db.Column(db.Integer, default=24)
    deadline_hours_before = db.Column(db.Integer, default=48)
    response_hours_before = db.Column(db.Integer, default=24)
    
    # التوقيت
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # العلاقات
    user = db.relationship('User', backref='notification_settings')
    
    def __repr__(self):
        return f'<NotificationSettings for {self.user.nom_complet}>'

# دوال مساعدة لإنشاء التنبيهات
def create_audience_notification(audience, hours_before=24):
    """إنشاء تنبيه للجلسة"""
    from app.models.user import User
    
    # حساب وقت الإرسال
    scheduled_time = audience.date_audience - timedelta(hours=hours_before)
    
    # إنشاء التنبيه لجميع المستخدمين المخولين
    users = User.query.filter(User.role.in_(['admin', 'lawyer'])).all()
    
    for user in users:
        # التحقق من إعدادات المستخدم
        settings = NotificationSettings.query.filter_by(user_id=user.id).first()
        if settings and not settings.audience_notifications:
            continue
        
        notification = Notification(
            type='audience',
            title=f'تذكير: جلسة الملف {audience.numero_dossier}',
            message=f'لديك جلسة غداً في {audience.date_audience.strftime("%d/%m/%Y")} الساعة {audience.heure_audience.strftime("%H:%M") if audience.heure_audience else "09:00"}\n'
                   f'الملف: {audience.numero_dossier}\n'
                   f'الموكل: {audience.client_nom}\n'
                   f'المحكمة: {audience.tribunal}',
            priority='high' if audience.is_important else 'medium',
            scheduled_time=scheduled_time,
            user_id=user.id,
            audience_id=audience.id,
            dossier_id=audience.dossier_id,
            send_email=settings.email_audience if settings else False
        )
        
        db.session.add(notification)
    
    db.session.commit()

def create_deadline_notification(dossier, deadline_date, deadline_type, hours_before=48):
    """إنشاء تنبيه للمواعيد النهائية"""
    from app.models.user import User
    
    # حساب وقت الإرسال
    scheduled_time = deadline_date - timedelta(hours=hours_before)
    
    # إنشاء التنبيه لجميع المستخدمين المخولين
    users = User.query.filter(User.role.in_(['admin', 'lawyer'])).all()
    
    for user in users:
        settings = NotificationSettings.query.filter_by(user_id=user.id).first()
        if settings and not settings.deadline_notifications:
            continue
        
        notification = Notification(
            type='deadline',
            title=f'موعد نهائي: {deadline_type}',
            message=f'ينتهي موعد {deadline_type} للملف {dossier.numero_dossier} في {deadline_date.strftime("%d/%m/%Y")}\n'
                   f'الموكل: {dossier.client.nom_complet}\n'
                   f'نوع الملف: {dossier.type_dossier.nom if dossier.type_dossier else "غير محدد"}',
            priority='urgent',
            scheduled_time=scheduled_time,
            user_id=user.id,
            dossier_id=dossier.id,
            client_id=dossier.client_id,
            send_email=settings.email_deadline if settings else False
        )
        
        db.session.add(notification)
    
    db.session.commit()

def create_response_notification(dossier, response_deadline, response_type, hours_before=24):
    """إنشاء تنبيه للردود المطلوبة"""
    from app.models.user import User
    
    # حساب وقت الإرسال
    scheduled_time = response_deadline - timedelta(hours=hours_before)
    
    # إنشاء التنبيه لجميع المستخدمين المخولين
    users = User.query.filter(User.role.in_(['admin', 'lawyer'])).all()
    
    for user in users:
        settings = NotificationSettings.query.filter_by(user_id=user.id).first()
        if settings and not settings.response_notifications:
            continue
        
        notification = Notification(
            type='response',
            title=f'رد مطلوب: {response_type}',
            message=f'مطلوب {response_type} للملف {dossier.numero_dossier} قبل {response_deadline.strftime("%d/%m/%Y")}\n'
                   f'الموكل: {dossier.client.nom_complet}\n'
                   f'يرجى إعداد الرد في الوقت المناسب',
            priority='high',
            scheduled_time=scheduled_time,
            user_id=user.id,
            dossier_id=dossier.id,
            client_id=dossier.client_id,
            send_email=settings.email_response if settings else False
        )
        
        db.session.add(notification)
    
    db.session.commit()
