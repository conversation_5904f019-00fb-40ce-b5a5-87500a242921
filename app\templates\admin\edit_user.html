{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-user-edit me-2"></i>
        تعديل المستخدم: {{ user.nom_complet }}
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if user.id != current_user.id %}
            <button type="button" class="btn btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                    onclick="toggleUserStatus()">
                <i class="fas fa-{{ 'user-slash' if user.is_active else 'user-check' }} me-1"></i>
                {{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}
            </button>
            {% endif %}
            <button type="button" class="btn btn-outline-warning" onclick="resetPassword()">
                <i class="fas fa-key me-1"></i>
                إعادة تعيين كلمة المرور
            </button>
        </div>
        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة لقائمة المستخدمين
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    تعديل بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6 mb-3">
                            <label for="nom_complet" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            {{ form.nom_complet(class="form-control") }}
                            {% if form.nom_complet.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.nom_complet.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-at me-1"></i>
                                اسم المستخدم <span class="text-danger">*</span>
                            </label>
                            {{ form.username(class="form-control") }}
                            {% if form.username.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.username.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">يستخدم لتسجيل الدخول</small>
                        </div>
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>
                            البريد الإلكتروني
                        </label>
                        {{ form.email(class="form-control") }}
                        {% if form.email.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.email.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">اختياري - للإشعارات والتواصل</small>
                    </div>

                    <div class="row">
                        <!-- الدور -->
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">
                                <i class="fas fa-user-tag me-1"></i>
                                الدور <span class="text-danger">*</span>
                            </label>
                            {{ form.role(class="form-select") }}
                            {% if form.role.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.role.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الحالة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label">
                                <i class="fas fa-toggle-on me-1"></i>
                                الحالة
                            </label>
                            <div class="form-check">
                                {{ form.is_active(class="form-check-input") }}
                                <label class="form-check-label" for="is_active">
                                    مستخدم نشط
                                </label>
                            </div>
                            <small class="form-text text-muted">يمكن للمستخدم النشط تسجيل الدخول</small>
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ التعديلات
                        </button>
                        <a href="{{ url_for('admin.users') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        {% if current_user.id != user.id %}
                        <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                            <i class="fas fa-trash me-1"></i>
                            حذف المستخدم
                        </button>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- معلومات المستخدم الحالية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="user-avatar-large mx-auto mb-2">
                        {{ user.nom_complet[0] }}
                    </div>
                    <h6 class="mb-1">{{ user.nom_complet }}</h6>
                    <span class="badge bg-{{ 'primary' if user.role == 'admin' else 'success' if user.role == 'lawyer' else 'info' if user.role == 'assistant' else 'secondary' }}">
                        {% if user.role == 'admin' %}مدير
                        {% elif user.role == 'lawyer' %}محامي
                        {% elif user.role == 'assistant' %}مساعد
                        {% else %}مشاهد{% endif %}
                    </span>
                </div>

                <div class="user-info-list">
                    <div class="info-item">
                        <i class="fas fa-at text-primary"></i>
                        <div>
                            <strong>اسم المستخدم</strong>
                            <p>{{ user.username }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-envelope text-info"></i>
                        <div>
                            <strong>البريد الإلكتروني</strong>
                            <p>{{ user.email or 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-calendar text-success"></i>
                        <div>
                            <strong>تاريخ الإنشاء</strong>
                            <p>{{ user.created_at.strftime('%d/%m/%Y %H:%M') if user.created_at else 'غير محدد' }}</p>
                        </div>
                    </div>

                    <div class="info-item">
                        <i class="fas fa-{{ 'check-circle text-success' if user.is_active else 'times-circle text-danger' }}"></i>
                        <div>
                            <strong>الحالة</strong>
                            <p>{{ 'نشط' if user.is_active else 'غير نشط' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات المستخدم -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات النشاط
                </h5>
            </div>
            <div class="card-body">
                <div class="stats-list">
                    <div class="stat-item">
                        <i class="fas fa-sign-in-alt text-primary"></i>
                        <div>
                            <strong>عدد مرات الدخول</strong>
                            <p>{{ user_stats.login_count or 0 }}</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <i class="fas fa-clock text-info"></i>
                        <div>
                            <strong>آخر دخول</strong>
                            <p>{{ user_stats.last_login or 'لم يسجل دخول بعد' }}</p>
                        </div>
                    </div>

                    <div class="stat-item">
                        <i class="fas fa-edit text-warning"></i>
                        <div>
                            <strong>العمليات المنجزة</strong>
                            <p>{{ user_stats.operations_count or 0 }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الأدوار -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-shield-alt me-2"></i>
                    صلاحيات الدور
                </h5>
            </div>
            <div class="card-body">
                <div class="role-permissions">
                    {% if user.role == 'admin' %}
                    <div class="alert alert-primary">
                        <h6><i class="fas fa-crown me-1"></i> مدير النظام</h6>
                        <ul class="mb-0">
                            <li>صلاحيات كاملة</li>
                            <li>إدارة المستخدمين</li>
                            <li>إعدادات النظام</li>
                            <li>النسخ الاحتياطية</li>
                        </ul>
                    </div>
                    {% elif user.role == 'lawyer' %}
                    <div class="alert alert-success">
                        <h6><i class="fas fa-balance-scale me-1"></i> محامي</h6>
                        <ul class="mb-0">
                            <li>إدارة الملفات</li>
                            <li>إدارة الجلسات</li>
                            <li>إدارة الموكلين</li>
                            <li>التقارير</li>
                        </ul>
                    </div>
                    {% elif user.role == 'assistant' %}
                    <div class="alert alert-info">
                        <h6><i class="fas fa-user-tie me-1"></i> مساعد</h6>
                        <ul class="mb-0">
                            <li>إضافة البيانات</li>
                            <li>تعديل البيانات</li>
                            <li>عرض التقارير</li>
                        </ul>
                    </div>
                    {% else %}
                    <div class="alert alert-secondary">
                        <h6><i class="fas fa-eye me-1"></i> مشاهد</h6>
                        <ul class="mb-0">
                            <li>عرض البيانات فقط</li>
                            <li>لا يمكن التعديل</li>
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toggle Status Modal -->
<div class="modal fade" id="toggleStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ 'إلغاء تفعيل' if user.is_active else 'تفعيل' }} المستخدم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من {{ 'إلغاء تفعيل' if user.is_active else 'تفعيل' }} المستخدم <strong>{{ user.nom_complet }}</strong>؟</p>
                {% if user.is_active %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    سيتم منع المستخدم من تسجيل الدخول
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('admin.toggle_user_status', id=user.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-{{ 'danger' if user.is_active else 'success' }}">
                        {{ 'إلغاء التفعيل' if user.is_active else 'تفعيل' }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>إعادة تعيين كلمة مرور المستخدم <strong>{{ user.nom_complet }}</strong></p>
                <div class="mb-3">
                    <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                    <input type="password" class="form-control" id="new_password" name="new_password" 
                           placeholder="أدخل كلمة المرور الجديدة" required minlength="6">
                    <small class="form-text text-muted">يجب أن تكون 6 أحرف على الأقل</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('admin.reset_user_password', id=user.id) }}" method="POST" style="display: inline;">
                    <input type="hidden" name="new_password" id="hidden_new_password">
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-key me-1"></i>
                        إعادة تعيين
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم <strong>{{ user.nom_complet }}</strong>؟</p>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    هذا الإجراء لا يمكن التراجع عنه وسيؤثر على جميع البيانات المرتبطة
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form action="{{ url_for('admin.delete_user', id=user.id) }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.user-avatar-large {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 2rem;
}

.user-info-list,
.stats-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.info-item,
.stat-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.info-item i,
.stat-item i {
    width: 20px;
    margin-top: 0.25rem;
}

.info-item div,
.stat-item div {
    flex: 1;
}

.info-item strong,
.stat-item strong {
    display: block;
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.info-item p,
.stat-item p {
    margin: 0;
    font-weight: 500;
}

.role-permissions ul {
    padding-left: 1.5rem;
}

.role-permissions li {
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleUserStatus() {
    const modal = new bootstrap.Modal(document.getElementById('toggleStatusModal'));
    modal.show();
}

function resetPassword() {
    document.getElementById('new_password').value = '';
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// نسخ كلمة المرور إلى الحقل المخفي عند الإرسال
document.getElementById('resetPasswordModal').querySelector('form').addEventListener('submit', function() {
    const password = document.getElementById('new_password').value;
    document.getElementById('hidden_new_password').value = password;
});

// توليد كلمة مرور عشوائية
function generateRandomPassword() {
    const length = 8;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";
    
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    
    document.getElementById('new_password').value = password;
    alert(`كلمة المرور المولدة: ${password}\nتأكد من حفظها في مكان آمن`);
}

// إضافة زر توليد كلمة مرور
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('new_password');
    const generateBtn = document.createElement('button');
    generateBtn.type = 'button';
    generateBtn.className = 'btn btn-outline-secondary btn-sm mt-2';
    generateBtn.innerHTML = '<i class="fas fa-random me-1"></i> توليد كلمة مرور';
    generateBtn.onclick = generateRandomPassword;
    
    passwordInput.parentNode.appendChild(generateBtn);
});
</script>
{% endblock %}
