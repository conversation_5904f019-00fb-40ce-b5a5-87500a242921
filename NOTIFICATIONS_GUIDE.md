# 🔔 دليل نظام التنبيهات التلقائية

## نظرة عامة

نظام التنبيهات التلقائية في نظام إدارة مكتب المحامي يوفر تذكيرات ذكية ومجدولة لضمان عدم فوات المواعيد المهمة والمواعيد النهائية الحرجة.

## 🚀 التفعيل السريع

### 1. تحديث قاعدة البيانات
```bash
python update_notifications.py
```

### 2. الوصول للتنبيهات
- افتح المتصفح واذهب إلى: `http://localhost:5001`
- سجل الدخول بحسابك
- انقر على "التنبيهات" في القائمة العلوية

### 3. تخصيص الإعدادات
- اذهب إلى "إعدادات التنبيهات"
- فعّل أنواع التنبيهات المطلوبة
- حدد التوقيت المناسب لكل نوع

## 📋 أنواع التنبيهات

### 📅 تنبيهات الجلسات
**الغرض**: تذكيرك بمواعيد الجلسات القادمة

**المميزات**:
- تنبيه قبل 24 ساعة (افتراضي)
- تنبيه عاجل قبل ساعتين
- معلومات شاملة: رقم الملف، اسم الموكل، المحكمة، التوقيت

**مثال على التنبيه**:
```
🔔 تذكير: جلسة الملف 2024/123/45
لديك جلسة غداً في 15/06/2025 الساعة 10:00
الملف: 2024/123/45
الموكل: أحمد محمد الكريم
المحكمة: ابتدائية الرباط
```

### ⏰ تنبيهات المواعيد النهائية
**الغرض**: تنبيهك قبل انتهاء آجال الاستئناف والطعن

**المميزات**:
- حساب تلقائي لآجال الاستئناف (30 يوم من تاريخ الحكم)
- تنبيه قبل 48 ساعة (افتراضي)
- تنبيهات للطعن والتبليغ

**مثال على التنبيه**:
```
⚠️ موعد نهائي: انتهاء أجل الاستئناف
ينتهي أجل الاستئناف للملف 2024/123/45 في 20/06/2025
الموكل: أحمد محمد الكريم
يرجى اتخاذ الإجراء المناسب
```

### 📝 تنبيهات الردود المطلوبة
**الغرض**: تذكيرك بإعداد الردود على مذكرات الخصوم

**المميزات**:
- كشف تلقائي للجلسات التي تتطلب ردود
- تنبيه قبل 24 ساعة (افتراضي)
- ربط مع الملفات والجلسات

**مثال على التنبيه**:
```
📝 رد مطلوب: مذكرة جوابية
مطلوب إعداد مذكرة جوابية للملف 2024/123/45 قبل 18/06/2025
الموكل: أحمد محمد الكريم
يرجى إعداد الرد في الوقت المناسب
```

## ⚙️ إعدادات التنبيهات

### الوصول للإعدادات
1. اذهب إلى "التنبيهات" → "الإعدادات"
2. أو اذهب مباشرة إلى: `/notifications/settings`

### أنواع الإعدادات

#### تفعيل/إلغاء أنواع التنبيهات
- ✅ تنبيهات الجلسات
- ✅ تنبيهات المواعيد النهائية  
- ✅ تنبيهات الردود المطلوبة
- ✅ التنبيهات العامة

#### تخصيص التوقيت
- **تنبيهات الجلسات**: 1 ساعة إلى 48 ساعة
- **المواعيد النهائية**: 24 ساعة إلى أسبوعين
- **الردود المطلوبة**: 6 ساعات إلى 3 أيام

#### إعدادات البريد الإلكتروني
- تفعيل إشعارات البريد الإلكتروني
- اختيار أنواع التنبيهات للبريد
- تأكد من إدخال بريدك في الملف الشخصي

## 🔄 التشغيل التلقائي

### فحص يدوي
```bash
# فحص واحد للتنبيهات
python notification_scheduler.py --once
```

### تشغيل مستمر
```bash
# تشغيل مجدول مستمر (يفحص كل ساعة)
python notification_scheduler.py
```

### جدولة تلقائية

#### Windows
```cmd
# إنشاء مهمة يومية
schtasks /create /tn "Mohami Notifications" /tr "python C:\path\to\notification_scheduler.py --once" /sc daily /st 08:00
```

#### Linux/Mac
```bash
# إضافة إلى crontab
crontab -e

# إضافة هذا السطر للفحص اليومي في 8:00 صباحاً
0 8 * * * cd /path/to/mohami && python notification_scheduler.py --once

# فحص كل ساعة للتنبيهات العاجلة
0 * * * * cd /path/to/mohami && python -c "from app.utils.notification_service import NotificationService; from app import create_app; app = create_app(); app.app_context().push(); NotificationService.send_pending_notifications()"
```

## 📊 إدارة التنبيهات

### عرض التنبيهات
- **جميع التنبيهات**: `/notifications`
- **غير المقروءة**: فلترة حسب الحالة
- **حسب النوع**: فلترة حسب نوع التنبيه
- **حسب الأولوية**: عاجلة، عالية، متوسطة، منخفضة

### إجراءات التنبيهات
- ✅ **تحديد كمقروء**: تحديد تنبيه واحد أو جميع التنبيهات
- 👁️ **عرض التفاصيل**: عرض التنبيه كاملاً مع الروابط
- 🗑️ **حذف**: حذف التنبيهات غير المرغوبة

### الفلترة والبحث
- **حسب النوع**: جلسات، مواعيد نهائية، ردود، عامة
- **حسب الأولوية**: عاجلة، عالية، متوسطة، منخفضة
- **حسب الحالة**: مقروءة، غير مقروءة، متأخرة

## 🎨 واجهة المستخدم

### مؤشرات بصرية
- 🔴 **عداد التنبيهات**: في القائمة العلوية
- 🟡 **التنبيهات الجديدة**: خلفية مميزة
- 🔴 **التنبيهات المتأخرة**: حدود حمراء
- ⭐ **التنبيهات المهمة**: أيقونات مميزة

### الألوان والأولويات
- 🔴 **عاجلة**: أحمر
- 🟠 **عالية**: برتقالي  
- 🟡 **متوسطة**: أصفر
- 🔵 **منخفضة**: أزرق

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### التنبيهات لا تظهر
1. تأكد من تشغيل `update_notifications.py`
2. تحقق من إعدادات التنبيهات
3. تأكد من وجود جلسات أو ملفات مناسبة

#### التنبيهات لا ترسل تلقائياً
1. تأكد من تشغيل `notification_scheduler.py`
2. تحقق من جدولة المهام في النظام
3. راجع ملف السجلات `notifications.log`

#### مشاكل البريد الإلكتروني
1. تأكد من إعدادات SMTP في `config.py`
2. تحقق من صحة البريد الإلكتروني في الملف الشخصي
3. تأكد من تفعيل إشعارات البريد في الإعدادات

### ملفات السجلات
```bash
# عرض سجلات التنبيهات
tail -f notifications.log

# عرض سجلات التطبيق
tail -f app.log
```

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من ملفات السجلات
3. افتح Issue في GitHub مع تفاصيل المشكلة
4. تواصل مع فريق الدعم

---

**نصيحة**: ابدأ بتفعيل تنبيهات الجلسات أولاً، ثم أضف الأنواع الأخرى تدريجياً حسب احتياجاتك.
