#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
تحديث قاعدة البيانات لإضافة نظام إدارة الوثائق
"""

import os
from app import create_app, db
from app.models.document import Document, DocumentCategory

def update_database():
    """تحديث قاعدة البيانات"""
    
    app = create_app()
    
    with app.app_context():
        print("🔄 تحديث قاعدة البيانات لنظام الوثائق...")
        
        # إنشاء الجداول الجديدة
        db.create_all()
        print("✅ تم إنشاء جداول الوثائق")
        
        # إنشاء التصنيفات الافتراضية
        create_default_categories()
        
        # إنشاء مجلدات الرفع
        create_upload_folders()
        
        db.session.commit()
        print("✅ تم حفظ جميع التغييرات")
        
        print("\n🎉 تم تحديث نظام الوثائق بنجاح!")
        print("💡 يمكنك الآن:")
        print("   - رفع الوثائق من: /documents/upload")
        print("   - إدارة الوثائق من: /documents")
        print("   - إدارة التصنيفات من: /documents/categories")

def create_default_categories():
    """إنشاء التصنيفات الافتراضية"""
    
    default_categories = DocumentCategory.get_default_categories()
    
    for category_data in default_categories:
        existing_category = DocumentCategory.query.filter_by(
            name=category_data['name']
        ).first()
        
        if not existing_category:
            category = DocumentCategory(**category_data)
            db.session.add(category)
            print(f"✅ تم إنشاء تصنيف: {category_data['name_ar']}")
        else:
            print(f"⚠️  التصنيف موجود مسبقاً: {category_data['name_ar']}")

def create_upload_folders():
    """إنشاء مجلدات الرفع"""
    
    upload_folder = 'uploads'
    folders_to_create = [
        'uploads',
        'uploads/documents',
        'uploads/thumbnails',
        'uploads/temp'
    ]
    
    for folder in folders_to_create:
        if not os.path.exists(folder):
            os.makedirs(folder)
            print(f"✅ تم إنشاء مجلد: {folder}")
        else:
            print(f"⚠️  المجلد موجود مسبقاً: {folder}")
    
    # إنشاء ملف .gitkeep للمجلدات الفارغة
    gitkeep_folders = ['uploads/documents', 'uploads/thumbnails', 'uploads/temp']
    for folder in gitkeep_folders:
        gitkeep_path = os.path.join(folder, '.gitkeep')
        if not os.path.exists(gitkeep_path):
            with open(gitkeep_path, 'w') as f:
                f.write('')

def create_sample_documents():
    """إنشاء وثائق تجريبية (اختياري)"""
    
    from app.models.user import User
    from app.models.dossier import Dossier
    
    # الحصول على أول مستخدم ومل
    admin_user = User.query.filter_by(role='admin').first()
    if not admin_user:
        admin_user = User.query.first()
    
    first_dossier = Dossier.query.first()
    
    if admin_user and first_dossier:
        # الحصول على تصنيف الأحكام
        judgment_category = DocumentCategory.query.filter_by(name='judgment').first()
        
        if judgment_category:
            # إنشاء وثيقة تجريبية
            sample_document = Document(
                title='حكم تجريبي - قضية رقم 123/2024',
                description='هذه وثيقة تجريبية لإظهار كيفية عمل نظام إدارة الوثائق',
                filename='sample_judgment.pdf',
                original_filename='حكم_تجريبي.pdf',
                file_path='uploads/documents/sample_judgment.pdf',
                file_size=1024000,  # 1 ميجابايت
                file_type='application/pdf',
                file_extension='.pdf',
                category_id=judgment_category.id,
                dossier_id=first_dossier.id,
                client_id=first_dossier.client_id,
                is_confidential=False,
                is_original=True,
                uploaded_by=admin_user.id
            )
            
            db.session.add(sample_document)
            print(f"✅ تم إنشاء وثيقة تجريبية: {sample_document.title}")

if __name__ == '__main__':
    update_database()
