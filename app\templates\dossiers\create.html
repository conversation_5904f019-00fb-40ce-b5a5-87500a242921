{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-folder-plus me-2"></i>
        إضافة ملف جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('dossiers.index') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-right me-1"></i>
            العودة للقائمة
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-folder me-2"></i>
                    بيانات الملف
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- رقم الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="numero_affaire" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                رقم الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.numero_affaire(class="form-control") }}
                            {% if form.numero_affaire.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.numero_affaire.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">مثال: 2025/1201/89</small>
                        </div>

                        <!-- المحكمة -->
                        <div class="col-md-6 mb-3">
                            <label for="tribunal" class="form-label">
                                <i class="fas fa-university me-1"></i>
                                المحكمة <span class="text-danger">*</span>
                            </label>
                            {{ form.tribunal(class="form-select") }}
                            {% if form.tribunal.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tribunal.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- نوع الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="type_dossier_id" class="form-label">
                                <i class="fas fa-tags me-1"></i>
                                نوع الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.type_dossier_id(class="form-select") }}
                            {% if form.type_dossier_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.type_dossier_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الموكل -->
                        <div class="col-md-6 mb-3">
                            <label for="client_id" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الموكل <span class="text-danger">*</span>
                            </label>
                            {{ form.client_id(class="form-select") }}
                            {% if form.client_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.client_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">
                                <a href="{{ url_for('clients.create') }}" target="_blank">إضافة موكل جديد</a>
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- حالة الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="situation" class="form-label">
                                <i class="fas fa-info-circle me-1"></i>
                                حالة الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.situation(class="form-select") }}
                            {% if form.situation.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.situation.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ فتح الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="date_ouverture" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ فتح الملف <span class="text-danger">*</span>
                            </label>
                            {{ form.date_ouverture(class="form-control") }}
                            {% if form.date_ouverture.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_ouverture.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <!-- الطرف المقابل -->
                        <div class="col-md-6 mb-3">
                            <label for="adversaire" class="form-label">
                                <i class="fas fa-user-times me-1"></i>
                                الطرف المقابل
                            </label>
                            {{ form.adversaire(class="form-control") }}
                            {% if form.adversaire.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.adversaire.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- تاريخ إغلاق الملف -->
                        <div class="col-md-6 mb-3">
                            <label for="date_cloture" class="form-label">
                                <i class="fas fa-calendar-times me-1"></i>
                                تاريخ إغلاق الملف
                            </label>
                            {{ form.date_cloture(class="form-control") }}
                            {% if form.date_cloture.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.date_cloture.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">اتركه فارغاً إذا كان الملف لا يزال مفتوحاً</small>
                        </div>
                    </div>

                    <!-- موضوع الملف -->
                    <div class="mb-3">
                        <label for="objet" class="form-label">
                            <i class="fas fa-file-alt me-1"></i>
                            موضوع الملف
                        </label>
                        {{ form.objet(class="form-control") }}
                        {% if form.objet.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.objet.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <!-- مبلغ النزاع -->
                        <div class="col-md-6 mb-3">
                            <label for="montant_litige" class="form-label">
                                <i class="fas fa-money-bill me-1"></i>
                                مبلغ النزاع (درهم)
                            </label>
                            {{ form.montant_litige(class="form-control") }}
                            {% if form.montant_litige.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.montant_litige.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- الأتعاب -->
                        <div class="col-md-6 mb-3">
                            <label for="honoraires" class="form-label">
                                <i class="fas fa-coins me-1"></i>
                                الأتعاب (درهم)
                            </label>
                            {{ form.honoraires(class="form-control") }}
                            {% if form.honoraires.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.honoraires.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- ملف مستعجل -->
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.is_urgent(class="form-check-input") }}
                            <label class="form-check-label" for="is_urgent">
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                ملف مستعجل
                            </label>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">
                            <i class="fas fa-sticky-note me-1"></i>
                            ملاحظات
                        </label>
                        {{ form.notes(class="form-control") }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            حفظ الملف
                        </button>
                        <a href="{{ url_for('dossiers.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    إرشادات
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-1"></i> نصائح مهمة:</h6>
                    <ul class="mb-0">
                        <li>الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة</li>
                        <li>تأكد من صحة رقم الملف</li>
                        <li>اختر المحكمة المناسبة</li>
                        <li>حدد نوع الملف بدقة</li>
                        <li>يمكن تعديل البيانات لاحقاً</li>
                    </ul>
                </div>

                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-1"></i> تنبيه:</h6>
                    <p class="mb-0">تأكد من دقة البيانات المدخلة حيث سيتم استخدامها في جميع التقارير والمراسلات.</p>
                </div>

                <div class="alert alert-success">
                    <h6><i class="fas fa-magic me-1"></i> ميزة خاصة:</h6>
                    <p class="mb-0">بعد إنشاء الملف، يمكنك استخراج آخر الإجراءات تلقائياً من موقع المحاكم المغربية.</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث قائمة الموكلين عند تغيير نوع الملف
document.addEventListener('DOMContentLoaded', function() {
    const typeSelect = document.getElementById('type_dossier_id');
    const clientSelect = document.getElementById('client_id');
    
    // تعيين التاريخ الحالي كافتراضي
    const dateInput = document.getElementById('date_ouverture');
    if (!dateInput.value) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.value = today;
    }
    
    // تنسيق أرقام المبالغ
    const amountInputs = document.querySelectorAll('input[name="montant_litige"], input[name="honoraires"]');
    amountInputs.forEach(input => {
        input.addEventListener('input', function() {
            // إزالة كل شيء عدا الأرقام والنقطة
            let value = this.value.replace(/[^0-9.]/g, '');
            
            // التأكد من وجود نقطة واحدة فقط
            const parts = value.split('.');
            if (parts.length > 2) {
                value = parts[0] + '.' + parts.slice(1).join('');
            }
            
            this.value = value;
        });
    });
});
</script>
{% endblock %}
